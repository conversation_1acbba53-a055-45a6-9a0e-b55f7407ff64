package com.magnamedia.mastersearch;

import java.lang.reflect.Field;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Mar 9, 2018
 */

public class SearchField{
    private final int order;
    private final String fieldName;
    private final String fieldHeader;
    private final boolean searched;
    private final boolean returned;
    private final Field field;

    public SearchField(
            int order, String fieldName, String fieldHeader,
            boolean searched, boolean returned, Field field) {
        this.order = order;
        this.fieldName = fieldName;
        this.fieldHeader = fieldHeader;
        this.searched = searched;
        this.returned = returned;
        this.field = field;
    }

    public int getOrder() {
        return order;
    }

    public String getFieldName() {
        return fieldName;
    }

    public String getFieldHeader() {
        return fieldHeader;
    }

    public boolean isSearched() {
        return searched;
    }

    public boolean isReturned() {
        return returned;
    }

    public Field getField() {
        return field;
    }

}
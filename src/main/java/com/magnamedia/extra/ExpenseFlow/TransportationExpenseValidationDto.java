package com.magnamedia.extra.ExpenseFlow;

public class TransportationExpenseValidationDto {
    private Double difference;
    private Boolean enableSendRequest ;
    private Boolean showAuditingSection ;
    public TransportationExpenseValidationDto(Double difference, Boolean enableSendRequest, Boolean showAuditingSection) {
        this.difference = difference;
        this.enableSendRequest = enableSendRequest;
        this.showAuditingSection = showAuditingSection;
    }

    public Double getDifference() {
        return difference;
    }

    public void setDifference(Double difference) {
        this.difference = difference;
    }

    public Boolean getRequiresAttachment() {
        return enableSendRequest;
    }

    public void setRequiresAttachment(Boolean requiresAttachment) {
        this.enableSendRequest = requiresAttachment;
    }

    public Boolean getEnableSendRequest() { return enableSendRequest; }

    public void setEnableSendRequest(Boolean enableSendRequest) {
        this.enableSendRequest = enableSendRequest;
    }

    public Boolean getShowAuditingSection() { return showAuditingSection; }

    public void setShowAuditingSection(Boolean showAuditingSection) {
        this.showAuditingSection = showAuditingSection;
    }
}

package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.core.mail.TextEmail;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 07, 2020
 */

public class ExceptionUtils {
    private static final Logger logger = Logger.getLogger(ExceptionUtils.class.getSimpleName());
    private static final String prefix = "MMM ";

    public static String getStackTrace(Throwable e) {
        return org.apache.commons.lang3.exception.ExceptionUtils.getStackTrace(e);
    }

    public static void printStackTrace(Throwable e) {
        logger.severe(prefix + "Exception Occurred");
        logger.severe(getStackTrace(e));
    }
}

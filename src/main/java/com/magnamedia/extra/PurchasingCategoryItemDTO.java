package com.magnamedia.extra;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.entity.Category;
import com.magnamedia.entity.Item;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.CategoryRepository;
import com.magnamedia.repository.ItemRepository;

import java.util.List;


/**
 * Created by Mamon.Masod on 05/31/2021.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PurchasingCategoryItemDTO {
    private Long categoryId;
    private String categoryName;
    private Long itemId;
    private String itemName;
    private String measureOfConsumption;
    private Double consumptionRate;
    private String unit;
    private String cycle;


    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    @JsonIgnore
    public Category getCategory() {
        if (StringUtils.isEmpty(categoryName)) return null;

        return Setup.getRepository(CategoryRepository.class).findFirstByName(categoryName);
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getItemName() {
        return itemName;
    }

    @JsonIgnore
    public Item getItem() {
        if (StringUtils.isEmpty(itemName)) return null;

        Category category = getCategory();
        if (category == null) return null;

        return Setup.getRepository(ItemRepository.class).findFirstByNameAndCategory(itemName, category);
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getMeasureOfConsumption() {
        return measureOfConsumption;
    }

    @JsonIgnore
    public PicklistItem getMeasureOfConsumptionAsPickListItem() {
        if (measureOfConsumption == null || measureOfConsumption.isEmpty()) return null;

        List<PicklistItem> picklistItems = Setup.getRepository(PicklistRepository.class).findByCode(AccountingModule.ITEM_MEASURE_OF_CONSUMPTION).getItems();

        for (PicklistItem item : picklistItems) {
            if (item.getName().equalsIgnoreCase(measureOfConsumption)) {
                return item;
            }
        }

        return null;
    }

    public void setMeasureOfConsumption(String measureOfConsumption) {
        this.measureOfConsumption = measureOfConsumption;
    }

    public Double getConsumptionRate() {
        return consumptionRate;
    }

    public void setConsumptionRate(Double consumptionRate) {
        this.consumptionRate = consumptionRate;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getCycle() {
        return cycle;
    }

    @JsonIgnore
    public PicklistItem getCycleAsPickListItem() {
        if (cycle == null || cycle.isEmpty()) return null;

        List<PicklistItem> picklistItems = Setup.getRepository(PicklistRepository.class).findByCode(AccountingModule.PICKLIST_CATEGORY_ORDER_CYCLE).getItems();

        for (PicklistItem item : picklistItems) {
            if (item.getName().equalsIgnoreCase(cycle)) {
                return item;
            }
        }

        return null;
    }

    public void setCycle(String cycle) {
        this.cycle = cycle;
    }
}

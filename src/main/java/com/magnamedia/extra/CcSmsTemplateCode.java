package com.magnamedia.extra;

public enum CcSmsTemplateCode {
    CC_PAYMENT_EXPIRY_4_1_1_SMS,
    CC_PAYMENT_EXPIRY_4_1_2_SMS,
    
    CC_ACCOUNTING_PAY_VIA_CREDIT_CARD_SMS,

    CC_SIGN_DD_MESSAGE_SMS,
    
    CC_DD_PENDING_INFO_SMS,
    
    CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_SMS,

    CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_SMS, //row 8 "If we receive an amount from the client when he doesn't owe us any amount (IF client has DD) if he already cancelled"
    CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_SMS, //row 9 "If we receive an amount from the client when he doesn't owe us any amount (IF client has DD),  IF his contract is “scheduled for termination” AND we don’t need that amount"
    
    CC_PAYTAB_THANKS_MESSAGE_SMS,
    
    CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_5_SMS,
    CC_PAYMENT_RECEIVED_8_1_5_SMS,
    CC_PAYMENT_UNDER_PROCESSING_8_1_5_SMS,
    
    CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_6_SMS,
    CC_PAYMENT_RECEIVED_8_1_6_SMS,
    CC_PAYMENT_UNDER_PROCESSING_8_1_6_SMS,
    
    CC_ACCOUNTING_OWE_MONEY_TO_CLIENT_8_1_1_SMS,

    //ACC-4905
    CC_PAYMENT_REMINDER_THANKS_MESSAGE_SMS,
    CC_SIGNING_OFFER_PAYMENT_RECEIVED_THANKS_MESSAGE_SMS,

    // ACC-4591
    CC_ACCOUNTING_PAY_ACCOMMODATION_FEE_SMS,
    CC_ACCOUNTING_PAY_CC_TO_MV_SMS,
    CC_ACCOUNTING_PAY_MONTHLY_PAYMENT_SMS,
    CC_ACCOUNTING_PAY_OVERSTAY_FEES_SMS,
    CC_ACCOUNTING_PAY_PCR_TEST_SMS,
    CC_ACCOUNTING_PAY_URGENT_VISA_CHARGES_SMS,
    CC_ACCOUNTING_PAY_INSURANCE_SMS,
    CC_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_SMS,
}

package com.magnamedia.extra.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Bucket;

import java.io.IOException;

public class IdLabelNameSerializer extends JsonSerializer<Bucket> {

    public IdLabelNameSerializer() {
    }

    public void serialize(
            Bucket value,
            JsonGenerator gen,
            SerializerProvider serializers) throws IOException, JsonProcessingException {

        if (value == null) {
            gen.writeNull();
            return;
        }

        gen.writeStartObject();
        gen.writeNumberField("id", value.getId());
        gen.writeStringField("label", value.getLabel());
        gen.writeStringField("name", value.getName());
        gen.writeEndObject();
    }
}

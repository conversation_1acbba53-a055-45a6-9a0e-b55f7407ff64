package com.magnamedia.extra;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DDMessagingType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public class CreditCardOfferProperties {

    private static final Logger logger = Logger.getLogger(CreditCardOfferProperties.class.getName());
    private int numberBeforeMaxTrialOrReminder;
    private String monthlySmsSentence;
    private String monthlyNotificationSentence;
    private String nonMonthlySmsSentence;
    private String nonMonthlyNotificationSentence;
    private String ctaLabel;
    private List<DDMessagingType> excludedFlows;
    private boolean enableFeature;
    private int xDaysBeforeDdStartDateToScheduledGenerateDds;

    public static CreditCardOfferProperties fill() {
        try {
            Map<String, Object> m = new ObjectMapper().readValue(
                    Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_OFFER_CC_PAYMENT_BEFORE_X_TRIALS_REMINDER),
                    Map.class);

            CreditCardOfferProperties creditCardOfferProperties = new CreditCardOfferProperties();
            creditCardOfferProperties.setNumberBeforeMaxTrialOrReminder(Integer.parseInt((String) m.get("x_number_before_max_trial_or_reminder")));
            creditCardOfferProperties.setxDaysBeforeDdStartDateToScheduledGenerateDds(Integer
                    .parseInt((String) m.get("x_days_before_dd_start_date_to_scheduled_generate_dds")));
            creditCardOfferProperties.setMonthlySmsSentence((String) m.get("monthly_sms_sentence"));
            creditCardOfferProperties.setMonthlyNotificationSentence((String) m.get("monthly_notification_sentence"));
            creditCardOfferProperties.setNonMonthlySmsSentence((String) m.get("non_monthly_sms_sentence"));
            creditCardOfferProperties.setNonMonthlyNotificationSentence((String) m.get("non_monthly_notification_sentence"));
            creditCardOfferProperties.setCtaLabel((String) m.get("cta_label"));

            String excludedFlowsStr = (String) m.get("excluded_flows");
            List<DDMessagingType> excludedFlows = new ArrayList<>();
            if (!excludedFlowsStr.isEmpty()) {
                String[] excludedFlowsArr = excludedFlowsStr.split(",");
                for (String excludedFlow : excludedFlowsArr) {
                    excludedFlows.add(DDMessagingType.valueOf(excludedFlow.trim()));
                }
            }
            creditCardOfferProperties.setExcludedFlows(excludedFlows);

            creditCardOfferProperties.setEnableFeature(Boolean.parseBoolean((String) m.get("enable_feature")));

            return creditCardOfferProperties;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static int getX_DaysBeforeDdStartDate() {
        try {
            Map<String, Object> m = new ObjectMapper().readValue(
                    Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_OFFER_CC_PAYMENT_BEFORE_X_TRIALS_REMINDER),
                    Map.class);

            logger.info( "x_days_before_dd_start_date_to_scheduled_generate_dds: " +
                    m.get("x_days_before_dd_start_date_to_scheduled_generate_dds"));
           return Integer.parseInt((String) m.get("x_days_before_dd_start_date_to_scheduled_generate_dds"));

        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public static boolean isBouncedFlowIncluded() {
        try {
            Map<String, Object> m = new ObjectMapper().readValue(
                    Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_OFFER_CC_PAYMENT_BEFORE_X_TRIALS_REMINDER),
                    Map.class);

            return Boolean.parseBoolean((String) m.get("enable_feature")) &&
                    !((String) m.get("excluded_flows")).contains(DDMessagingType.BouncedPayment.toString());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return false;
    }

    public int getNumberBeforeMaxTrialOrReminder() { return numberBeforeMaxTrialOrReminder; }

    public void setNumberBeforeMaxTrialOrReminder(int numberBeforeMaxTrialOrReminder) { this.numberBeforeMaxTrialOrReminder = numberBeforeMaxTrialOrReminder; }

    public String getMonthlySmsSentence() { return monthlySmsSentence; }

    public void setMonthlySmsSentence(String monthlySmsSentence) { this.monthlySmsSentence = monthlySmsSentence; }

    public String getMonthlyNotificationSentence() { return monthlyNotificationSentence; }

    public void setMonthlyNotificationSentence(String monthlyNotificationSentence) { this.monthlyNotificationSentence = monthlyNotificationSentence; }

    public String getNonMonthlySmsSentence() { return nonMonthlySmsSentence; }

    public void setNonMonthlySmsSentence(String nonMonthlySmsSentence) { this.nonMonthlySmsSentence = nonMonthlySmsSentence; }

    public String getNonMonthlyNotificationSentence() { return nonMonthlyNotificationSentence; }

    public void setNonMonthlyNotificationSentence(String nonMonthlyNotificationSentence) { this.nonMonthlyNotificationSentence = nonMonthlyNotificationSentence; }

    public String getCtaLabel() { return ctaLabel; }

    public void setCtaLabel(String ctaLabel) { this.ctaLabel = ctaLabel; }

    public List<DDMessagingType> getExcludedFlows() { return excludedFlows; }

    public void setExcludedFlows(List<DDMessagingType> excludedFlows) { this.excludedFlows = excludedFlows; }

    public boolean isEnableFeature() { return enableFeature; }

    public void setEnableFeature(boolean enableFeature) { this.enableFeature = enableFeature; }

    public int getxDaysBeforeDdStartDateToScheduledGenerateDds() { return xDaysBeforeDdStartDateToScheduledGenerateDds; }

    public void setxDaysBeforeDdStartDateToScheduledGenerateDds(int xDaysBeforeDdStartDateToScheduledGenerateDds) {
        this.xDaysBeforeDdStartDateToScheduledGenerateDds = xDaysBeforeDdStartDateToScheduledGenerateDds;
    }
}

package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.entity.*;

import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractType;
import com.magnamedia.module.type.HousemaidLiveplace;
import java.util.*;

import org.joda.time.DateTimeConstants;

/**
 *
 * <AUTHOR>
 */
public class PaymentUtil {

    public static final Double VACATION_DAY_COST = 70.0;
    public static PicklistItemRepository picklistItemRepository;

    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT = "Monthly Payment";


    public static double getDefaultFeesLongTerm() {
        return Double.parseDouble(Setup.getParameter(Setup.getModule("clientmgmt"),
                AccountingModule.PARAMETER_DEFAULT_FEES_LONG_TERM));
    }

    public static double getDefaultFeesShortTerm() {
        return Double.parseDouble(Setup.getParameter(Setup.getModule("clientmgmt"),
                AccountingModule.PARAMETER_DEFAULT_FEES_SHORT_TERM));
    }

    public static double getDefaultFeesShortTermEthiopian() {
        return Double.parseDouble(Setup.getParameter(Setup.getModule("clientmgmt"),
                AccountingModule.PARAMETER_DEFAULT_FEES_SHORT_TERM_NEW_CONTRACTS_ETHIOPIAN));
    }

    public static double getDefaultFeesShortTermKenyanType2() {
        return Double.parseDouble(Setup.getParameter(Setup.getModule("clientmgmt"),
                AccountingModule.PARAMETER_DEFAULT_FEES_KENYAN_SHORT_TERM_TYPE2));
    }

    public static double getDefaultEthiopianFee() {
        return Double.parseDouble(Setup.getParameter(Setup.getModule("clientmgmt"),
                AccountingModule.PARAMETER_DEFAULT_FEES_ETHIOPIAN));
    }


    public static double getDefaultKenyanFeeType2() {
        return Double.parseDouble(Setup.getParameter(Setup.getModule("clientmgmt"),
                AccountingModule.PARAMETER_DEFAULT_FEES_KENYAN_TYPE2));
    }

    public static double getDefaultLiveOutFees() {
        return Double.parseDouble(Setup.getParameter(Setup.getModule("clientmgmt"),
                AccountingModule.PARAMETER_DEFAULT_LIVE_OUT_FEES));
    }

    public static Double getCurrentMonthlyFee(Contract contract) {

        return getCurrentMonthlyFeeV2(contract);
    }

    public static Double getCurrentMonthlyFeeV2(Contract contract) {
        PicklistItem ethiopian = Setup.getApplicationContext().getBean(PicklistItemRepository.class).
                findByListAndCodeIgnoreCase(Setup.getApplicationContext().getBean(PicklistRepository.class).findByCode("nationalities"),
                        "ethiopian");
        PicklistItem kenyan = Setup.getApplicationContext().getBean(PicklistItemRepository.class).
                findByListAndCodeIgnoreCase(Setup.getApplicationContext().getBean(PicklistRepository.class).findByCode("nationalities"),
                        "kenyan");
        if (contract.getHousemaid() != null) {
            if (contract.getLiving() == HousemaidLiveplace.IN && contract.getHousemaid().getNationality().getId().equals(ethiopian.getId())) {
                return getEthiopianLiveInMonthlyFee(contract);
            }
        } else {
            SelectQuery<Replacement> query = new SelectQuery<>(Replacement.class);
            query.filterBy("contract.id",
                    "=",
                    contract.getId());
            query.filterBy("oldHousemaid",
                    "IS NOT NULL",
                    null);
            query.sortBy("creationDate", false);
            List<Replacement> replacements = query.execute();
            if (replacements.size() > 0 && replacements.get(0).getOldHousemaid().getNationality().getId().equals(ethiopian.getId())) {
                return getEthiopianLiveInMonthlyFee(contract);
            }
            else if(replacements.size() > 0 && replacements.get(0).getOldHousemaid().getNationality().getId().equals(kenyan.getId()))
                return getKenyanLiveInMonthlyFeeType2(contract);
        }

//        if (contract.getLiving() == HousemaidLiveplace.IN &&contract.getNationality().getId().equals(ethiopian.getId())) {
//            return getEthiopianLiveInMonthlyFee(contract);
//        }
        if (contract.getLiving() == HousemaidLiveplace.IN) {
            return getLiveInMonthlyFee(contract);
        }
        return getLiveOutMonthlyFee(contract);
    }

    public static Double getLiveInMonthlyFee(Contract contract) {
        PicklistItem contractNationality = contract.getNationality();

        if (picklistItemRepository == null) {
            picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);
        }
        //PicklistItem ethiopianItem = picklistItemRepository.findByListAndCodeIgnoreCase(Setup.getRepository(PicklistRepository.class).findByCode(Picklist.NATIONALITIES), PicklistItem.getCode("ethiopian"));

//        if (contract.getNationality() != null && contract.getNationality().getId().equals(ethiopianItem.getId())) {
//            if (contract.getContractType().equals(ContractType.LONG_TERM)) {
//                return getDefaultEthiopianFee();
//            }
//
//        }
        if (contract.getContractType() == ContractType.SHORT_TERM) {
            if (contract.getDefaultST() != null) {
                return contract.getDefaultST();
            }
            return getDefaultFeesShortTerm();
        }

        return getDefaultFeesLongTerm();
    }

    public static Double getLiveOutMonthlyFee(Contract contract) {
        Double amount = getLiveInMonthlyFee(contract);
        Double liveOutFees = contract.getLiveOutFee();
        if (liveOutFees == null) {
            liveOutFees = getDefaultLiveOutFees();
        }
        return amount + liveOutFees;

    }

    public static Double getEthiopianLiveInMonthlyFee(Contract contract) {
        if (contract.getContractType() == ContractType.LONG_TERM) {
            return contract.getEthiopianLiveInMonthlyFee() != null ? contract.getEthiopianLiveInMonthlyFee() : getDefaultEthiopianFee();
        }
        return getDefaultFeesShortTermEthiopian();
    }

    public static Double getKenyanLiveInMonthlyFeeType2(Contract contract) {
        if (contract.getContractType() == ContractType.LONG_TERM) {
            return getDefaultKenyanFeeType2();
        }
        return getDefaultFeesShortTermKenyanType2();
    }

    public static final Map<String, Integer> weekendMap = new HashMap<String, Integer>();

    static {
        weekendMap.put("FRIDAY", DateTimeConstants.FRIDAY);
        weekendMap.put("SATURDAY", DateTimeConstants.SATURDAY);
        weekendMap.put("SUNDAY", DateTimeConstants.SUNDAY);
        weekendMap.put("MONDAY", DateTimeConstants.MONDAY);
        weekendMap.put("TUESDAY", DateTimeConstants.TUESDAY);
        weekendMap.put("WEDNESDAY", DateTimeConstants.WEDNESDAY);
        weekendMap.put("THURSDAY", DateTimeConstants.THURSDAY);

//        reasons.put("End-of-Contract", TerminationMode.NON_RENEWAL);
    }
}
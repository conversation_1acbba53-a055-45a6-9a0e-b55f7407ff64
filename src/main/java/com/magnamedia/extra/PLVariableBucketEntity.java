package com.magnamedia.extra;

import com.magnamedia.entity.BasePLVariableBucket;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Oct 21, 2018
 */
public class PLVariableBucketEntity{
        
        private final Long id;
        private final Long variableId;
        private final Long comapnyId;
        private final String companyName;
        private final String mainLevel;
        private final String subLevel;
        private final String variable;
        private final Double weight;
        private final String type;
        
        public PLVariableBucketEntity(BasePLVariableBucket pLVariableBucket){
            this.id = pLVariableBucket.getId();
            this.variableId = pLVariableBucket.getpLVariable().getId();
            this.comapnyId = pLVariableBucket.getpLVariable().getPLCompany().getId();
            this.companyName = pLVariableBucket.getpLVariable().getPLCompany().getName();
            this.mainLevel = pLVariableBucket.getpLVariable().getParent().getParent().getName();
            this.subLevel = pLVariableBucket.getpLVariable().getParent().getName();
            this.variable = pLVariableBucket.getpLVariable().getName();
            this.weight = pLVariableBucket.getWieght();
            this.type = pLVariableBucket.getpLVariable().getpLNodeType().getValue();
        }

        public Long getId() {
            return id;
        }

        public Long getVariableId() {
            return variableId;
        }

        public Long getComapnyId() {
            return comapnyId;
        }

        public String getCompanyName() {
            return companyName;
        }

        public String getMainLevel() {
            return mainLevel;
        }

        public String getSubLevel() {
            return subLevel;
        }

        public String getVariable() {
            return variable;
        }

        public Double getWeight() {
            return weight;
        }

        public String getType() {
            return type;
        }
        
    }

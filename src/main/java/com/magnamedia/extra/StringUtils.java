package com.magnamedia.extra;

import com.magnamedia.entity.Client;
import com.magnamedia.entity.Housemaid;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 10, 2020
 */

public class StringUtils {

    public static String vowels = "aeiou";

    public static boolean startsWithVowel(String text) {
        return vowels.indexOf(Character.toLowerCase(text.charAt(0))) != -1;
    }

    public static String toCamelCase(String input) {
        if (input == null || input.isEmpty() || isCamelCase(input)) {
            return input;
        }

        // Split by common delimiters: underscore, hyphen, and space
        String[] words = input.split("[_\\-\\s]+");
        if (words.length == 1) {
            return input.toLowerCase();
        }

        StringBuilder camelCase = new StringBuilder(words[0].toLowerCase());

        for (int i = 1; i < words.length; i++) {
            if (!words[i].isEmpty()) {
                camelCase.append(Character.toUpperCase(words[i].charAt(0)));
                if (words[i].length() > 1) {
                    camelCase.append(words[i].substring(1).toLowerCase());
                }
            }
        }

        return camelCase.toString();
    }

    private static boolean isCamelCase(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        // Check if string contains delimiters
        if (str.contains("_") || str.contains("-") || str.contains(" ")) {
            return false;
        }

        // Check if first character is lowercase
        if (!Character.isLowerCase(str.charAt(0))) {
            return false;
        }

        // Check for consecutive uppercase letters
        for (int i = 1; i < str.length() - 1; i++) {
            if (Character.isUpperCase(str.charAt(i)) && Character.isUpperCase(str.charAt(i + 1))) {
                return false;
            }
        }

        return true;
    }

    public static boolean isEmpty(String str) {
        return str == null || "".equals(str.trim());
    }

    public static String getHousemaidNationality(String name) {

        return (startsWithVowel(name) ? "an " : "a ") + name;
    }

    public static String getClientNicknameOrName(Client client) {

        return getClientNicknameOrName(client, true);
    }

    public static String getClientNicknameOrName(Client client, boolean withTitle) {

        return client != null ?
                ((withTitle && client.getTitle() != null ? client.getTitle().getName() + ". " : "") +
                        (client.getNickName() != null && !client.getNickName().isEmpty() ?
                                client.getNickName() :
                                client.getName() != null && !client.getName().isEmpty() ?
                                        client.getName() :
                                        "")):
                "";
    }

    public static String getClientNicknameOrFirstName(Client client) {

        return getClientNicknameOrFirstName(client, true);
    }

    public static String getClientNicknameOrFirstName(Client client, boolean withTitle) {

        return client != null ?
                client.getNickName() != null && !client.getNickName().isEmpty() ?
                        client.getNickName(withTitle) :
                        client.getName() != null && !client.getName().isEmpty() ?
                                client.getFirstName(withTitle) :
                                "":
                "";
    }

    public static String NormalizePhoneNumber(String number) {

        String NormalizedNumber = number;
        NormalizedNumber = NormalizedNumber.replace("-", "");
        NormalizedNumber = NormalizedNumber.replace("+", "");
        NormalizedNumber = NormalizedNumber.replace(")", "");
        NormalizedNumber = NormalizedNumber.replace("(", "");
        NormalizedNumber = NormalizedNumber.replace(" ", "");

        NormalizedNumber = removeFirst(NormalizedNumber, "00");

        if (NormalizedNumber.startsWith("0")) {
            NormalizedNumber = removeFirst(NormalizedNumber, "0");
            if (!NormalizedNumber.startsWith("971")) {
                NormalizedNumber = "971" + NormalizedNumber;
            }
        }
        if (NormalizedNumber.startsWith("9710")) {
            NormalizedNumber = removeFirst(NormalizedNumber, "9710");
            NormalizedNumber = "971" + NormalizedNumber;
        }
        return NormalizedNumber;
    }

    public static String removeFirst(String s, String toRemove) {
        if (s.startsWith(toRemove)) {
            s = s.substring(toRemove.length());
        }
        return s;
    }

    public static String getHousemaidNationalityOrLiveStatus(Housemaid housemaid, boolean switchSameNationality) {

        return switchSameNationality ?
                    "a " + (housemaid.getLiveOut() ? "live-out" : "live-in") :
                getHousemaidNationality(housemaid.getNationality().getName()) +
                            (housemaid.getLiveOut() ? " - live out " : "");
    }
}

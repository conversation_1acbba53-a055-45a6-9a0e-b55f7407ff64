package com.magnamedia.businessrule;


import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.*;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import com.magnamedia.service.ContractService;

import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */

// Form CM ACC-6103
@BusinessRule(moduleCode = "", entity = DirectDebit.class,
        events = {BusinessEvent.AfterCreate , BusinessEvent.AfterUpdate}, fields = {"id", "status", "MStatus", "type","bank.id","contractPaymentTerm.id",
        "contractPaymentTerm.contract.id", "payments.amount", "oecSalary",
        "payments.paymentType.code",  "payments.paymentType.id","payments.includeWorkerSalary", "payments.subType.id",
        "payments.date", "payments.replaceOf.id" , "payments.isProRated","payments.discountAmount" ,"payments.affectsPaidEndDate","payments.vatPaidByClient", "generateRelatedPayments","category"})
public class DDConfirmedByDataEntryBR implements BusinessAction<DirectDebit> {
    Logger logger = Logger.getLogger(DDConfirmedByDataEntryBR.class.getName());

    @Override
    public boolean validate(DirectDebit directDebit, BusinessEvent be) {
        logger.info("DD confirmed by Data entry Validation dd id " + (directDebit.getId() == null ? "NULL" : directDebit.getId()));

        List<Payment> payments =  Setup.getRepository(PaymentRepository.class).findByDirectDebitId(directDebit.getId());
        return payments.isEmpty() && directDebit.getStatus() != null && directDebit.getMStatus() != null &&
                directDebit.getStatus().equals(DirectDebitStatus.PENDING) &&
                (directDebit.getMStatus().equals(DirectDebitStatus.PENDING)  ||
                        (directDebit.getCategory() != null && directDebit.getCategory().equals(DirectDebitCategory.B) &&
                        directDebit.getMStatus().equals(DirectDebitStatus.NOT_APPLICABLE)));
    }

    @Override
    public Map<String, Object> execute(DirectDebit entity, BusinessEvent be) {
        logger.info("DD confirmed by Data entry execute dd id " + (entity.getId() == null ? "NULL" : entity.getId()));

        if (entity == null || !entity.isGenerateRelatedPayments()) return null;
        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", true);

        entity.closeRelatedVoiceResolverTodos();
        entity.createRelatedPayments(PaymentStatus.PRE_PDP , null);
        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", false);
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .updatePaidEndDate(entity.getContractPaymentTerm().getContract());
        return null;
    }
}
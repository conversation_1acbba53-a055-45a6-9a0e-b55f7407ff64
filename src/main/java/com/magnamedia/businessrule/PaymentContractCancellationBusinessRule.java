package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.scheduledjobs.AccountingModuleMainJob;
import com.magnamedia.service.PaymentService;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Feb 9, 2019
 *         ACC-1135
 */

// cancellation flow and refund flows
@BusinessRule(moduleCode = "", entity = Payment.class,
        events = {BusinessEvent.AfterUpdate, BusinessEvent.AfterCreate},
        fields = {"id", "status", "contract.id", "amountOfPayment", "dateOfPayment", "clientInformedAboutRefund", "bouncedInCancellationWaitingPeriod",
                "typeOfPayment.id", "methodOfPayment", "directDebit.id", "requiredForUnfitToWork", "requiredForBouncing", "isInitial", "isProRated", "replacementFor.id",
                "pCCBrChecked", "replaced"})
public class PaymentContractCancellationBusinessRule implements BusinessAction<Payment> {

    private static final Logger logger = Logger.getLogger(PaymentContractCancellationBusinessRule.class.getName());

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "PaymentContractCancellationBusinessRule Validation.");
        logger.log(Level.SEVERE, "payment id: " + entity.getId());

        if (entity.ispCCBrChecked()) return false;

        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());

        logger.log(Level.SEVERE, "Status: " + contract.getStatus());
        logger.log(Level.SEVERE, "Scheduled date of termination: " + contract.getScheduledDateOfTermination());
        logger.log(Level.SEVERE, "Scheduled For Termination: " + contract.getIsScheduledForTermination());

        // we owes refund to the client
        PicklistItem monthlyPayment = PicklistHelper.getItem(
                AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                AccountingModule.PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT);

        if (event.equals(BusinessEvent.AfterUpdate)) {
            HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
            historyQuery.filterBy("id", "=", entity.getId());
            historyQuery.sortBy("lastModificationDate", false, true);
            historyQuery.setLimit(1);
            
            List<Payment> oldPayments = historyQuery.execute();
            Payment old = oldPayments.isEmpty() ? null : oldPayments.get(0);
            
            if(old == null) {
                logger.log(Level.SEVERE, "oldPayments is null or empty ");
                return false;
            }
            
            logger.log(Level.SEVERE, "payment status: " + entity.getStatus() + "; old: " + old.getStatus());
            
            if (old.getBouncedInCancellationWaitingPeriod() && !entity.getBouncedInCancellationWaitingPeriod()) {
                logger.log(Level.SEVERE, "coming from reschedule or undo termination business rules: " + contract.getIsScheduledForTermination());
            }
            
            return ((entity.getStatus().equals(PaymentStatus.BOUNCED) && !old.getStatus().equals(PaymentStatus.BOUNCED) &&
                    !(old.getBouncedInCancellationWaitingPeriod() && !entity.getBouncedInCancellationWaitingPeriod()))
                    || (entity.getStatus().equals(PaymentStatus.RECEIVED) && !old.getStatus().equals(PaymentStatus.RECEIVED)))
                    && entity.getTypeOfPayment().getId().equals(monthlyPayment.getId())
                    && (contract.getStatus() != null
                            && Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED).contains(contract.getStatus()));

        } else {
            logger.log(Level.SEVERE, "payment status: " + entity.getStatus() + " old: new payment");

            return Arrays.asList(PaymentStatus.BOUNCED, PaymentStatus.RECEIVED)
                            .contains(entity.getStatus()) &&
                    entity.getTypeOfPayment().getId().equals(monthlyPayment.getId()) &&
                    (contract.getStatus() != null && Arrays.asList(
                            ContractStatus.CANCELLED, ContractStatus.EXPIRED)
                                    .contains(contract.getStatus()));
        }
    }

    @Override
    public Map<String, Object> execute(Payment entity, BusinessEvent even) {
        Map<String, Object> map = new HashMap<>();
        logger.log(Level.SEVERE, "Execute.");
        map.put("pCCBrChecked", true);

        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());
        PaymentService paymentService = Setup.getApplicationContext()
                .getBean(PaymentService.class);

        if (contract.isMaidVisa()) {
            map.putAll(paymentService.maidVisaFlow(entity, contract));
        } else {
            if ((PaymentStatus.RECEIVED.equals(entity.getStatus()))) {
                AccountingEntityPropertyRepository accountingEntityPropertyRepository =
                        Setup.getRepository(AccountingEntityPropertyRepository.class);
                if (!accountingEntityPropertyRepository.existsByOriginAndKeys(
                        entity, Collections.singletonList(AccountingModuleMainJob.DELAYED_REFUND))) {
                    AccountingEntityProperty property = new AccountingEntityProperty();
                    property.setKey(AccountingModuleMainJob.DELAYED_REFUND);
                    property.setOrigin(entity);
                    property.setPurpose("maidCCFlow");
                    accountingEntityPropertyRepository.save(property);
                }
            } else {
                map.putAll(paymentService.maidCCFlow(entity, contract));
            }
        }

        logger.log(Level.SEVERE, "Execute end.");
        return map;
    }
}

package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.repository.ClientRefundTodoRepository;
import com.magnamedia.service.ClientRefundService;
import com.magnamedia.workflow.type.ClientRefundStatus;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@BusinessRule(moduleCode = "", entity = ClientRefundToDo.class,
        events = {BusinessEvent.AfterUpdate},
        fields = {"id", "status", "contract.id"})
public class AccClientRefundTodoBusinessRule implements BusinessAction<ClientRefundToDo>{
    private static final Logger logger = Logger.getLogger(AccClientRefundTodoBusinessRule.class.getName());

    @Override
    public boolean validate(ClientRefundToDo entity, BusinessEvent event) {

        HistorySelectQuery<ClientRefundToDo> historyQuery = new HistorySelectQuery(ClientRefundToDo.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<ClientRefundToDo> oldToDos = historyQuery.execute();

        if (oldToDos.isEmpty())  return false;


        return !oldToDos.get(0).getStatus().equals(ClientRefundStatus.REJECTED) &&
                entity.getStatus().equals(ClientRefundStatus.REJECTED) &&
                oldToDos.get(0).getParent() == null;
    }

    @Override
    public Map<String, Object> execute(ClientRefundToDo entity, BusinessEvent event) {

        try {
            logger.info("entity id: " + entity.getId());
            Setup.getApplicationContext()
                    .getBean(ClientRefundService.class)
                    .sendNotificationToClient(entity);

        } catch (Exception ex) {
            logger.log(Level.SEVERE, "an error occured when executing the business rule", ex);
        }
        return null;
    }
}

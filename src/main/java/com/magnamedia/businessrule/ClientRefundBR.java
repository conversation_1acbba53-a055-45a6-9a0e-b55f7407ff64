package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.ClientRefundTodoRepository;
import com.magnamedia.service.ClientMessagingAndRefundService;
import com.magnamedia.service.ClientRefundService;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.MessagingService;
import com.magnamedia.workflow.type.ClientRefundPaymentMethod;
import com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Dec 19, 2020
 *         Jirra ACC-2857
 */

@BusinessRule(entity = PayrollAccountantTodo.class, events = {BusinessEvent.AfterUpdate},
        fields = {"id", "ceoAction", "clientRefundToDo.id"})
public class ClientRefundBR implements BusinessAction<PayrollAccountantTodo> {

    private static final Logger logger =
            Logger.getLogger(ClientRefundToDo.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(PayrollAccountantTodo entity, BusinessEvent event) {
        logger.info(prefix + "ClientRefundToDo Validation.");
        logger.info(prefix + "id: " + entity.getId());
        logger.info(prefix + "cooAction: " + entity.getCeoAction());

        if (entity.getClientRefundToDo() == null || entity.getClientRefundToDo().getId() == null) {
            logger.info(prefix + "Client Refund is NULL");
            return false;
        }

        HistorySelectQuery<PayrollAccountantTodo> historyQuery = new HistorySelectQuery(PayrollAccountantTodo.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("ceoAction");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<PayrollAccountantTodo> oldToDos = historyQuery.execute();

        if (oldToDos == null || oldToDos.isEmpty()) {
            logger.info(prefix + "New Record");
            return false;
        }

        PayrollAccountantTodo oldToDo = oldToDos.get(0);

        return (oldToDo.getCeoAction() == null || !oldToDo.getCeoAction().equals(PayrollAccountantTodoManagerAction.APPROVED)) &&
                entity.getCeoAction().equals(PayrollAccountantTodoManagerAction.APPROVED);
    }
    
    @Override
    public Map<String, Object> execute(PayrollAccountantTodo entity, BusinessEvent even) {
        logger.info(prefix + "ClientRefundToDo Execution.");
        ClientRefundTodoRepository clientRefundTodoRepository = Setup.getRepository(ClientRefundTodoRepository.class);
        
        String templateName = null;
        String smsType = null;
        ClientRefundToDo clientRefundToDo = clientRefundTodoRepository.findOne(entity.getClientRefundToDo().getId());
        if (clientRefundToDo == null) {
            logger.severe("Client Refund Not Found");
            return null;
        }

        // ACC-7120 ACC-4326
        Setup.getApplicationContext()
                .getBean(ClientRefundService.class)
                .markPayrollAsPaidAndRefunded(clientRefundToDo);

        if (clientRefundToDo.getMethodOfPayment().equals(ClientRefundPaymentMethod.MONEY_TRANSFER)) {
            templateName = "CLIENT_REFUND_MONEY_TRANSFER_DETAILS";
            smsType = "Client Refund Money Transfer Paid";
        } else if (clientRefundToDo.getMethodOfPayment().equals(ClientRefundPaymentMethod.BANK_TRANSFER)) {
            if (clientRefundToDo.getContract().isMaidCc()) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("templateOriginalName", "CLIENT_REFUND_BANK_TRANSFER_DETAILS");
                map.put("payrollAccountantTodoId", entity.getId());
                map.put("amount", String.valueOf(clientRefundToDo.getAmount().intValue()));
                map.put("contract", clientRefundToDo.getContract());
                MessageTemplateService.messageTemplateSendNotification(map);
                
                return null;
            }
            templateName = "CLIENT_REFUND_BANK_TRANSFER_DETAILS";
            smsType = "Client Refund Bank Transfer Paid";
        }
        
        if (templateName == null) return null;

        Client client = clientRefundToDo.getClient();
        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendClientSms(clientRefundToDo.getContract(),
                        TemplateUtil.getTemplate(templateName),
                        getParameters(templateName, entity.getId(), clientRefundToDo),
                        new HashMap<>(),
                        client.getNormalizedMobileNumber(),
                        client.getNormalizedWhatsappNumber(),
                        client.getId(),
                        client.getEntityType());

        return null;
    }
    
    private Map<String, String> getParameters(String templateName, Long toDoId, ClientRefundToDo entity) {
        Map<String, String> params = new HashMap();
        params.put("amount", entity.getAmount().toString());
        
        if (templateName.equalsIgnoreCase("CLIENT_REFUND_MONEY_TRANSFER_DETAILS")) {
            return params;
        }

        params.putAll(Setup.getApplicationContext()
                .getBean(ClientRefundService.class)
                .getProofTransferLink(toDoId));

        String agency = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_NAME);
        params.put("agency", agency);
        
        return params;
    }
}

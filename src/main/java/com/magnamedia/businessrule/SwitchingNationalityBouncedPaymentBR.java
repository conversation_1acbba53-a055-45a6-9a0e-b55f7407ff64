package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.SwitchingNationalityService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.magnamedia.helper.PicklistHelper.getItem;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 24, 2020
 *         Jirra ACC-2800
 *  
 */

// handle by switch nationality flow
@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate},
        fields = {"id", "amountOfPayment", "typeOfPayment.id", "dateOfPayment", "methodOfPayment",
                "directDebit.id", "status", "replaced", "isProRated", "contract.id",
                "sNBPBrChecked"})
public class SwitchingNationalityBouncedPaymentBR implements BusinessAction<Payment> {

    private static final Logger logger =
            Logger.getLogger(SwitchingNationalityBouncedPaymentBR.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, prefix + "SwitchingNationalityBouncedPaymentBR Validation.");

        logger.log(Level.SEVERE, prefix + "payment id: " + (entity.isNewInstance() ? "new instance" : entity.getId()));

        if (entity.issNBPBrChecked()) return false;

        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<Payment> oldPayments = historyQuery.execute();
        Payment old = null;

        if (oldPayments != null && !oldPayments.isEmpty()) {
            old = oldPayments.get(0);
            logger.log(Level.SEVERE, prefix + "old payment founded");
        }

        if (entity.getStatus() == null || !entity.getStatus().equals(PaymentStatus.BOUNCED)) {
            logger.log(Level.SEVERE, prefix + "status: " + entity.getStatus());
            return false;
        }

        if (entity.getDirectDebit() == null || entity.getDirectDebit().getId() == null) {
            logger.log(Level.SEVERE, prefix + "Direct Debit IS NULL");
            return false;
        }

        if (entity.getContract() == null || entity.getContract().getId() == null) {
            logger.log(Level.SEVERE, prefix + "Contract IS NULL");
            return false;
        }

        PicklistItem monthlyPayment = getItem(AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                AccountingModule.PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT);

        SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);
        if (!switchingNationalityService.relatesToSwitching(entity.getDirectDebit().getId(), true) ||
                !switchingNationalityService.doesPaymentCoverSwitchingPeriod(entity.getId())) {
            logger.log(Level.SEVERE, "doesn't relate Switching");
            return false;
        }

        return (old == null || old.getStatus() == null || !old.getStatus().equals(PaymentStatus.BOUNCED)) &&
                entity.getTypeOfPayment() != null && entity.getTypeOfPayment().getId() != null &&
                entity.getTypeOfPayment().getId().equals(monthlyPayment.getId()) &&
                entity.getMethodOfPayment() != null && entity.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT);
    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "RetractContractTerminationReplacedBPBR Execution.");
        Map map = new HashMap();
        map.put("sNBPBrChecked", true);

        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);

        DirectDebit directDebit = directDebitRepository.findOne(entity.getDirectDebit().getId());
        SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);

        switchingNationalityService.handleUpgradingNationalityRequiredBouncedPayment(directDebit, entity, true);
        return map;
    }
}
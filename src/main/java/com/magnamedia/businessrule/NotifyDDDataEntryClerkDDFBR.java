package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.NotificationType;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.notification.NotificationService;
import com.magnamedia.core.repository.NotificationTypeRepository;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.MessagingService;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jun 15, 2020
 *         Jirra ACC-2059
 */

@BusinessRule(moduleCode = "accounting", entity = DirectDebitFile.class, events = {BusinessEvent.AfterCreate, BusinessEvent.BeforeUpdate},
        fields = {"id", "directDebit.id", "ddStatus", "ddDataEntryNotificationSent", "needAccountantReConfirmation"})
public class NotifyDDDataEntryClerkDDFBR implements BusinessAction<DirectDebitFile> {

    private static final Logger logger =
            Logger.getLogger(NotifyDDDataEntryClerkDDFBR.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(DirectDebitFile entity, BusinessEvent event) {
        logger.log(Level.SEVERE, prefix + "NotifyDDDataEntryClerkDDFBR Validation.");
        DirectDebitRepository ddRepository = Setup.getRepository(DirectDebitRepository.class);
        DirectDebitFileRepository ddfRepository = Setup.getRepository(DirectDebitFileRepository.class);

        if (entity.isDdDataEntryNotificationSent()) return false;

        if (entity.getDirectDebit() == null || entity.getDirectDebit().getId() == null) {
            logger.log(Level.SEVERE, prefix + "DD IS NULL, " + entity.getDirectDebit());
            return false;
        }

        DirectDebit dd = ddRepository.findOne(entity.getDirectDebit().getId());

        if (dd == null) {
            logger.log(Level.SEVERE, prefix + "DD Not Found, ID: " + entity.getDirectDebit().getId());
            return false;
        }

        HistorySelectQuery<DirectDebitFile> historyQuery = new HistorySelectQuery(DirectDebitFile.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("ddStatus");
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);

        List<DirectDebitFile> oldDDFs = historyQuery.execute();
        DirectDebitFile old = null;

        if (oldDDFs != null && !oldDDFs.isEmpty()) {
            old = oldDDFs.get(0);
        }

        if (dd.getContractPaymentTerm() == null || dd.getContractPaymentTerm().getContract() == null || dd.getContractPaymentTerm().getContract().getStatus() == null) {
            logger.log(Level.SEVERE, prefix + "CPT or Contract or Contract.Status IS NULL");
            return false;
        }

        logger.log(Level.SEVERE, prefix + "DD Status: " + entity.getDdStatus());
        logger.log(Level.SEVERE, prefix + "Old DD Status: " + (old == null ? "old is NULL" : old.getDdStatus()));
        logger.log(Level.SEVERE, prefix + "Old DD Status: " + (old == null ? "old is NULL" : old.getDdStatus()));

        return (old == null || (old.getDdStatus() != null && !old.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY))) &&
                entity.getDdStatus() != null &&
                entity.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY) &&
                Arrays.asList(ContractStatus.ACTIVE, ContractStatus.PLANNED_RENEWAL).contains(dd.getContractPaymentTerm().getContract().getStatus()) &&
                !ddfRepository.existsByDirectDebit_DdBankInfoGroupAndDdDataEntryNotificationSentAndDdStatus(dd.getDdBankInfoGroup(), true, DirectDebitStatus.PENDING_DATA_ENTRY);
    }

    @Override
    public Map execute(DirectDebitFile entity, BusinessEvent even) {
        logger.log(Level.SEVERE, prefix + "NotifyDDDataEntryClerkDDFBR Execution.");
        Map map = new HashMap();

        NotificationService notificationService = Setup.getApplicationContext().getBean(NotificationService.class);
        NotificationTypeRepository notificationTypeRepository = Setup.getRepository(NotificationTypeRepository.class);

        String pageURL =
                Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL)
                        + "#!/"
                        + "accounting/confirm-dd" + (entity.getNeedAccountantReConfirmation() != null && entity.getNeedAccountantReConfirmation() ?
                        "-accountant/" : "/") + entity.getId();

        String notificationBody = "Hi,\n" +
                "A new \"Data entry\" task has been added, please click ";
        notificationBody += "<a href='" + pageURL + "'>here</a>";
        notificationBody += " and check it out.\n Thanks";

        HashMap<String, String> parameters = new HashMap<>();
        parameters.put("pageURL",pageURL);

        NotificationType notificationType = notificationTypeRepository.findByCode(AccountingModule.DD_DATA_ENTRY_CLERK_NOTIFICATION_TYPE);
        // send notification
        notificationService.sendNotification("New data entry task.", notificationBody, notificationType);

        //send email
        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("new_data_entry_task_mail",
                        parameters, Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_DATA_ENTRY_CLERK_EMAIL),
                        "New data entry task.");

        map.put("ddDataEntryNotificationSent", true);

        return map;
    }
}

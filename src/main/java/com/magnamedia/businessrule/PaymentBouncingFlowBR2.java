package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.DirectDebitSignature;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.BouncingFlowService;
import com.magnamedia.service.DirectDebitSignatureService;
import com.magnamedia.service.SwitchingBankAccountService;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.magnamedia.helper.PicklistHelper.getItem;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Apr 15, 2020
 *         Jirra ACC-1721
 */

// handle by switch bank account flow or call executeBFForAcOrExContracts
@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate},
        fields = {"id", "amountOfPayment", "methodOfPayment", "typeOfPayment.id", "contract.id", "directDebit.id", "dateOfPayment", "status", "trials",
                "isProRated", "requiredForUnfitToWork", "requiredForBouncing", "pBF2BrChecked"})
public class PaymentBouncingFlowBR2 implements BusinessAction<Payment> {

    private static final Logger logger =
            Logger.getLogger(PaymentBouncingFlowBR2.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, prefix + "PaymentBouncingFlowBR2 Validation.");

        if (entity.ispBF2BrChecked()) return false;

        DirectDebitRepository ddRepository = Setup.getRepository(DirectDebitRepository.class);
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);

        logger.log(Level.SEVERE, prefix + prefix + "payment id: " + (entity.isNewInstance() ? "new instance" : entity.getId()));

        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<Payment> oldPayments = historyQuery.execute();
        Payment old = null;

        if (!oldPayments.isEmpty()) {
            old = oldPayments.get(0);
        }

        if (entity.getStatus() == null)
            return false;

        DirectDebit directDebit = null;
        if (entity.getDirectDebit() != null && entity.getDirectDebit().getId() != null) {
            logger.log(Level.SEVERE, prefix + "DD ID: " + entity.getDirectDebit().getId());
            directDebit = ddRepository.findOne(entity.getDirectDebit().getId());
        } else {
            //Jirra ACC-2654, Bug Debugging
            logger.log(Level.SEVERE, prefix + (entity.getDirectDebit() == null ? "DD IS NULL" : "DD.ID IS NULL"));
        }

        if (entity.getContract() == null || entity.getContract().getId() == null) return false;
        Contract contract = contractRepository.findOne(entity.getContract().getId());
        if (contract == null) return false;

        BouncingFlowService bouncingFlowService = Setup.getApplicationContext().getBean(BouncingFlowService.class);
        if (bouncingFlowService.isBouncingFlowStopped(entity)) {
            logger.log(Level.SEVERE, "Bouncing Flow is Stopped");
            return false;
        }

        return !Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED).contains(contract.getStatus()) &&
                entity.getStatus().equals(PaymentStatus.BOUNCED) &&
                (old == null || old.getStatus() == null || !old.getStatus().equals(PaymentStatus.BOUNCED)) &&
                (directDebit == null || (directDebit.getAutoDdfFile() != null &&
                        (directDebit.getManualDdfFile() == null || !directDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) &&
                        directDebit.getCategory().equals(DirectDebitCategory.B)));
    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        logger.log(Level.SEVERE, prefix + "PaymentBouncingFlowBR2 Execution.");
        DirectDebitRepository ddRepository = Setup.getRepository(DirectDebitRepository.class);
        BouncingFlowService bouncingFlowService = Setup.getApplicationContext().getBean(BouncingFlowService.class);
        SwitchingBankAccountService switchingBankAccountService = Setup.getApplicationContext().getBean(SwitchingBankAccountService.class);
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        InterModuleConnector moduleConnector = Setup.getApplicationContext().getBean(InterModuleConnector.class);
        DirectDebitSignatureService directDebitSignatureService = Setup.getApplicationContext().getBean(DirectDebitSignatureService.class);

        Map map = new HashMap();
        map.put("pBF2BrChecked", true);

        Contract contract = contractRepository.findOne(entity.getContract().getId());

        PicklistItem monthlyPayment = getItem(AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                AccountingModule.PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT);

        //Jirra ACC-2790, Scheduled for Termination + Monthly Payment, and don't want money from client
        // MV condition should (!requiredForBouncing) as it's implemented in BFBR1, but not sure about BRs execution order
        if (!contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE)) &&
                contract.getIsScheduledForTermination() != null && contract.getIsScheduledForTermination() &&
                entity.getTypeOfPayment().getId().equals(monthlyPayment.getId()) &&
                !bouncingFlowService.doWeWantMoneyFromClient(contract, false, null)) {
            return map;
        }

        DirectDebit directDebit = null;
        if (entity.getDirectDebit() != null && entity.getDirectDebit().getId() != null) {
            directDebit = ddRepository.findOne(entity.getDirectDebit().getId());
        }

        Map<String, Object> signatureType = directDebitSignatureService
                .getLastSignatureType(contract.getActiveContractPaymentTerm(), true, false);

        List<DirectDebitSignature> signatures = (List<DirectDebitSignature>) signatureType.get("currentSignatures");

        // ACC-2418
        if (directDebit != null && entity.getContract() != null && entity.getContract().getId() != null &&
                switchingBankAccountService.isClientSwitchingBankAccount(entity)) {

            if (switchingBankAccountService.isSwitchingBankAccountDDsCoverPayment(entity)) {
                logger.info("Payment is Covered");

                Payment paymentImage = switchingBankAccountService.getPaymentImage(entity);
                Map paymentMap = new HashMap();
                paymentMap.put("id", paymentImage.getId());
                paymentMap.put("sentToBankByMDD", Boolean.FALSE);

                if (switchingBankAccountService.isSwitchingBankAccountDDsCoverPaymentAndApproved(entity)) {
                    logger.info("New DDs are Approved");
                } else {
                    logger.info("New DDs not Approved");
                    switchingBankAccountService.markNewDDFsAsForBouncingPayment(Arrays.asList(directDebit));
                    paymentMap.put("status", PaymentStatus.BOUNCED.toString());
                }

                logger.log(Level.SEVERE, prefix + "update Payment Async, #" + paymentImage.getId());
                moduleConnector.postJsonAsync("clientmgmt/Payments/update", paymentMap);

                map.put("status", PaymentStatus.DELETED.toString());
            }

            return map;
        }

        map.putAll(bouncingFlowService.paymentGetBouncedStartDirectDebitProcess(entity, signatures, directDebit));
        return map;
    }
}

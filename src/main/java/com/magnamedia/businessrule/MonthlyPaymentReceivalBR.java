package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.Payment;
import com.magnamedia.extra.DDUtils;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.service.SwitchingNationalityService;
import org.joda.time.DateTime;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.magnamedia.helper.PicklistHelper.getItem;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Oct 26, 2020
 *         Jirra ACC-2731
 * 
 */

// handle by switch nationality flow -> cancel dds
@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate},
        fields = {"id", "typeOfPayment.id", "dateOfPayment", "directDebit.id", "status", "replacementFor.id", "isProRated", "contract.id"})
public class MonthlyPaymentReceivalBR implements BusinessAction<Payment> {
    private static final Logger logger =
            Logger.getLogger(MonthlyPaymentReceivalBR.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, prefix + "MonthlyPaymentReceivalBR Validation.");

        logger.log(Level.SEVERE, prefix + prefix + "payment id: " + (entity.isNewInstance() ? "new instance" : entity.getId()));

        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<Payment> oldPayments = historyQuery.execute();
        Payment old = null;

        if (oldPayments != null && !oldPayments.isEmpty()) {
            old = oldPayments.get(0);
            logger.log(Level.SEVERE, prefix + "old payment founded");
        }

        if (entity.getStatus() == null) {
            logger.log(Level.SEVERE, prefix + "STATUS IS NULL");
            return false;
        }

        if (entity.getContract() == null || entity.getContract().getId() == null) {
            logger.log(Level.SEVERE, prefix + "Contract IS NULL");
            return false;
        }

        DirectDebit directDebit = PaymentHelper.getMainReplacedDD(entity);

        logger.log(Level.SEVERE, prefix + prefix + "Direct debit#" + (directDebit != null ? directDebit.getId() : " NULL"));

        PicklistItem monthlyPayment = getItem(AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                AccountingModule.PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT);

        return entity.getStatus().equals(PaymentStatus.RECEIVED) &&
                (old == null || old.getStatus() == null || !old.getStatus().equals(PaymentStatus.RECEIVED)) &&
                directDebit != null &&
                entity.getTypeOfPayment().getId().equals(monthlyPayment.getId());
    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        logger.log(Level.SEVERE, prefix + "MonthlyPaymentReceivalBR Execution.");

        SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);

        DirectDebit directDebit = PaymentHelper.getMainReplacedDD(entity);

        if (switchingNationalityService.relatesToSwitching(directDebit.getId(), false)) {
            logger.log(Level.SEVERE, "relates to switching nationality");
            handleSwitchingNationality(entity, directDebit);
        }

        return null;
    }

    private void handleSwitchingNationality(Payment entity, DirectDebit directDebit) {
        SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);
        ContractPaymentTermRepository cptRepository = Setup.getRepository(ContractPaymentTermRepository.class);

        ContractPaymentTerm cpt = directDebit.getContractPaymentTerm();

        ContractPaymentTerm nextCPT = cptRepository.findFirstByContractAndIdGreaterThanOrderById(cpt.getContract(), cpt.getId());

        DateTime paymentDate = new DateTime(entity.getDateOfPayment());

        if (entity.getReplacementFor() != null && entity.getReplacementFor().getId() != null) {
            Payment replacedPayment = PaymentHelper.getReplacedPayment(entity);
            if (replacedPayment != null) {
                paymentDate = new DateTime(replacedPayment.getDateOfPayment());
            } else {
                logger.log(Level.SEVERE, "no replaced payment found");
            }
        }


        DateTime replacementDate = new DateTime(nextCPT.getSwitchOrReplaceNationalityDate());

        logger.log(Level.SEVERE, "Payment Date: " + paymentDate);
        logger.log(Level.SEVERE, "Replacement Date: " + replacementDate);
        // in case from non-filipino to filipino -> payment may relate to tha same month as replacement month
        if (nextCPT.getSwitchingToPhilipino() != null && nextCPT.getSwitchingToPhilipino()) {
            if (paymentDate.getYear() == replacementDate.getYear() && paymentDate.getMonthOfYear() == replacementDate.getMonthOfYear()) {

                if (!DDUtils.doesDDCoverDate(directDebit, new DateTime(nextCPT.getSwitchOrReplaceNationalityDate()).plusMonths(1).toDate()))
                    switchingNationalityService.processCancellationToDos_fromReceivingPayment(directDebit);
                return;
            }
        }

        if ((paymentDate.getYear() == replacementDate.getYear() && (paymentDate.getMonthOfYear() == (replacementDate.getMonthOfYear() + 1))) ||
                paymentDate.getMonthOfYear() == 1 && replacementDate.getMonthOfYear() == 12 && (paymentDate.getYear() == (replacementDate.getYear() + 1))) {
            switchingNationalityService.processCancellationToDos_fromReceivingPayment(directDebit);
        }
    }
}

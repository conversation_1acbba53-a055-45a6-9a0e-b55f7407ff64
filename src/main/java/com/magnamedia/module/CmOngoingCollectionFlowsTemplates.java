package com.magnamedia.module;

import com.magnamedia.core.entity.Template;
import com.magnamedia.helper.PicklistHelper;

import java.util.HashMap;

public class CmOngoingCollectionFlowsTemplates {

    public enum CmOngoingCollectionFlowsTemplateCode {
        CM_ONGOING_COLLECTION_FLOWS_BOUNCED_PAYMENT,
        CM_ONGOING_COLLECTION_FLOWS_INCOMPLETE_DDS,
        CM_ONGOING_COLLECTION_FLOWS_REJECTED_DDS,
        CM_ONGOING_COLLECTION_FLOWS_IPAM,
        CM_ONGOING_COLLECTION_FLOWS_CLIENT_PAYING_VIA_CC,
        CM_ONGOING_COLLECTION_FLOWS_CLIENT_HAS_TOKENIZED_CARD_PAYING_VIA_CC,
    }

    public static void createTemplates() {
        createBouncedPaymentMessageTemplate();
        createInCompleteDDsMessageTemplate();
        createRejectedDDsMessageTemplate();
        createIPAMMessageTemplate();
        createClientPayingViaCCMessageTemplate();
        createClientHasTokenizedCardPayingViaCCMessageTemplate();
    }

    private static void createBouncedPaymentMessageTemplate() {
        new Template.TemplateBuilder().Template(
                        CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_BOUNCED_PAYMENT.toString(), "","")
                .text("The client's payment from the bank is bounced, meaning the client doesn't have enough funds " +
                        "in his account. The client should add funds to be able to proceed, or we can let them " +
                        "pay by credit card this time only through this link @link_to_pay_via_cc@")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(new HashMap<>())
                .build();
    }

    private static void createInCompleteDDsMessageTemplate() {
        new Template.TemplateBuilder().Template(
                        CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_INCOMPLETE_DDS.toString(), "","")
                .text("The client's monthly bank payment documents are incorrect or missing. " +
                        "The client should upload the correct @missing_info@, which should be uploaded through this link " +
                        "@link_to_upload_documents@")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(new HashMap<>())
                .build();
    }

    private static void createRejectedDDsMessageTemplate() {
        new Template.TemplateBuilder().Template(
                        CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_REJECTED_DDS.toString(), "","")
                .text("The client's monthly bank payment documents were rejected from the bank due " +
                        "@reason_of_rejection@. The client should upload the correct documents through this link " +
                        "@link_to_upload_documents@")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(new HashMap<>())
                .build();
    }

    private static void createIPAMMessageTemplate() {
        new Template.TemplateBuilder().Template(
                        CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_IPAM.toString(), "","")
                .text("The client paid their initial payment via a method other than Monthly Bank Payment. " +
                        "We now need the client to provide us with their Monthly Bank Payment form details, " +
                        "that can be uploaded through this link: @signing_link@")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(new HashMap<>())
                .build();
    }

    private static void createClientPayingViaCCMessageTemplate() {
        new Template.TemplateBuilder().Template(
                        CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_CLIENT_PAYING_VIA_CC.toString(), "","")
                .text("The client pays their payments by credit card instead of using a " +
                        "Monthly Bank Payment form. Hence, the client should either provide us with their " +
                        "Monthly Bank Payment form details through this link: @signing_link@; or the client " +
                        "should pay their upcoming payment through this link: @paying_via_cc_link@")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(new HashMap<>())
                .build();
    }

    private static void createClientHasTokenizedCardPayingViaCCMessageTemplate() {
        new Template.TemplateBuilder().Template(
                        CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_CLIENT_HAS_TOKENIZED_CARD_PAYING_VIA_CC.toString(), "","")
                .text("The client pays their payments by a tokenized credit card instead of using a Monthly Bank Payment form. " +
                        "Hence, the client should either provide us with their Monthly Bank Payment form details through this link: @signing_link@; " +
                        "or the client can keep his tokenized credit card to deduct upcoming payment automatically")
                .method("CMS")
                .target(PicklistHelper.getItem("template_target", "client"))
                .expressionParameters(new HashMap<>())
                .build();
    }
}
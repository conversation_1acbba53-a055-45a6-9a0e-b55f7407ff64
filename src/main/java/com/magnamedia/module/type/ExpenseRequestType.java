package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

public enum ExpenseRequestType implements LabelValueEnum {
    COVID_TEST("Covid 19 Test"),
    COVID_LASER_TEST("Covid 19 Laser Test"),
    MEDICAL("Medical"),
    TAXI_REIMBURSEMENT("Taxi Reimbursement"),
    MAID_PAYMENT("Maid Payment"),
    CASHIER("Cashier"),
    TRANSPORTATION("Trasnsportation"),
    ARAMEX("Aramex"),
    TELECOM("Telecom"),
    DEWA("Dewa"),
    TICKETING("Ticketing"),
    ONE_TIME("One Time"),
    NEW_REQUEST("New Request"),
    VIP("VIP"),
    MAINTENANC<PERSON>("Maintenance"),
    PURCHASING("Purchasing"),
    INSURANCE("Insurance"),
    REPLENISHMENT("Replenishment"),
    AUTO_DEDUCT("Auto deduct");

    private final String label;

    ExpenseRequestType(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 01, 2020
 *         Jirra ACC-1435
 */

public enum ContractPackageType implements LabelValueEnum {
    NORMAL_LONG_TERM("Normal Long Term"),
    TEMPORARY_PACKAGE("Temporary Package"),
    NORMAL_SHORT_TERM("Normal Short Term"),
    PROBATION_PACKAGE("Probation Package"),
    RENEWAL("Renewal");

    private final String label;

    private ContractPackageType(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
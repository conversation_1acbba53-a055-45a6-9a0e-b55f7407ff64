package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR>
 */
public enum CompareOperations implements LabelValueEnum{
    
    EQIAL_TO("=="),
    LESS_THAN("<"),
    GREATER_THAN(">"),
    LESS_THAN_OR_EQIAL_TO("<="),
    GREATER_THAN_OR_EQIAL_TO(">=");

    private final String label;

    CompareOperations(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}

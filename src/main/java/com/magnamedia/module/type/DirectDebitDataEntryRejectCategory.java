package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on July 5, 2020
 * Jirra ACC-2024
 */
public enum DirectDebitDataEntryRejectCategory implements LabelValueEnum {
    WrongEID("Wrong EID"),
    WrongIB<PERSON>("Wrong IBAN"),
    WrongAccountName("Wrong account name"),
    WrongEIDAndIBAN("Wrong EID and IBAN"),
    WrongEIDAndAccountName("Wrong EID and account name"),
    WrongIBANAndAccountName("Wrong IBAN and account name"),
    AllDataIncorrect("All data incorrect");

    private final String label;

    DirectDebitDataEntryRejectCategory(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}



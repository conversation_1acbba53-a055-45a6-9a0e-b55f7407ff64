package com.magnamedia.module.type;

/**
 * <AUTHOR>
 */
public enum DirectDebitRejectionToDoType {

    WAITING_BANK_RESPONSE,
    WAITING_CLIENT_SIGNATURE,
    WAITING_ACCOUNTANT_ACTION,
    WAITING_CLIENT_INFO_DUE_AUTHORIZATION,

    WAITING_BANK_RESPONSE_B,
    WAITING_RE_SCHEDULE_B,

    WAITING_BANK_RESPONSE_B_CASE_D,
    WAITING_CLIENT_SIGNATURE_B_CASE_D,
    WAITING_ACCOUNTANT_ACTION_B_CASE_D,
    WAITING_CLIENT_INFO_DUE_AUTHORIZATION_B_CASE_D,

    WAITING_BANK_RESPONSE_B_BOUNCED,
    WAITING_CLIENT_SIGNATURE_B_BOUNCED,
    WAITING_ACCOUNTANT_ACTION_B_BOUNCED,
    WAITING_FLOW_PAUSE_B_BOUNCED

};

package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR>
 */
public enum ExpenseApproveHolderType implements LabelValueEnum{
    FINAL_MANAGER("Final Manager"),
    USER("User"),
    EMAIL("Email");

    private final String label;

    ExpenseApproveHolderType(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}

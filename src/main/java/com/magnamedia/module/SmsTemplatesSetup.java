package com.magnamedia.module;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.sms.SmsTemplate;
import com.magnamedia.entity.sms.SmsTranslation;
import com.magnamedia.repository.SmsTemplateRepository;
import com.magnamedia.repository.SmsTranslationRepository;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created at Jun 20, 2019
 * <AUTHOR> kanaan <<EMAIL>>
 * Jirra ACC-1092
 */
public class SmsTemplatesSetup {

    public static final String Message_1_1_1 = "Message 1.1.1";
    public static final String Message_1_1_2 = "Message 1.1.2";
    public static final String Message_1_2_1 = "Message 1.2.1";
    public static final String Message_1_2_2 = "Message 1.2.2";
    public static final String Message_1_2_3 = "Message 1.2.3";
    public static final String Message_1_3_1 = "Message 1.3.1";
    public static final String Message_1_3_2 = "Message 1.3.2";
    public static final String Message_1_3_3 = "Message 1.3.3";
    public static final String Message_1_4_1 = "Message 1.4.1";
    public static final String Message_1_4_2 = "Message 1.4.2";
    public static final String Message_1_4_3 = "Message 1.4.3";
    public static final String Message_1_5_1 = "Message 1.5.1";
    public static final String Message_1_5_2 = "Message 1.5.2";
    public static final String Message_3_2 = "Message 3.2";
    public static final String Message_4_1 = "Message 4.1";
    public static final String Message_4_2 = "Message 4.2";
    public static final String Message_4_3 = "Message 4.3";
    public static final String Message_4_4 = "Message 4.4";
    public static final String Message_4_5 = "Message 4.5";
    public static final String Message_4_6_1 = "Message 4.6.1";
    public static final String Message_4_6_2 = "Message 4.6.2";
    public static final String Message_4_7 = "Message 4.7";
    public static final String Message_4_8 = "Message 4.8";

    private SmsTranslationRepository smsTranslationRepository;

    private SmsTemplateRepository smsTemplateRepository;

    public SmsTemplatesSetup() {
        smsTemplateRepository = Setup.getRepository(SmsTemplateRepository.class);
        smsTranslationRepository = Setup.getRepository(SmsTranslationRepository.class);
    }

    public void setup() {
        setupTemplates();
        setuptranslations();
    }

    private void setupTemplates() {
        List<SmsTemplate> templates = getTemplates();
        for (SmsTemplate t : templates) {
            SmsTemplate temp = smsTemplateRepository.findFirst1ByCodeOrderByCreationDateDesc(t.getCode());
            if (temp == null) { 
                smsTemplateRepository.saveAndFlush(t);
            }
        }
    }

    private void setuptranslations() {
        List<SmsTranslation> templates = getTranslations();
        for (SmsTranslation t : templates) {
            if(t.getNationality()==null)
                continue;
            SmsTranslation temp = smsTranslationRepository.findFirst1ByTemplateAndNationality(t.getTemplate(), t.getNationality());
            if (temp == null) {
                smsTranslationRepository.saveAndFlush(t);
            }
        }
    } 

    private List<SmsTemplate> getTemplates() {
        List<SmsTemplate> templates = Arrays.asList(

                 
                
                
                new SmsTemplate(Message_1_1_1,
                        "SCHEDULED_TERMINATION_CONFIRMED",
                        "Clicking Confirm Cancellation on schedule contract termiantion,cancellation reason is client dissatisfaction or no fault",
                        "@greetings@,\n We're sorry to hear that you have decided to discontinue our service. We would like to notify you that Ms @maid_first_name@'s last working day is @scheduled_termination_date@, at 7 PM. However, you can send her to the office any time before that if you wish. For your records.",
                        "","","",
                        Arrays.asList(
                            "@greetings@",
                            "@maid_first_name@",
                            "@scheduled_termination_date@",
                            "@link_cancellation_form_settlement@"
                )),
                new SmsTemplate(Message_1_1_2,
                        "SCHEDULED_TERMINATION_CONFIRMED",
                        "Clicking Confirm Cancellation on schedule contract termiantion, cancellation reason is company dissatisfaction",
                        "@greetings@,\n Your @maid_first_name@'s last working day is @scheduled_termination_date@. She must return to the center at that day, before 7 PM. However, you can send her to the office any time before that if you wish. ",
                        "","","",
                        Arrays.asList(
                            "@greetings@",
                            "@maid_first_name@",
                            "@scheduled_termination_date@",
                            "@link_cancellation_form_settlement@"
                )), 
                
                new SmsTemplate(Message_1_2_1,
                        "SCHEDULED_TERMINATION_IN_5_DAYS",
                        "5 days before Scheduled Termination Date, 10 AM (Contract is still ACTIVE)",
                        "@greetings@,\n Your client's contract ends in 5 days, on @scheduled_termination_date@. We might ask you to come back to the office in a few days. Do not leave before we send you a final confirmation message. Please tell Sir and Madam that we sent them an SMS to their phone number. Click on this link to confirm that you read this message @confirmation_link@. Thank you.",
                        "@greetings@,\n Your client's contract ends in 5 days, on @scheduled_termination_date@. We might ask you to come back to the office in a few days. Do not leave before we send you a final confirmation message. Please tell Sir and Madam that we sent them an SMS to their phone number. Thank you.",
                        "","", 
                        Arrays.asList(
                                "@greetings@",
                                "@scheduled_termination_date@",
                                "@confirmation_link@")),

                new SmsTemplate(Message_1_2_2,
                        "SCHEDULED_TERMINATION_IN_5_DAYS",
                        "5 days before Scheduled Termination Date All Long Term + Short Term where cancellation reason is client or company dissatisfaction",
                        "@greetings@,\n Since your contract will end in 5 days, we've told your maid to return to our center on @scheduled_termination_date@. Please click on the following link @whatsapp_link@ to WhatsApp us if you have any questions or concerns. Thank you.",
                        "","","",
                        Arrays.asList(
                                "@greetings@",
                                "@scheduled_termination_date@",
                                "@whatsapp_link@")),
                
                new SmsTemplate(Message_1_2_3,
                        "SCHEDULED_TERMINATION_IN_5_DAYS",
                        "5 days before Scheduled Termination Date Short Term where cancellation reason is no fault",
                        "@greetings@,\n Since your contract will end in 5 days, we've told your maid @maid_name@ to return to our center on @scheduled_termination_date@. If you'd like to hire her for an additional month, please transfer an amount of  AED @monthly_payment@. Please click on the following link to get transfer details @transfer_details_link@",
                        Arrays.asList(
                                "@greetings@",
                                "@scheduled_termination_date@",
                                "@monthly_payment@",
                                "@transfer_details_link@")),
                
                new SmsTemplate(Message_1_3_1,
                        "SCHEDULED_TERMINATION_IN_1_DAYS",
                        "1 days before Scheduled Termination Date, 10 AM (Contract is still ACTIVE)",
                        "@greetings@, \n Your client's contract ends TOMORROW, on @scheduled_termination_date@. We might ask you to come back to the office TOMORROW.  Do not leave until we send you a final confirmation message. Please tell Sir and Madam that we sent them an SMS to their phone number. Please click on this link to confirm that you read this message @confirmation_link@ \nThank you",
                        "@greetings@, \n Your client's contract ends TOMORROW, on @scheduled_termination_date@. We might ask you to come back to the office TOMORROW.  Do not leave until we send you a final confirmation message. Please tell Sir and Madam that we sent them an SMS to their phone number. \nThank you",
                        "","",
                        Arrays.asList(
                                "@greetings@",
                                "@scheduled_termination_date@",
                                "@confirmation_link@")),

                new SmsTemplate(Message_1_3_2,
                        "SCHEDULED_TERMINATION_IN_1_DAYS",
                        "1 days before Scheduled Termination Date All Long Term + Short Term where cancellation reason is client or company dissatisfaction",
                        "@greetings@,\n Your contract ends Tomorrow @scheduled_termination_date@. Please make sure your maid arrives to our center by 7pm tomorrow. \n\nDubai center:\n@dubai_center_locations@\n\nAbu Dhabi Center:\n@abu_dhabi_center@",
                        "","","",
                        Arrays.asList(
                                "@greetings@",
                                "@scheduled_termination_date@",
                                "@dubai_center_locations@",
                                "@abu_dhabi_center@")),
                
                new SmsTemplate(Message_1_3_3,
                        "SCHEDULED_TERMINATION_IN_1_DAYS",
                        "1 days before Scheduled Termination Date Short Term where cancellation reason is no fault",
                        "@greetings@,\nYour contract ends tomorrow @scheduled_termination_date@. Please make sure your maid arrives to our center by tomorrow 7pm. If you'd like to hire her for an additional month, please transfer an amount of AED @monthly_payment@. Please click on the following link to get the transfer details @transfer_details_link@\n\nDubai center:\n@dubai_center_locations@\n\nAbu Dhabi Center:\n@abu_dhabi_center@",
                        "","","",
                        Arrays.asList(
                                "@greetings@",
                                "@scheduled_termination_date@",
                                "@monthly_payment@",
                                "@transfer_details_link@",
                                "@dubai_center_locations@",
                                "@abu_dhabi_center@")),
                
                new SmsTemplate(Message_1_4_1,
                        "SCHEDULED_TERMINATION_TODAY",
                        "Same day as Scheduled Termination Date Termination Date, 10 AM (Contract is still ACTIVE)",
                        "@greetings@,\n\nYour client's contract ends TODAY. So pack your bags and be ready to leave TODAY. If your client wants to take you back to the office, please go with them. If your client does not want to take you, we'll speak to you later to order you a taxi. Click on this link to confirm that you read this message @confirmation_link@. \n\nThank you. ",
                        "@greetings@,\n\nYour client's contract ends TODAY. So pack your bags and be ready to leave TODAY. If your client wants to take you back to the office, please go with them. If your client does not want to take you, we'll speak to you later to order you a taxi.\n\nThank you. ",
                        "","",
                        Arrays.asList(
                                "@greetings@",
                                "@confirmation_link@")),

                new SmsTemplate(Message_1_4_2,
                        "SCHEDULED_TERMINATION_TODAY",
                        "Same day as Scheduled Termination Date All Long Term + Short Term where cancellation reason is client or company dissatisfaction",
                        "@greetings@,\n\nYour contract ends today @scheduled_termination_date@. Please make sure  to take Ms @maid_name@ to our center before 7pm today.\n\nDubai center:\n@dubai_center_locations@\n\nAbu Dhabi Center:\n@abu_dhabi_center@",
                        "","","",
                        Arrays.asList(
                                "@greetings@",
                                "@scheduled_termination_date@",
                                "@maid_name@",
                                "@dubai_center_locations@",
                                "@abu_dhabi_center@")),
                
                new SmsTemplate(Message_1_4_3,
                        "SCHEDULED_TERMINATION_TODAY",
                        "Same day as Scheduled Termination Date  Short Term where cancellation reason is no fault",
                        "@greetings@,\n\nYour contract ends today. Please make sure  to take Ms @maid_name@ to our center before 7pm today. If you'd like to hire her for an additional month, please transfer an amount AED @monthly_payment@. Click on this link to get transfer details @transfer_details_link@.\n\nDubai center:\n@dubai_center_locations@\n\nAbu Dhabi Center:\n@abu_dhabi_center@",
                        "","","",
                        Arrays.asList(
                                "@greetings@",
                                "@maid_name@",
                                "@monthly_payment@",
                                "@transfer_details_link@",
                                "@dubai_center_locations@",
                                "@abu_dhabi_center@")),
                
                new SmsTemplate(Message_1_5_1,
                        "SCHEDULED_TERMINATION_YESTERDAY",
                        "Next day of Termination Scheduled Date, 10 AM (Maid's status is still with the same client and Contract's status is still ACTIVE) ",
                        "Hello @maid_name@, \nYour client's contract has ended. We just ordered a taxi for you. The taxi will pick you up in 20 minutes. Pack your stuff NOW and wait for the taxi outside. We will send you the details of the taxi in a different message. Please click on this link to confirm that you read this message @confirmation_link@",
                        "Hello @maid_name@, \nYour client's contract has ended. We just ordered a taxi for you. The taxi will pick you up in 20 minutes. Pack your stuff NOW and wait for the taxi outside. We will send you the details of the taxi in a different message.",
                        "","",
                        Arrays.asList(
                                "@maid_name@",
                                "@confirmation_link@")),

                new SmsTemplate(Message_1_5_2,
                        "SCHEDULED_TERMINATION_YESTERDAY",
                        "Next day of Termination Scheduled Date, 10 AM (Maid's status is still with the same client and Contract's status is still ACTIVE) ",
                        "Your contract has expired and your payments are still not settled. We've ordered a taxi for your maid to come back to our center. If you'd like to continue your service, please visit our center today.",
                        "","","",
                        Arrays.asList()
                ),
                
                new SmsTemplate(Message_3_2,
                        "OLD_EXPIRATIOMN",
                        "30 days before OLD LT contract adjusted end date, At 18:00 ",
                        "Your contract will expire on @adjusted_end_date@. To avoid interruptions in your service, please click the link below to renew your contract.\n@link_renewal_details@.",
                        "",
                        "Your contract will expire on @adjusted_end_date@. To avoid interruptions in your service, please click the link below to renew your contract.\n@link_renewal_details@.",
                        "Maids.cc: Renew your contract",
                        Arrays.asList(
                                "@adjusted_end_date@",
                                "@link_renewal_details@")
                ),
                
                new SmsTemplate(Message_4_1,
                        "PAYMENT_IS_BOUNCED",
                        "Whenever a payment has been tagged as BOUNCED",
                        "Your monthly payment was rejected and didn't go through. To avoid a AED@penalty@ penalty, please settle the amount of @amount_of_payment@ before @2_days_after_today_date@, by 6pm. \n\nThe easiest way to settle your payment is by sending a bank transfer to the following account: @transfer_details_link@\n\nOnce the transfer is complete, please send us a screenshot of the proof of transfer on the same link above.",
                        "",
                        "Your monthly payment was rejected and didn't go through. To avoid a AED@penalty@ penalty, please settle the amount of @amount_of_payment@ before @2_days_after_today_date@, by 6pm. \n\nThe easiest way to settle your payment is by sending a bank transfer to the following account: @transfer_details_link@\n\nOnce the transfer is complete, please send us a screenshot of the proof of transfer on the same link above.",
                        "Maids.cc: Your monthly payment didn't go through",
                        Arrays.asList( "@penalty@",
                                "@amount_of_payment@",
                                "@2_days_after_today_date@",
                                "@transfer_details_link@")),
                 
                new SmsTemplate(Message_4_2,
                        "PAYMENT_IS_BOUNCED_YESTERDAY",
                        "Next day of tagging payment as BOUNCED and still BOUNCED (Not Replaced), At 10 AM",
                        "Your amount is still not settled since your previous payment was rejected. To avoid a @penalty@ AED penalty, please settle the amount of @amount_of_payment@ before @tomorrow_date@, by 6pm. Failing to pay may result in the withdrawal of @maid_name@ from your home, and cancellation of your contract.\n\nThe easiest way to settle your payment is by sending a bank transfer to the following account: @transfer_details_link@\n\nOnce the transfer is complete, please send us a screenshot of the proof of transfer on the same link above. ",
                        "",
                        "Your amount is still not settled since your previous payment was rejected. To avoid a @penalty@ AED penalty, please settle the amount of @amount_of_payment@ before @tomorrow_date@, by 6pm. Failing to pay may result in the withdrawal of @maid_name@ from your home, and cancellation of your contract.\n\nThe easiest way to settle your payment is by sending a bank transfer to the following account: @transfer_details_link@\n\nOnce the transfer is complete, please send us a screenshot of the proof of transfer on the same link above. ",
                        "Maids.cc Reminder: your monthly payment didn't go through",
                        Arrays.asList(
                                "@penalty@",
                                "@amount_of_payment@",
                                "@tomorrow_date@",
                                "@maid_name@",
                                "@transfer_details_link@")),
                
                new SmsTemplate(Message_4_3,
                        "PAYMENT_IS_BOUNCED_2_DAYS_AGO",
                        "2 days after tagging payment as BOUNCED and still BOUNCED  (Not Replaced), At 10 AM",
                        "You still did not replace your bounced payment. Please settle the amount of @amount_of_payment@ before Today @today_date@, 6 PM, to avoid a AED @penalty@ penalty. Failing to pay may result in the withdrawal of Ms @maid_name@ from your home, and cancellation of your contract.\n\nThe easiest way to settle your payment is by sending a bank transfer to the following account: @transfer_details_link@\n\nOnce the transfer is complete, please send us a screenshot of the proof of transfer on the same link above. ",
                        "",
                        "You still did not replace your bounced payment. Please settle the amount of @amount_of_payment@ before Today @today_date@, 6 PM, to avoid a AED @penalty@ penalty. Failing to pay may result in the withdrawal of Ms @maid_name@ from your home, and cancellation of your contract.\n\nThe easiest way to settle your payment is by sending a bank transfer to the following account: @transfer_details_link@\n\nOnce the transfer is complete, please send us a screenshot of the proof of transfer on the same link above. ",
                        "Maids.cc Last Reminder: your monthly payment didn't go through",
                        Arrays.asList(
                                "@amount_of_payment@",
                                "@today_date@",
                                "@penalty@",
                                "@maid_name@",
                                "@transfer_details_link@")),
                
                new SmsTemplate(Message_4_4,
                        "PAYMENT_IS_BOUNCED_3_DAYS_AGO",
                        "3 days after tagging payment as BOUNCED and still BOUNCED (Not Replaced), At 10 AM",
                        "You did not settle your payment of @amount_of_payment@ within your grace period. Please note that an additional penalty fee of AED @penalty@ has been added to your total amount. We will allow an additional 2 day grace period for you to settle the total amount. If your payment of AED @paymentwithpenalty@  is not settled before Tomorrow, @4days_after_bounced@ 6:00 PM, we have no choice but to discontinue the service and ask Ms @maid_name@ to come back to our center. \n\nThank you for understanding.",
                        "",
                        "You did not settle your payment of @amount_of_payment@ within your grace period. Please note that an additional penalty fee of AED @penalty@ has been added to your total amount. We will allow an additional 2 day grace period for you to settle the total amount. If your payment of AED @paymentwithpenalty@  is not settled before Tomorrow, @4days_after_bounced@ 6:00 PM, we have no choice but to discontinue the service and ask Ms @maid_name@ to come back to our center. \n\nThank you for understanding.",
                        "Maids.cc Last Reminder: your monthly payment didn't go through",
                        Arrays.asList( 
                                "@amount_of_payment@",
                                "@penalty@",
                                "@paymentwithpenalty@",
                                "@4days_after_bounced@",
                                "@maid_name@",
                                "@transfer_details_link@")),
                
                new SmsTemplate(Message_4_5,
                        "PAYMENT_IS_BOUNCED_4_DAYS_AGO",
                        "4 days after tagging payment as BOUNCED and still BOUNCED (Not Replaced), At 10 AM",
                        "This is a kind reminder that you have to settle your amount of AED @payment_with_penalty@ before TODAY 6:00 PM. Otherwise, we'd have to discontinue the service and ask  @maid_name@ to come back to our center. \n\nThank you for understanding.",
                        "",
                        "This is a kind reminder that you have to settle your amount of AED @payment_with_penalty@ before TODAY 6:00 PM. Otherwise, we'd have to discontinue the service and ask  @maid_name@ to come back to our center. \n\nThank you for understanding.",
                        "Maids.cc Last Reminder: your monthly payment didn't go through",
                        Arrays.asList( 
                                "@payment_with_penalty@",
                                "@maid_name@",
                                "@penalty@",
                                "@transfer_details_link@",
                                "@amount_of_payment@")), 
                
                new SmsTemplate(Message_4_6_1,
                        "PAYMENT_IS_BOUNCED_5_DAYS_AGO",
                        "5 days after tagging payment as BOUNCED and still BOUNCED (Not Replaced), At 10 AM",
                        "Hello @maid_name@, \nYour client's contract has ended. We just ordered a taxi for you. The taxi will pick you up in 20 minutes. Pack your stuff NOW and wait for the taxi outside. We will send you the details of the taxi in a different message. Please click on this link to confirm that you read this message @confirmation_link@",
                        "Hello @maid_name@, \nYour client's contract has ended. We just ordered a taxi for you. The taxi will pick you up in 20 minutes. Pack your stuff NOW and wait for the taxi outside. We will send you the details of the taxi in a different message.",
                        "","",
                        Arrays.asList(
                                "@maid_name@",
                                "@confirmation_link@")),

                new SmsTemplate(Message_4_6_2,
                        "PAYMENT_IS_BOUNCED_5_DAYS_AGO",
                        "5 days after tagging payment as BOUNCED and still BOUNCED (Not Replaced), At 10 AM",
                        "Since you didn't settle the payment of this month, we've just ordered a taxi to pick up @maid_name@ and return her to our center. Please visit our center today to settle your payment and have her return to working in your home.",
                        "",
                        "Since you didn't settle the payment of this month, we've just ordered a taxi to pick up @maid_name@ and return her to our center. Please visit our center today to settle your payment and have her return to working in your home.",
                        "Maids.cc: Contract Cancellation",
                        Arrays.asList(  
                                "@amount_of_payment@",
                                "@penalty@",
                                "@maid_name@",
                                "@transfer_details_link@")),
                
                new SmsTemplate(Message_4_7,
                        "BOUNCED_PAYMENT_IS_REPLACED",
                        "Client replaced his bounced payment",
                        "Transfer well received. For next time, Please ensure there's sufficient amount in the bank account way before the first of every month, to avoid further penalties",
                        "",
                        "\"Transfer well received. For next time, Please ensure there's sufficient amount in the bank account way before the first of every month, to avoid further penalties\",",
                        "Maids.cc: Transfer well received",
                        Arrays.asList()),
                
                new SmsTemplate(Message_4_8,
                        "BIG_CHECK_REMINDER",
                        "3rd of month before the date of a big check, At 10 AM",
                        "Dear @client_name@,\n\nWe're holding a check of yours amounting to AED @big_check_amount@, due on @big_check_due_date@. If you'd like to replace it with monthly payments, please visit our center within the next 10 days to sign a new Bank Direct Debit form.  If you would rather not come and sign, we will go ahead and to deposit your check as planned. If you have any questions, please whatsapp us at @whatsapp_link@",
                        "",
                        "Dear @client_name@,\n\nWe're holding a check of yours amounting to AED @big_check_amount@, due on @big_check_due_date@. If you'd like to replace it with monthly payments, please visit our center within the next 10 days to sign a new Bank Direct Debit form.  If you would rather not come and sign, we will go ahead and to deposit your check as planned. If you have any questions, please whatsapp us at @whatsapp_link@",
                        "Maids.cc: Check Replacement",
                        Arrays.asList(
                                "@client_name@", 
                                "@big_check_amount@",
                                "@big_check_due_date@",
                                "@whatsapp_link@"))

        );
        return templates;
    }

    private List<SmsTranslation> getTranslations() {
        List<SmsTranslation> translations = Arrays.asList(

                new SmsTranslation(Message_1_2_1,
                        "philippines",
                        "@greetings_tg@,\n Ang kontrata ng amo mo ay magtatapos sa loob ng limang araw, sa @scheduled_termination_date@. Paki empake ng iyong bag dahil baka aalis kana sa araw na iyon. Huwag kang aalis hanggang makapag send kami ng huling mensahe ng kompirmasyon. Paki pindot itong link para sa kompirmasyon na nabasa mo ang mensahe na ito @confirmation_link@. Salamat",
                        "@greetings_tg@,\n Ang kontrata ng amo mo ay magtatapos sa loob ng limang araw, sa @scheduled_termination_date@. Paki empake ng iyong bag dahil baka aalis kana sa araw na iyon. Huwag kang aalis hanggang makapag send kami ng huling mensahe ng kompirmasyon. Salamat"
                ),
                
                new SmsTranslation(Message_1_2_1,
                        "ethiopian",
                        "@greetings_am@, የቀጣሪዎ ውል በ5 ቀን ውስጥ ያበቃል፣ በ @scheduled_termination_date@ ።በአጭር ግዜ ውስጥ ወደ ቢሮ እንዲመጡ እናውቃለን።የመጨረሻ የማረጋገጫ መልዕክት ሳንልክ በፊት እንዳይወጡ።መልዕክት ልከን እንደነበር በስልካቸው ለቀጣሪዎችሽ አሳውቂልን።ይህን መልዕክት ማንበቦን ለማረጋገጥ ከታች ያለውን ማስፈንጠሪያ ይጫኑ  @confirmation_link@ እናመሰግናለን።",
                        "@greetings_am@, የቀጣሪዎ ውል በ5 ቀን ውስጥ ያበቃል፣ በ @scheduled_termination_date@ ።በአጭር ግዜ ውስጥ ወደ ቢሮ እንዲመጡ እናውቃለን።የመጨረሻ የማረጋገጫ መልዕክት ሳንልክ በፊት እንዳይወጡ።መልዕክት ልከን እንደነበር በስልካቸው ለቀጣሪዎችሽ አሳውቂልን።"
                ),
                
                new SmsTranslation(Message_1_2_1,
                        "kenyan",
                        "@greetings_sw@,\n Mkataba wa mteja wako unakamilika katika siku 5, tarehe @scheduled _termination_date@. Tunaweza kukuomba kurudi ofisini katika siku chache. Usiondoke kabla tukutumie ujumbe wa uthibitisho wa mwisho. Tafadhali mwambie Sir na Madam kwamba tumewatumia SMS kwa nambari yao ya simu. Bofya kwenye kiungo hiki ili kuthibitisha kwamba umesoma ujumbe huu @confirmation_link@. \n Asante.",
                        "@greetings_sw@,\n Mkataba wa mteja wako unakamilika katika siku 5, tarehe @scheduled _termination_date@. Tunaweza kukuomba kurudi ofisini katika siku chache. Usiondoke kabla tukutumie ujumbe wa uthibitisho wa mwisho. Tafadhali mwambie Sir na Madam kwamba tumewatumia SMS kwa nambari yao ya simu. \n Asante."
                ),
                
                new SmsTranslation(Message_1_2_1,
                        "Congolese",
                        "@greetings_fr@\n le contrat de votre client prend fin dans 5 jours, le @scheduled_termination_date@. La nuit, nous vous demandons de revenir au bureau dans quelques jours. Ne partez pas avant de vous envoyer un dernier message de confirmation, veuillez informer Monsieur et Madame que nous leur avons envoyé un message. sms à leur numéro de téléphone, cliquez sur ce lien pour confirmer que vous avez bien lu ce message @confirmation_link@ Merci.",
                        "@greetings_fr@\n le contrat de votre client prend fin dans 5 jours, le @scheduled_termination_date@. La nuit, nous vous demandons de revenir au bureau dans quelques jours. Ne partez pas avant de vous envoyer un dernier message de confirmation, veuillez informer Monsieur et Madame que nous leur avons envoyé un message. sms à leur numéro de téléphone. Merci."
                ),
                
                new SmsTranslation(Message_1_2_1,
                        "Cameroonian",
                        "@greetings_fr@\n le contrat de votre client prend fin dans 5 jours, le @scheduled_termination_date@. La nuit, nous vous demandons de revenir au bureau dans quelques jours. Ne partez pas avant de vous envoyer un dernier message de confirmation, veuillez informer Monsieur et Madame que nous leur avons envoyé un message. sms à leur numéro de téléphone, cliquez sur ce lien pour confirmer que vous avez bien lu ce message @confirmation_link@ Merci.",
                        "@greetings_fr@\n le contrat de votre client prend fin dans 5 jours, le @scheduled_termination_date@. La nuit, nous vous demandons de revenir au bureau dans quelques jours. Ne partez pas avant de vous envoyer un dernier message de confirmation, veuillez informer Monsieur et Madame que nous leur avons envoyé un message. sms à leur numéro de téléphone. Merci."
                ),
                
                new SmsTranslation(Message_1_2_1,
                        "nigerian",
                        "@greetings_fr@\n le contrat de votre client prend fin dans 5 jours, le @scheduled_termination_date@. La nuit, nous vous demandons de revenir au bureau dans quelques jours. Ne partez pas avant de vous envoyer un dernier message de confirmation, veuillez informer Monsieur et Madame que nous leur avons envoyé un message. sms à leur numéro de téléphone, cliquez sur ce lien pour confirmer que vous avez bien lu ce message @confirmation_link@ Merci.",
                        "@greetings_fr@\n le contrat de votre client prend fin dans 5 jours, le @scheduled_termination_date@. La nuit, nous vous demandons de revenir au bureau dans quelques jours. Ne partez pas avant de vous envoyer un dernier message de confirmation, veuillez informer Monsieur et Madame que nous leur avons envoyé un message. sms à leur numéro de téléphone. Merci."
                ),
                
                new SmsTranslation(Message_1_2_1,
                        "ivoirienne",
                        "@greetings_fr@\n le contrat de votre client prend fin dans 5 jours, le @scheduled_termination_date@. La nuit, nous vous demandons de revenir au bureau dans quelques jours. Ne partez pas avant de vous envoyer un dernier message de confirmation, veuillez informer Monsieur et Madame que nous leur avons envoyé un message. sms à leur numéro de téléphone, cliquez sur ce lien pour confirmer que vous avez bien lu ce message @confirmation_link@ Merci.",
                        "@greetings_fr@\n le contrat de votre client prend fin dans 5 jours, le @scheduled_termination_date@. La nuit, nous vous demandons de revenir au bureau dans quelques jours. Ne partez pas avant de vous envoyer un dernier message de confirmation, veuillez informer Monsieur et Madame que nous leur avons envoyé un message. sms à leur numéro de téléphone. Merci."
                ),
                
                new SmsTranslation(Message_1_3_1,
                        "philippines",
                        "@greetings_tg@,\n Ang kontrata ng iyong amo ay matatapos BUKAS, sa @scheduled_termination_date@. Pakiempake ng iyong bag at maging handa sa pag alis BUKAS. Pero huwag kang umalis hanggat makapagpadala kami ng huling kompirmasyon galing sa amin. Paki pindot itong link para sa kompirmasyon na nabasa mo ang mensahe na ito @confirmation_link@. Salamat",
                        "@greetings_tg@,\n Ang kontrata ng iyong amo ay matatapos BUKAS, sa @scheduled_termination_date@. Pakiempake ng iyong bag at maging handa sa pag alis BUKAS. Pero huwag kang umalis hanggat makapagpadala kami ng huling kompirmasyon galing sa amin. Salamat"
                ),
                
                new SmsTranslation(Message_1_3_1,
                        "ethiopian",
                        "@greetings_am@, \n የቀጣሪዎ ውል ነገ ያልቃል፣ በ @scheduled_termination_date@ ።ነገ ወደ ቢሮ እንዲመጡ ልናሳውቆ እንችላለን።እንዲወጡ የመጨረሻ የማረጋገጫ መልዕክት እስክንልክ እንዳይወጡ።መልዕክት ልከን እንደነበር በስልካቸው ለቀጣሪዎችሽ አሳውቂልን።ይህን መልዕክት ማንበቦን ለማረጋገጥ ከታች ያለውን ማስፈንጠሪያ ይጫኑ  @confirmation_link@ እናመሰግናለን።",
                        "@greetings_am@, \n የቀጣሪዎ ውል ነገ ያልቃል፣ በ @scheduled_termination_date@ ።ነገ ወደ ቢሮ እንዲመጡ ልናሳውቆ እንችላለን።እንዲወጡ የመጨረሻ የማረጋገጫ መልዕክት እስክንልክ እንዳይወጡ።መልዕክት ልከን እንደነበር በስልካቸው ለቀጣሪዎችሽ አሳውቂልን። እናመሰግናለን።"
                ),
                
                new SmsTranslation(Message_1_3_1,
                        "kenyan",
                        "@greetings_sw@, \n Mkataba wa mteja wako unaisha KESHO, tarehe @scheduled_termination_date@. Tunaweza kukuomba kurudi ofisini KESHO. Usiondoke mpaka tukutumie ujumbe wa uthibitisho wa mwisho. Tafadhali mwambie Sir na Madam kwamba tumewatumia SMS kwa nambari yao ya simu. Bofya kwenye kiungo hiki ili kuthibitisha kwamba umesoma ujumbe huu @confirmation_link@.\n Asante",
                        "@greetings_sw@, \n Mkataba wa mteja wako unaisha KESHO, tarehe @scheduled_termination_date@. Tunaweza kukuomba kurudi ofisini KESHO. Usiondoke mpaka tukutumie ujumbe wa uthibitisho wa mwisho. Tafadhali mwambie Sir na Madam kwamba tumewatumia SMS kwa nambari yao ya simu.\n Asante"
                ),

                new SmsTranslation(Message_1_3_1,
                        "Congolese",
                        "@greetings_fr@,\n le contrat de votre client prend fin demain, le @scheduled_termination_date@, nous pourrons vous demander de revenir au bureau demain. Ne partez pas avant que nous vous envoyions un dernier message de confirmation. Veuillez informer Monsieur et Madame que nous avons envoyé un SMS à leur numéro de téléphone. Veuillez cliquer sur ce lien pour confirmer que vous avez bien lu ce message @confirmation_link@.\n Je vous remercie",
                        "@greetings_fr@,\n le contrat de votre client prend fin demain, le @scheduled_termination_date@, nous pourrons vous demander de revenir au bureau demain. Ne partez pas avant que nous vous envoyions un dernier message de confirmation. Veuillez informer Monsieur et Madame que nous avons envoyé un SMS à leur numéro de téléphone. \n Je vous remercie"
                ),
                
                new SmsTranslation(Message_1_3_1,
                        "Cameroonian",
                        "@greetings_fr@,\n le contrat de votre client prend fin demain, le @scheduled_termination_date@, nous pourrons vous demander de revenir au bureau demain. Ne partez pas avant que nous vous envoyions un dernier message de confirmation. Veuillez informer Monsieur et Madame que nous avons envoyé un SMS à leur numéro de téléphone. Veuillez cliquer sur ce lien pour confirmer que vous avez bien lu ce message @confirmation_link@.\n Je vous remercie",
                        "@greetings_fr@,\n le contrat de votre client prend fin demain, le @scheduled_termination_date@, nous pourrons vous demander de revenir au bureau demain. Ne partez pas avant que nous vous envoyions un dernier message de confirmation. Veuillez informer Monsieur et Madame que nous avons envoyé un SMS à leur numéro de téléphone. \n Je vous remercie"
                ),
                
                new SmsTranslation(Message_1_3_1,
                        "nigerian",
                        "@greetings_fr@,\n le contrat de votre client prend fin demain, le @scheduled_termination_date@, nous pourrons vous demander de revenir au bureau demain. Ne partez pas avant que nous vous envoyions un dernier message de confirmation. Veuillez informer Monsieur et Madame que nous avons envoyé un SMS à leur numéro de téléphone. Veuillez cliquer sur ce lien pour confirmer que vous avez bien lu ce message @confirmation_link@.\n Je vous remercie",
                        "@greetings_fr@,\n le contrat de votre client prend fin demain, le @scheduled_termination_date@, nous pourrons vous demander de revenir au bureau demain. Ne partez pas avant que nous vous envoyions un dernier message de confirmation. Veuillez informer Monsieur et Madame que nous avons envoyé un SMS à leur numéro de téléphone. \n Je vous remercie"
                ),
                
                new SmsTranslation(Message_1_3_1,
                        "ivoirienne",
                        "@greetings_fr@,\n le contrat de votre client prend fin demain, le @scheduled_termination_date@, nous pourrons vous demander de revenir au bureau demain. Ne partez pas avant que nous vous envoyions un dernier message de confirmation. Veuillez informer Monsieur et Madame que nous avons envoyé un SMS à leur numéro de téléphone. Veuillez cliquer sur ce lien pour confirmer que vous avez bien lu ce message @confirmation_link@.\n Je vous remercie",
                        "@greetings_fr@,\n le contrat de votre client prend fin demain, le @scheduled_termination_date@, nous pourrons vous demander de revenir au bureau demain. Ne partez pas avant que nous vous envoyions un dernier message de confirmation. Veuillez informer Monsieur et Madame que nous avons envoyé un SMS à leur numéro de téléphone. \n Je vous remercie"
                ),
                
                new SmsTranslation(Message_1_4_1,
                        "philippines",
                        "@greetings_tg@,\n Ang kontrata ng iyong amo ay natapos NGAYONG ARAW. Paki empake ng iyong gamit at maging handa sa pag alis NGAYONG ARAW. Kung gusto kang ihatid ng amo mo sa Opisina, sama ka po sa kanila. Pero kung ayaw kang dalhin ng amo mo, kalausapin ka namin ulit mamaya para kuhanan ka ng taxi mo. Paki pindot itong link para sa kompirmasyon na nabasa mo ang mensahe na ito @confirmation_link@. Salamat",
                        "@greetings_tg@,\n Ang kontrata ng iyong amo ay natapos NGAYONG ARAW. Paki empake ng iyong gamit at maging handa sa pag alis NGAYONG ARAW. Kung gusto kang ihatid ng amo mo sa Opisina, sama ka po sa kanila. Pero kung ayaw kang dalhin ng amo mo, kalausapin ka namin ulit mamaya para kuhanan ka ng taxi mo. Salamat"
                ),
                
                new SmsTranslation(Message_1_4_1,
                        "ethiopian",
                        "@greetings_am@,\n የቀጣሪዎ ውል ዛሬ ያልቃል።ስለዚ ዕቃዎን ያዘጋጁ እና ዛሬ ለመውጣት ይዘጋጁ።ቀጣሪዎ ወደ ቢሮ ሊያመጦ ከፈለጉ እባክዎ መምጣት ይችላሉ ፤ቀጣሪዎ ማምጣት ካልፈለጉ ግን ታክሲ እናዝሎትና ትንሽ ቆይተን እናናግሮታለን።ይህን መልዕክት ማንበቦን ለማረጋገጥ ከታች ያለውን ማስፈንጠሪያ ይጫኑ  @confirmation_link@ እናመሰግናለን።",
                        "@greetings_am@,\n የቀጣሪዎ ውል ዛሬ ያልቃል።ስለዚ ዕቃዎን ያዘጋጁ እና ዛሬ ለመውጣት ይዘጋጁ።ቀጣሪዎ ወደ ቢሮ ሊያመጦ ከፈለጉ እባክዎ መምጣት ይችላሉ ፤ቀጣሪዎ ማምጣት ካልፈለጉ ግን ታክሲ እናዝሎትና ትንሽ ቆይተን እናናግሮታለን። እናመሰግናለን።"
                ),
                
                new SmsTranslation(Message_1_4_1, 
                        "kenyan",
                        "@greetings_sw@,\n Mkataba wa mteja wako unakamilika leo. Tayarisha mizigo yako na uwe tayari kuondoka leo. Ikiwa mwajiri wako anataka kukupeleka ofisini, tafadhali nenda pamoja naye. Ikiwa mwajiri wako hataki kukupeleka, tutazungumza nawe baadaye ili kukuagizia teksi. Bofya kwenye kiungo hiki ili kuthibitisha kwamba umesoma ujumbe huu @confirmation_link@.\n Asante",
                        "@greetings_sw@,\n Mkataba wa mteja wako unakamilika leo. Tayarisha mizigo yako na uwe tayari kuondoka leo. Ikiwa mwajiri wako anataka kukupeleka ofisini, tafadhali nenda pamoja naye. Ikiwa mwajiri wako hataki kukupeleka, tutazungumza nawe baadaye ili kukuagizia teksi.\n Asante"
                ),
       
                new SmsTranslation(Message_1_4_1,
                        "Congolese",
                        "@greetings_fr@,\n le contrat de votre client prend fin aujourd'hui, alors faites vos valises et soyez prêt à partir aujourd'hui. si vos clients veulent vous ramener au bureau, veuillez les accompagner. Si votre client ne veut pas vous emmener, nous vous parlerons plus tard pour vous commander un taxi. Cliquez sur ce lien pour confirmer que vous avez lu ce message. @confirmation_link@\n Merci",
                        "@greetings_fr@,\n le contrat de votre client prend fin aujourd'hui, alors faites vos valises et soyez prêt à partir aujourd'hui. si vos clients veulent vous ramener au bureau, veuillez les accompagner. Si votre client ne veut pas vous emmener, nous vous parlerons plus tard pour vous commander un taxi.\n Merci"
                ),
                
                new SmsTranslation(Message_1_4_1,
                        "Cameroonian",
                        "@greetings_fr@,\n le contrat de votre client prend fin aujourd'hui, alors faites vos valises et soyez prêt à partir aujourd'hui. si vos clients veulent vous ramener au bureau, veuillez les accompagner. Si votre client ne veut pas vous emmener, nous vous parlerons plus tard pour vous commander un taxi. Cliquez sur ce lien pour confirmer que vous avez lu ce message. @confirmation_link@\n Merci",
                        "@greetings_fr@,\n le contrat de votre client prend fin aujourd'hui, alors faites vos valises et soyez prêt à partir aujourd'hui. si vos clients veulent vous ramener au bureau, veuillez les accompagner. Si votre client ne veut pas vous emmener, nous vous parlerons plus tard pour vous commander un taxi.\n Merci"
                ),
                
                new SmsTranslation(Message_1_4_1,
                        "nigerian",
                        "@greetings_fr@,\n le contrat de votre client prend fin aujourd'hui, alors faites vos valises et soyez prêt à partir aujourd'hui. si vos clients veulent vous ramener au bureau, veuillez les accompagner. Si votre client ne veut pas vous emmener, nous vous parlerons plus tard pour vous commander un taxi. Cliquez sur ce lien pour confirmer que vous avez lu ce message. @confirmation_link@\n Merci",
                        "@greetings_fr@,\n le contrat de votre client prend fin aujourd'hui, alors faites vos valises et soyez prêt à partir aujourd'hui. si vos clients veulent vous ramener au bureau, veuillez les accompagner. Si votre client ne veut pas vous emmener, nous vous parlerons plus tard pour vous commander un taxi.\n Merci"
                ),
                
                new SmsTranslation(Message_1_4_1,
                        "ivoirienne",
                        "@greetings_fr@,\n le contrat de votre client prend fin aujourd'hui, alors faites vos valises et soyez prêt à partir aujourd'hui. si vos clients veulent vous ramener au bureau, veuillez les accompagner. Si votre client ne veut pas vous emmener, nous vous parlerons plus tard pour vous commander un taxi. Cliquez sur ce lien pour confirmer que vous avez lu ce message. @confirmation_link@\n Merci",
                        "@greetings_fr@,\n le contrat de votre client prend fin aujourd'hui, alors faites vos valises et soyez prêt à partir aujourd'hui. si vos clients veulent vous ramener au bureau, veuillez les accompagner. Si votre client ne veut pas vous emmener, nous vous parlerons plus tard pour vous commander un taxi.\n Merci"
                ),
                
                new SmsTranslation(Message_1_5_1,
                        "philippines",
                        "Hello Ate @maid_name@,\n Ang kontrata ng amo mo ay natapos na. Kumuha po kami ng taxi para sa iyo at kukunin ka niya diyan sa loob ng 20 minuto. Pake empake na po ng gamit mo NGAYON at hintayin siya sa labas. Ipapadala namin sa iyo ang detalye ng drayber sa panibagong mensahe. Paki pindot itong link para sa kompirmasyon na nabasa mo ang mensahe na ito @confirmation_link@. Salamat",
                        "Hello Ate @maid_name@,\n Ang kontrata ng amo mo ay natapos na. Kumuha po kami ng taxi para sa iyo at kukunin ka niya diyan sa loob ng 20 minuto. Pake empake na po ng gamit mo NGAYON at hintayin siya sa labas. Ipapadala namin sa iyo ang detalye ng drayber sa panibagong mensahe. Salamat"
                ), 
                
                new SmsTranslation(Message_1_5_1,
                        "ethiopian",
                        "ሰላም  @maid_name@, የቀጣሪዎ ውል አልቋል፤ታክሲ ልከንሎታል።ታክሲው በ20 ደቂቃ ውስጥ ይወስዶታል።ዕቃዎን አሁኑኑ ያዘጋጁ እና ወተው ታክሲውን ይጠብቁ። በሌላ መልዕክት የታክሲውን ሙሉ መረጃ እንልክሎታለን። ይህን መልዕክት ማንበቦን ለማረጋገጥ ከታች ያለውን ማስፈንጠሪያ ይጫኑ  @confirmation_link@",
                        "ሰላም  @maid_name@, የቀጣሪዎ ውል አልቋል፤ታክሲ ልከንሎታል።ታክሲው በ20 ደቂቃ ውስጥ ይወስዶታል።ዕቃዎን አሁኑኑ ያዘጋጁ እና ወተው ታክሲውን ይጠብቁ። በሌላ መልዕክት የታክሲውን ሙሉ መረጃ እንልክሎታለን።"
                ),
                
                new SmsTranslation(Message_1_5_1, 
                        "kenyan",
                        "Jambo @maid_name@, mkataba wa mteja wako umekwisha.Tumekuagizia teksi sasa hivi.Teksi itakuchukua katika dakika 20.Tayarisha mizigo wako sasa hivi na usubiri teksi nje. Tutakutumia maelezo ya teksi katika ujumbe tofauti. Tafadhali bofya kwenye kiungo hiki ili kuhakikisha kwamba umesoma ujumbe huu @confirmation_link@",
                        "Jambo @maid_name@, mkataba wa mteja wako umekwisha.Tumekuagizia teksi sasa hivi.Teksi itakuchukua katika dakika 20.Tayarisha mizigo wako sasa hivi na usubiri teksi nje. Tutakutumia maelezo ya teksi katika ujumbe tofauti."
                ),
  
                new SmsTranslation(Message_1_5_1,
                        "Congolese",
                        "Bonjour @maid_name@, le contrat de votre client est terminé.Nous venons de commander un taxi pour vous.Le taxi viendra vous chercher dans 20 minutes.emballez vos affaires maintenant et attendez le taxi à l'extérieur.Nous vous enverrons les détails du taxi le un message différent. veuillez cliquer sur ce lien pour confirmer que vous avez lu ce message @confirmation_link@",
                        "Bonjour @maid_name@, le contrat de votre client est terminé.Nous venons de commander un taxi pour vous.Le taxi viendra vous chercher dans 20 minutes.emballez vos affaires maintenant et attendez le taxi à l'extérieur.Nous vous enverrons les détails du taxi le un message différent. "
                ),
                
                new SmsTranslation(Message_1_5_1,
                        "Cameroonian",
                        "Bonjour @maid_name@, le contrat de votre client est terminé.Nous venons de commander un taxi pour vous.Le taxi viendra vous chercher dans 20 minutes.emballez vos affaires maintenant et attendez le taxi à l'extérieur.Nous vous enverrons les détails du taxi le un message différent. veuillez cliquer sur ce lien pour confirmer que vous avez lu ce message @confirmation_link@",
                        "Bonjour @maid_name@, le contrat de votre client est terminé.Nous venons de commander un taxi pour vous.Le taxi viendra vous chercher dans 20 minutes.emballez vos affaires maintenant et attendez le taxi à l'extérieur.Nous vous enverrons les détails du taxi le un message différent. "
                ),
                
                new SmsTranslation(Message_1_5_1,
                        "nigerian",
                        "Bonjour @maid_name@, le contrat de votre client est terminé.Nous venons de commander un taxi pour vous.Le taxi viendra vous chercher dans 20 minutes.emballez vos affaires maintenant et attendez le taxi à l'extérieur.Nous vous enverrons les détails du taxi le un message différent. veuillez cliquer sur ce lien pour confirmer que vous avez lu ce message @confirmation_link@",
                        "Bonjour @maid_name@, le contrat de votre client est terminé.Nous venons de commander un taxi pour vous.Le taxi viendra vous chercher dans 20 minutes.emballez vos affaires maintenant et attendez le taxi à l'extérieur.Nous vous enverrons les détails du taxi le un message différent. "
                ),
                
                new SmsTranslation(Message_1_5_1,
                        "ivoirienne",
                        "Bonjour @maid_name@, le contrat de votre client est terminé.Nous venons de commander un taxi pour vous.Le taxi viendra vous chercher dans 20 minutes.emballez vos affaires maintenant et attendez le taxi à l'extérieur.Nous vous enverrons les détails du taxi le un message différent. veuillez cliquer sur ce lien pour confirmer que vous avez lu ce message @confirmation_link@",
                        "Bonjour @maid_name@, le contrat de votre client est terminé.Nous venons de commander un taxi pour vous.Le taxi viendra vous chercher dans 20 minutes.emballez vos affaires maintenant et attendez le taxi à l'extérieur.Nous vous enverrons les détails du taxi le un message différent. "
                )
                 
        ); 
        return translations;
    } 
 


}

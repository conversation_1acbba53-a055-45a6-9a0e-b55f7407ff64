package com.magnamedia.module;

import com.magnamedia.core.entity.DynamicApi;
import java.util.Arrays;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SetupDynamicApis {

    public List<DynamicApi> setup() {
        return Arrays.asList(
                bouncedPayByCardFaqBody(),
                getClientDirectDebitRejected33Link2VisibilityApi(),
                getClientDirectDebitRejected33Action1VisibilityApi(),
                clientPayingViaCreditCard());
    }

    private DynamicApi getClientDirectDebitRejected33Link2VisibilityApi() {
        DynamicApi api = new DynamicApi();
        api.setCode("client_direct_debit_rejected33_link2");
        api.setExpression("T(com.magnamedia.core.Setup).getApplicationContext().getBean('ccAppNotificationExpiry').getClientDirectDebitRejected33Link2VisibilityApi(_entityId_)");
        return api;
    }

    private DynamicApi getClientDirectDebitRejected33Action1VisibilityApi() {
        DynamicApi api = new DynamicApi();
        api.setCode("client_direct_debit_rejected33_Action1");
        api.setExpression("T(com.magnamedia.core.Setup).getApplicationContext().getBean('ccAppNotificationExpiry').getClientDirectDebitRejected33Action1VisibilityApi(_entityId_)");
        return api;
    }
    
    private DynamicApi bouncedPayByCardFaqBody() {
        DynamicApi api = new DynamicApi();
        api.setCode("bounced_pay_by_card_faq_body");
        api.setExpression("T(com.magnamedia.core.Setup).getApplicationContext().getBean('CCAppContentService').bouncedPayByCardFaqBody(_entityId_)");
        return api;
    }
    
    private DynamicApi clientPayingViaCreditCard() {
        DynamicApi api = new DynamicApi();
        api.setCode("client_paying_via_credit_card");
        api.setExpression("T(com.magnamedia.core.Setup).getApplicationContext().getBean('CCAppContentService').clientPayingViaCreditCard(_entityId_)");
        return api;
    }
}

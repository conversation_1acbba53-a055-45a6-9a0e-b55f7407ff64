package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankPaymentActivationRecord;
import com.magnamedia.repository.BankPaymentActivationRecordRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 17, 2018
 * Jirra ACC-423
 */
@RequestMapping("/bankpaymentactivationrecords")
@RestController
public class BankPaymentActivationRecordController
        extends BaseRepositoryController<BankPaymentActivationRecord>{

    @Autowired
    private BankPaymentActivationRecordRepository bankPaymentActivationRecordRepository;
    
    @Override
    public BaseRepository<BankPaymentActivationRecord> getRepository() {
        return bankPaymentActivationRecordRepository;
    }
    
}

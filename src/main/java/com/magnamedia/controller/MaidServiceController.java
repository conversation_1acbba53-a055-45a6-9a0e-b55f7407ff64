package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.MaidService;
import com.magnamedia.repository.MaidServiceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Apr 28, 2019
 */

@RequestMapping("/maidservices")
@RestController
public class MaidServiceController extends BaseRepositoryController<MaidService> {
    
    @Autowired
    private MaidServiceRepository maidServiceRepository;
    
    @Override
    public BaseRepository<MaidService> getRepository() {
        return maidServiceRepository;

    }

}

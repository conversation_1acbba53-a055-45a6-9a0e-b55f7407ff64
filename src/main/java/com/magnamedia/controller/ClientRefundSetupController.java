package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.ClientRefundSetup;
import com.magnamedia.entity.ComplaintType;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.repository.ClientRefundSetupRepository;
import com.magnamedia.repository.ComplaintTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RequestMapping("/clientRefundSetup")
@RestController
public class ClientRefundSetupController extends BaseRepositoryController<ClientRefundSetup> {

    @Autowired
    private ClientRefundSetupRepository clientRefundSetupRepository;

    @Override
    public BaseRepository<ClientRefundSetup> getRepository() {
        return clientRefundSetupRepository;
    }

    @PreAuthorize("hasPermission('clientRefundSetup','getSetupByPurpose')")
    @RequestMapping(value = "/getSetupByPurpose/{purposeId}", method = RequestMethod.GET)
    public ResponseEntity<?> getSetupByPurpose(@PathVariable("purposeId") PaymentRequestPurpose paymentRequestPurpose) {
        if (paymentRequestPurpose == null) throw new RuntimeException("Purpose Not Found");
        if (paymentRequestPurpose.getUniquePurposeSetup() != null)
            return new ResponseEntity<>(paymentRequestPurpose.getUniquePurposeSetup(), HttpStatus.OK);
        else throw new RuntimeException("This purpose don't have setup");
    }

    //Jirra ACC-3123
    public List<ComplaintType> getComplaintTypes() {
        InterModuleConnector moduleConnector = Setup.getApplicationContext().getBean(InterModuleConnector.class);
        ComplaintTypeRepository complaintTypeRepository = Setup.getApplicationContext().getBean(ComplaintTypeRepository.class);

        List<Long> typesIDs = new ArrayList();

        List<Map> response = moduleConnector.get("complaints/category/list/withTypes", List.class);

        if (response != null) {
            for (Map item : response) {
                List<Map> itemTypes = (List) item.get("types");
                typesIDs.addAll(itemTypes.stream()
                        .map(itemType -> Long.parseLong(itemType.get("id").toString()))
                        .collect(Collectors.toList()));
            }
        }

        return complaintTypeRepository.findAll(typesIDs);
    }
}

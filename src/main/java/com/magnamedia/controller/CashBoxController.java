package com.magnamedia.controller;

import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.CashBox;
import com.magnamedia.module.type.BucketType;
import com.magnamedia.repository.BucketRepository;
import com.magnamedia.repository.CashBoxRepository;
import com.magnamedia.service.AccountBalanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * <PERSON> (Jan 27, 2021)
 */
@RestController
@RequestMapping("/cash-box")
public class CashBoxController extends BaseRepositoryController<CashBox> {
    @Autowired
    CashBoxRepository cashBoxRepository;
    @Autowired
    UserRepository userRepository;
    @Autowired
    AccountBalanceService accountBalanceService;
    @Autowired
    BucketRepository bucketRepository;

    @Override
    public BaseRepository<CashBox> getRepository() {
        return cashBoxRepository;
    }

    @NoPermission
    @PostMapping("/update-cash-box")
    public ResponseEntity<?> updateCashBox(
            Authentication authentication,
            @RequestBody CashBox cashBox) {

        String username = authentication.getName();
        User user = userRepository.findByLoginName(username);
        cashBox.setUser(user);

        Bucket bucket = bucketRepository.findFirstByHolderAndBucketType(user, BucketType.CASH_BOX);
        if (bucket == null) throw new BusinessException("No cash bucket assigned to your user");

        //Double balance = accountBalanceService.addAccountBalance(buckets.get(0));
        Bucket b = accountBalanceService.setBucketBalanceBasedOnTransaction(bucket);

        if (b == null) return badRequestResponse();

        cashBox.setBucket(b);
        cashBox.setLastCloseDate(new Date());
        cashBox.setBucketBalance(b.getBalance());
        cashBoxRepository.save(cashBox);

        return ResponseEntity.ok().build();
    }
}

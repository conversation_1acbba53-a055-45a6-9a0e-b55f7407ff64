package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.workflow.WorkflowController;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.helper.ConcurrentModificationHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.workflow.entity.projection.DirectDebitCancelationToDoProjection;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoFlow;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitARejectionWaitingClientReSignStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBBouncedRejectionWaitingClientReSignStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBCaseDRejectionWaitingClientReSignStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBRejectionWaitingReScheduleStep;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 4-4-2020
 *         Jirra ACC-1595
 */
@RestController
@RequestMapping("/directdebitrejectiontodos")
public class DirectDebitRejectionToDoController
        extends WorkflowController<DirectDebitRejectionToDo, DirectDebitRejectionToDoFlow> {

    private static final String prefix = "MMM";

    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;

    @Autowired
    private DirectDebitRejectionFlowService directDebitRejectionFlowService;

    @Autowired
    private DirectDebitRepository ddRepo;
    
    @Override
    public ResponseEntity<?> createEntity(DirectDebitRejectionToDo directDebitRejectionToDo) {
        return super.createEntity(directDebitRejectionToDo);
    }

    @Transactional
    public void proccessReminderAndLastReSignTrial(Long directDebitRejectionToDoID) {
        DirectDebitRejectionToDo directDebitRejectionToDo = getRepository().findOne(directDebitRejectionToDoID);
        logger.log(Level.INFO, "directDebitRejectionToDo id: " + directDebitRejectionToDo.getId());

        if (directDebitRejectionToDo.isCompleted() || directDebitRejectionToDo.isStopped()) {
            logger.log(Level.INFO, "directDebitRejectionToDo completed or stopped: " + directDebitRejectionToDo.getId());
            return;
        }

        List<DirectDebit> directDebitsList = directDebitRejectionToDo.getDirectDebits();
        if (directDebitsList == null) {
            logger.info("No DDs Found");
            return;
        }
        if (directDebitsList.size() > 0) {
            boolean thereIsNoIncompleteDD = directDebitsList.stream()
                    .noneMatch(dd -> (dd.getCategory() == DirectDebitCategory.A && dd.getMStatus() == DirectDebitStatus.IN_COMPLETE)
                            || dd.getCategory() == DirectDebitCategory.B && (dd.getStatus() == DirectDebitStatus.IN_COMPLETE
                            || dd.getMStatus() == DirectDebitStatus.IN_COMPLETE));

            if (thereIsNoIncompleteDD) {
                logger.log(Level.INFO, "directDebitRejectionToDo has no incomplete dd");
                return;
            }

            if (directDebitRejectionToDo.getLastRejectCategory() == DirectDebitRejectCategory.Signature) {
                DirectDebit directDebit = ddRepo.findOne(directDebitsList.get(0).getId());
                Contract contract = directDebit.getContractPaymentTerm() != null ? directDebit.getContractPaymentTerm().getContract() : null;

                if (contract != null && directDebitRejectionFlowService.doesClientHavePendingDesignerToDo(contract)) {
                    logger.info("Contract: " + contract.getId() + " Has a Pending Graphic Designer ToDo -> do nothing");
                    return;
                }
            }
        }

        directDebitsList = directDebitRejectionToDo.getDirectDebitsForBouncingFlow();

        for (DirectDebit directDebit : directDebitsList) {
            SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
            query.filterBy("directDebitId", "=", directDebit.getId());
            query.filterBy("replaced", "=", false);
            query.filterBy("bouncedFlowPausedForReplacement", "=", true);
            List<Payment> execute = query.execute();

            if (!execute.isEmpty()) {
                logger.log(Level.INFO, "directDebitRejectionToDo is on direct debit that has paused payment for bouncing flow" +
                        "; directDebit id:" + directDebit.getId() +
                        "; paused payment id:" + execute.get(0).getId());
                return;
            }
        }


        DirectDebit lastDD = directDebitRejectionToDo.getLastDirectDebit();
        if (lastDD != null && lastDD.getContractPaymentTerm() != null && lastDD.getContractPaymentTerm().getContract() != null) {
            Contract contract = lastDD.getContractPaymentTerm().getContract();

            if (contract.getStatus() == ContractStatus.CANCELLED || contract.getStatus() == ContractStatus.EXPIRED) {
                logger.log(Level.INFO, "contract is not active; status is: " + contract.getStatus());
                return;
            }
        }

        Integer maxReSignTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DD_MAX_RE_SIGN_TRIALS));

        Date today = new Date();
        Date reminderDate;
        int hours;
        
        switch(directDebitRejectionToDo.getLastRejectCategory()) {
            case Authorization:
                reminderDate = directDebitRejectionToDo.getReminderDate();

                logger.log(Level.INFO, "category: " + DirectDebitRejectCategory.Authorization +
                        "; reminderDate: " + reminderDate +
                        "; today: " + today);

                hours = Math.abs(DateUtil.hoursDifference(reminderDate, today));

                Integer reminderPeriod = Integer.parseInt(Setup.getParameter(
                        Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_REMINDER_PERIOD));
                
                if (hours >= reminderPeriod) {
                    // ACC-2969
                    String checkIfLockedKey = DirectDebitRejectionToDoController.class.getSimpleName() + "proccessReminderAndLastReSignTrial" + Contract.class.getSimpleName();
                    Long contractId = lastDD.getContractPaymentTerm().getContract().getId();

                    if (lastDD.getStatus().equals(DirectDebitStatus.IN_COMPLETE)) {
                        ConcurrentModificationHelper.lockOrThrowException(checkIfLockedKey, contractId);
                    }

                    try {
                        DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(directDebitRejectionToDo.getTaskName());
                        DirectDebitRejectionToDoManualStep clientReSignStep = null;

                        switch (step) {
                            case WAITING_CLIENT_SIGNATURE: {
                                clientReSignStep = Setup.getApplicationContext().getBean(DirectDebitARejectionWaitingClientReSignStep.class);
                                break;
                            }
                            case WAITING_CLIENT_SIGNATURE_B_CASE_D: {
                                clientReSignStep = Setup.getApplicationContext().getBean(DirectDebitBCaseDRejectionWaitingClientReSignStep.class);
                                break;
                            }
                            case WAITING_CLIENT_SIGNATURE_B_BOUNCED: {
                                clientReSignStep = Setup.getApplicationContext().getBean(DirectDebitBBouncedRejectionWaitingClientReSignStep.class);
                                break;
                            }
                        }

                        if (clientReSignStep != null) {
                            clientReSignStep.onDone(directDebitRejectionToDo);
                        }

                        ConcurrentModificationHelper.unLock(checkIfLockedKey, contractId);
                    } catch (Exception e) {
                        logger.severe("Exception while processing Contract#" + contractId);
                        ConcurrentModificationHelper.unLock(checkIfLockedKey, contractId);

                        throw new RuntimeException(e);
                    }
                }
                break;

            case Signature:
                if(directDebitRejectionToDo.getReSignTrials() >= maxReSignTrials) {
                    Integer signLastTrialPeriod = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_DD_SIGN_LAST_TRIAL_PERIOD));

                    if (directDebitRejectionToDo.getLastTrialDate() == null) {
                        logger.log(Level.INFO, "directDebitRejectionToDo getLastTrialDate is null");
                        return;
                    }

                    Date lastTrialDate = directDebitRejectionToDo.getLastTrialDate();
                    hours = Math.abs(DateUtil.hoursDifference(lastTrialDate, today));

                    logger.log(Level.INFO, "lastTrialDate: " + lastTrialDate +
                            "; today: " + today +
                            "; signLastTrialPeriod: " + signLastTrialPeriod +
                            "; Period hours: " + hours);

                    if (hours >= signLastTrialPeriod) {
                        DirectDebit lastDirectDebit = directDebitRejectionToDo.getLastDirectDebit();

                        if (lastDirectDebit != null) {
                            Contract contract = lastDirectDebit.getContractPaymentTerm().getContract();

                            if (contract.getIsScheduledForTermination()) {
                                logger.log(Level.INFO, "contract.getIsScheduledForTermination: " + contract.getIsScheduledForTermination());
                                return;
                            }

                            if (contract.isTerminateContractDueRejection() || !directDebitRejectionToDo.isDdAddedByOecFlow()) {
                                logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                                        "; entity.isDdAddedByOecFlow: " + directDebitRejectionToDo.isDdAddedByOecFlow());

                                directDebitRejectionToDo.setContractScheduleDateOfTermination(
                                        directDebitRejectionFlowService
                                                .setContractForTermination(lastDirectDebit.getContractPaymentTerm(),
                                                        "direct_debit_rejection_type_a_maxsignaturetrials_reached",
                                                        directDebitRejectionToDo));
                                directDebitRejectionToDo.setLeadingRejectionFlow(true);
                            }

                            directDebitRejectionToDo.setStopped(true);
                            directDebitRejectionToDoRepository.save(directDebitRejectionToDo);

                        } else {
                            logger.log(Level.INFO, "with no getLastDirectDebit");
                        }
                    }
                    
                    break;
                }
                
            default:
                if (directDebitRejectionToDo.getReminderDate() == null) {
                    logger.log(Level.INFO, "directDebitRejectionToDo getReminderDate is null");
                    return;
                }
                reminderDate = directDebitRejectionToDo.getReminderDate();
                hours = Math.abs(DateUtil.hoursDifference(reminderDate, today));
                reminderPeriod = Integer.parseInt(Setup.getParameter(
                        Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_REMINDER_PERIOD));
                Integer signReminderPeriod = Integer.parseInt(Setup.getParameter(
                        Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_SIGN_REMINDER_PERIOD));

                logger.log(Level.INFO, "reminderDate: " + reminderDate  +
                        "; today: " + today +
                        "; reminderPeriod: " + reminderPeriod +
                        "; signReminderPeriod: " + signReminderPeriod +
                        "; Period hours: " + hours);

                boolean valid = false;
                if (directDebitRejectionToDo.getLastRejectCategory() == DirectDebitRejectCategory.Signature) {
                    if (hours >= signReminderPeriod) {
                        valid = true;
                    }
                } else {
                    if (hours >= reminderPeriod) {
                        valid = true;
                    }
                }

                logger.log(Level.INFO, "valid: " + valid);
                if (!valid) return;

                Integer maxReminders = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_DD_MAX_REMINDERS));
                Integer maxSignReminders = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_DD_MAX_SIGN_REMINDERS));

                DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(directDebitRejectionToDo.getTaskName());

                switch (step) {
                    case WAITING_CLIENT_SIGNATURE:
                    case WAITING_CLIENT_SIGNATURE_B_CASE_D:
                    case WAITING_CLIENT_SIGNATURE_B_BOUNCED: {
                        logger.log(Level.INFO, "old reminder: " + directDebitRejectionToDo.getReminder());

                        boolean leadingRejectionFlow = false;
                        if(lastDD != null) {
                            leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                                    lastDD.getContractPaymentTerm().getContract(), Arrays.asList(lastDD.getId()));
                        }

                        // reminder +1 because after 24 hours we didn't get new info
                        directDebitRejectionToDo.setReminder(directDebitRejectionToDo.getReminder() + 1);
                        directDebitRejectionToDo.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                        directDebitRejectionToDo.setLeadingRejectionFlow(leadingRejectionFlow);

                        logger.log(Level.INFO, "leadingRejectionFlow: " + leadingRejectionFlow +
                                "; new reminder: " + directDebitRejectionToDo.getReminder());

                        int comparedNumber = directDebitRejectionToDo.getLastRejectCategory() == DirectDebitRejectCategory.Signature ?
                                maxSignReminders : maxReminders;

                        if (directDebitRejectionToDo.getReminder() >= comparedNumber) {
                            DirectDebit lastDirectDebit = directDebitRejectionToDo.getLastDirectDebit();

                            if (lastDirectDebit != null) {
                                Contract contract = lastDirectDebit.getContractPaymentTerm().getContract();

                                if (contract.isTerminateContractDueRejection() || !directDebitRejectionToDo.isDdAddedByOecFlow()) {
                                    logger.info(" contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                                            "; entity.isDdAddedByOecFlow: " + directDebitRejectionToDo.isDdAddedByOecFlow());

                                    directDebitRejectionToDo.setContractScheduleDateOfTermination(
                                            directDebitRejectionFlowService
                                                    .setContractForTermination(lastDirectDebit.getContractPaymentTerm(),
                                                            "client_did_not_provide_new_info_after_rejection",
                                                            directDebitRejectionToDo));
                                    directDebitRejectionToDo.setLeadingRejectionFlow(true);
                                }

                                directDebitRejectionToDo.setStopped(true);

                            } else {
                                logger.log(Level.INFO, "with no getLastDirectDebit");
                            }
                        }

                        directDebitRejectionToDoRepository.save(directDebitRejectionToDo);
                        break;
                    }
                }
                break;
        }
    }

    @Override
    public BaseRepository<DirectDebitRejectionToDo> getRepository() {
        return directDebitRejectionToDoRepository;
    }

    @Override
    protected SelectFilter filter(SelectFilter filter, String search, List<String> joins, Map<String, String> joinType) {
        return filter;
    }

    @Override
    protected Class<?> getProjectionClass() {
        return DirectDebitCancelationToDoProjection.class;
    }

    @Transactional
    public void processWaitingReScheduleStepForDDB(Long directDebitRejectionToDoID) {
        DirectDebitRejectionToDo directDebitRejectionToDo = getRepository().findOne(directDebitRejectionToDoID);
        logger.log(Level.INFO, "directDebitRejectionToDo id: " + directDebitRejectionToDo.getId());

        if (directDebitRejectionToDo.isCompleted() || directDebitRejectionToDo.isStopped()) {
            logger.log(Level.INFO, "directDebitRejectionToDo is stopped or completed");
            return;
        }

        List<String> currentTasks = directDebitRejectionToDo.getCurrentTasks();
        if (!currentTasks.isEmpty()) {
            DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTasks.get(currentTasks.size() - 1));

            switch (step) {
                case WAITING_RE_SCHEDULE_B: {
                    Setup.getApplicationContext().getBean(DirectDebitBRejectionWaitingReScheduleStep.class)
                            .onDone(directDebitRejectionToDo);
                    break;
                }
            }
        }
    }
}
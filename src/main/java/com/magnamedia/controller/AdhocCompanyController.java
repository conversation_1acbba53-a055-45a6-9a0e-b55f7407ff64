package com.magnamedia.controller;


import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.*;
import com.magnamedia.report.ADHOC_Report;
import com.magnamedia.report.BaseReport;
import com.magnamedia.repository.AdhocNodeRepository;
import com.magnamedia.repository.AdhocVariableBucketRepository;
import com.magnamedia.repository.BasePLNodeRepository;
import com.magnamedia.repository.BasePLVariableBucketRepository;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.commons.io.IOUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@RequestMapping("/adhoccompanies")
@RestController
public class AdhocCompanyController extends BaseCompanyReportController<AdhocCompany> {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    protected ResponseEntity<?> createEntity(AdhocCompany company) {
        company = getRepository().save(company);
        int nodeOrder = 0;
        for (BasePLNode node : company.getChildren()) {
            node.setPLCompany(company);
            node.setNodeOrder(nodeOrder++);
            linkAdhocNodes((AdhocNode) node);
            node.validate();
        }

        return new ResponseEntity(company, HttpStatus.OK);
    }

    @Override
    @Transactional
    public ResponseEntity<?> update(@RequestBody ObjectNode objectNode) throws IOException {
        if (this.checkPermission("update")) {
            AdhocCompany company = this.parse(objectNode);
            company.validate();
            company = entityManager.merge(company);
            int nodeOrder = 0;
            for (BasePLNode node : company.getChildren()) {
                node.setPLCompany(company);
                node.setNodeOrder(nodeOrder++);
                linkAdhocNodes((AdhocNode) node);
                node.validate();
            }

            return new ResponseEntity(new Response("Updated"), HttpStatus.OK);
        } else {
            return this.unauthorizedReponse();
        }

    }

    private void linkAdhocNodes(AdhocNode parent) {
        int nodeOrder = 0;
        if (parent.getChildren() != null) {
            for (BasePLNode child : parent.getChildren()) {
                child.setParent(parent);
                child.setNodeOrder(nodeOrder++);

                if (child.getType().equalsIgnoreCase(AdhocNode.class.getSimpleName())) {
                    linkAdhocNodes((AdhocNode) child);
                } else if (child.getType().equalsIgnoreCase(AdhocVariableNode.class.getSimpleName())) {
                    linkAdhocVariableNodes((AdhocVariableNode) child);
                }

                child.validate();
            }
        }
    }

    private void linkAdhocVariableNodes(AdhocVariableNode variableNode) {
        if (variableNode.getpLVariableBuckets() != null && !variableNode.getpLVariableBuckets().isEmpty()) {
            for (BasePLVariableBucket variableBucket : variableNode.getpLVariableBuckets()) {
                variableBucket.setpLVariable(variableNode);
                variableBucket.validate();
            }
        }
    }

    @Override
    protected BaseReport getReport(BaseReportCompany pLCompany, Date fromDate, Date toDate, boolean isColored, int levels, String baseUrl, String format, boolean showFormula, boolean withRounding
            , SearchCriteria searchCriteria) throws Exception {
        return new ADHOC_Report(pLCompany, fromDate, toDate, isColored, levels, baseUrl, format.toString(), showFormula);
    }

    @Override
    public void generateExcelReportFile(HttpServletResponse response, AdhocCompany adhocCompany, Date fromDate, Date toDate, boolean isColored, Integer maxLevel, boolean showFormula, boolean withRounding) throws Exception {
        List<BasePLNode> headNodes = adhocCompany.getSortedChildren();
        XSSFWorkbook workbook = new XSSFWorkbook();

        XSSFFont boldTitleFont = workbook.createFont();
        boldTitleFont.setBold(true);
        boldTitleFont.setFontHeightInPoints((short) 16);

        XSSFFont boldFont = workbook.createFont();
        boldFont.setBold(true);

        DataFormat fmt = workbook.createDataFormat();

        XSSFSheet spreadsheet = workbook.createSheet("Sheet1");
        spreadsheet.setColumnWidth(0, 25 * 256);
        spreadsheet.setColumnWidth(1, 25 * 256);
        spreadsheet.setColumnWidth(2, 25 * 256);
        if (showFormula)
            spreadsheet.setColumnWidth(3, 25 * 256);

        int rowId = 0;

        // Header Text Row
        buildTableNameRow(workbook, spreadsheet, adhocCompany.getReportTitle(), fmt, boldFont, rowId);
        buildTableNameRow(workbook, spreadsheet, "From Date: " + com.magnamedia.helper.DateUtil.formatFullDate(fromDate), fmt, boldFont, ++rowId);
        buildTableNameRow(workbook, spreadsheet, "To Date: " + com.magnamedia.helper.DateUtil.formatFullDate(toDate), fmt, boldFont, ++rowId);

        //Details Table
        for (BasePLNode pLNode : headNodes) {
            buildSpacerRow(spreadsheet, ++rowId);
            buildTableNameRow(workbook, spreadsheet, pLNode.getName(), fmt, boldFont, ++rowId);
            String[] headers = showFormula ? new String[]{"Name", "Amount (AED)", "Ratio", "Formula"} :
                    new String[]{"Name", "Amount (AED)", "Ratio"};
            buildHeadTableRow(
                    workbook,
                    spreadsheet,
                    headers,
                    fmt,
                    boldFont,
                    ++rowId);
            //Details Table Body
            rowId = buildNodeBlock(fromDate, toDate, null, null, workbook, pLNode, spreadsheet, rowId, null, maxLevel, false, isColored, fmt, boldFont, showFormula, withRounding);
        }

        //File
        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                new Date().getTime() + "")
                .toFile();
        FileOutputStream out = new FileOutputStream(file);
        workbook.write(out);
        out.close();
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Disposition",
                "attachment; filename=\""
                        + adhocCompany.getName()
                        + ".xlsx"
                        + "\"");
        IOUtils.copy(
                new FileInputStream(file),
                response.getOutputStream());

        response.getOutputStream().flush();

    }

    @Override
    protected Class<AdhocCompany> getEntityClass() {
        return AdhocCompany.class;
    }

    @Override
    public BasePLNodeRepository getBaseNodeRepository() {
        return Setup.getRepository(AdhocNodeRepository.class);
    }

    @Override
    protected Class getVariableBucketEntityClass() {
        return AdhocVariableBucket.class;
    }

    @Override
    public BasePLVariableBucketRepository getBasePLVariableBucketRepository() {
        return Setup.getRepository(AdhocVariableBucketRepository.class);
    }
}

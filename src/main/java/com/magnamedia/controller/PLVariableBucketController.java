package com.magnamedia.controller;

import com.magnamedia.entity.PLVariableBucket;
import com.magnamedia.entity.PLVariableNode;
import com.magnamedia.repository.BasePLVariableBucketRepository;
import com.magnamedia.repository.BasePLVariableRepository;
import com.magnamedia.repository.PLVariableBucketRepository;
import com.magnamedia.repository.PLVariableNodeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> kanaan <<EMAIL>> Created on Oct 8, 2018
 */
@RequestMapping("/plvariablebuckets")
@RestController
public class PLVariableBucketController extends BasePLVariableBucketController<PLVariableBucket> {
    @Autowired
    private PLVariableBucketRepository pLVariableBucketRepository;
    @Autowired
    private PLVariableNodeRepository plVariableNodeRepository;

    @Override
    public BasePLVariableRepository getPlVariableRepository() {
        return plVariableNodeRepository;
    }

    @Override
    public BasePLVariableBucketRepository<PLVariableBucket> getRepository() {
        return pLVariableBucketRepository;
    }
}

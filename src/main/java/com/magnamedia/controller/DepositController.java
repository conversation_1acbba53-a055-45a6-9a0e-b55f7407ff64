package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Deposit;
import com.magnamedia.repository.DepositRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 2, 2018
 * Jirra ACC-284
 */

@RequestMapping("/deposits")
@RestController
public class DepositController extends BaseRepositoryController<Deposit> {
    
    @Autowired
    private DepositRepository depositRepository;
    
    @Override
    public BaseRepository<Deposit> getRepository() {
        return depositRepository;

    }

}

package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.entity.PurchaseOrder;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.entity.dto.PurchaseBillInfoDto;
import com.magnamedia.entity.dto.PurchaseItemDtoForGetSupplier;
import com.magnamedia.entity.dto.PurchaseOrderDto;
import com.magnamedia.entity.dto.PurchasingManagerListDto;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.helper.AttachmentHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.ConsumptionRateService;
import com.magnamedia.service.EmailTemplateService;
import com.magnamedia.service.StockKeeperToDoService;
import com.magnamedia.workflow.service.purchasingsteps.GetBestSupplierStep;
import com.magnamedia.workflow.service.purchasingsteps.GetBetterSupplierStep;
import com.magnamedia.workflow.service.purchasingsteps.PurchasingStep;
import com.magnamedia.workflow.type.AttachmentTag;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import com.magnamedia.workflow.type.PurchasingToDoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Mohammad Nosairat (Feb 03, 2021)
 */
@Transactional
@RequestMapping("/purchasing-manager")
@RestController
public class PurchasingManagerController extends BaseController {
    @Autowired
    PurchasingToDoRepository purchasingToDoRepository;
    @Autowired
    MaintenanceRequestRepository maintenanceRequestRepository;
    @Autowired
    PurchaseItemRepository purchaseItemRepository;
    @Autowired
    SupplierRepository supplierRepository;
    @Autowired
    GetBestSupplierStep getBestSupplierStep;
    @Autowired
    GetBetterSupplierStep getBetterSupplierStep;
    @Autowired
    PurchaseOrderRepository purchaseOrderRepository;
    @Autowired
    AttachementRepository attachementRepository;
    @Autowired
    PurchasingStep purchasingStep;
    @Autowired
    ExpenseRequestTodoController expenseRequestTodoController;
    @Autowired
    StockKeeperToDoService stockKeeperToDoService;

    @NoPermission
    @RequestMapping("/get-manager-requests-list")
    public ResponseEntity<?> getManagerRequestsList() {
        List<PurchasingManagerListDto> purchasingToDos = purchasingToDoRepository
                .findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseIn(Arrays.asList(
                        PurchasingToDoType.PM_GET_BEST_SUPPLIER.toString(),
                        PurchasingToDoType.PM_PURCHASE.toString(),
                        PurchasingToDoType.PM_GET_BETTER_SUPPLIER.toString()
                )).stream().map(PurchasingManagerListDto::new).collect(Collectors.toList());
        List<PurchasingManagerListDto> maintenances = maintenanceRequestRepository
                .findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseIn(Arrays.asList(
                        MaintenanceRequestType.PURCHASE_MANAGER_GET_BETTER_PRICE.toString(),
                        MaintenanceRequestType.PURCHASE_MANAGER_GET_PRICE.toString()
                )).stream().map(PurchasingManagerListDto::new).collect(Collectors.toList());
        purchasingToDos.addAll(maintenances);
        List<PurchasingManagerListDto> items = purchasingToDos.stream().sorted(Comparator.comparing(PurchasingManagerListDto::getCreationDate)).collect(Collectors.toList());
        return ResponseEntity.ok(items);
    }

    @NoPermission
    @RequestMapping("/get-suppliers-request-items/{purchasingToDoId}")
    public ResponseEntity<?> getSupplierRequestsList(@PathVariable("purchasingToDoId") PurchasingToDo purchasingToDo) {
        List<PurchaseItemDtoForGetSupplier> items = purchaseItemRepository
                .findByPurchasingToDo(purchasingToDo)
                .stream().map(PurchaseItemDtoForGetSupplier::new)
                .collect(Collectors.toList());
        return ResponseEntity.ok(items);
    }

    @Transactional
    @PreAuthorize("hasPermission('purchasing-manager','approve-best-supplier-step')")
    @PostMapping("/approve-best-supplier-step/{purchasingToDoId}")
    public ResponseEntity<?> approveBestSupplierStep(@PathVariable("purchasingToDoId") PurchasingToDo purchasingToDo,
                                                     @RequestBody List<PurchaseItem> purchaseItems) {
        for (PurchaseItem item : purchaseItems) {
            PurchaseItem entity = purchaseItemRepository.findOne(item.getId());
            // ACC-7037 give the user the ability to add any number without any validation rule.
            // validateQuantity(entity, entity.getQuantity(), item.getQuantity(), item.getPackageSize());

            entity.setQuantity(item.getQuantity());
            entity.setPackagePrice(item.getPackagePrice());
            entity.setPackageSize(item.getPackageSize());
            entity.calculateUnitPrice();
            entity.setCurrentSupplier(supplierRepository.findOne(item.getCurrentSupplier().getId()));
            purchaseItemRepository.save(entity);
        }
        getBestSupplierStep.onDone(purchasingToDo);
        purchasingToDoRepository.save(purchasingToDo);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasPermission('purchasing-manager','get-recheck-suppliers-request-items')")
    @RequestMapping("/get-recheck-suppliers-request-items/{purchasingToDoId}")
    public ResponseEntity<?> getRecheckSupplierRequestsList(@PathVariable("purchasingToDoId") PurchasingToDo purchasingToDo) {
        List<PurchaseItemDtoForGetSupplier> items = purchaseItemRepository
                .findByPurchasingToDoAndSupplierStatus(purchasingToDo, PurchaseItemSupplierStatus.SEND_GET_BETTER_PRICES)
                .stream().map(PurchaseItemDtoForGetSupplier::new)
                .collect(Collectors.toList());
        return ResponseEntity.ok(items);
    }

    @Transactional
    @PreAuthorize("hasPermission('purchasing-manager','approve-get-better-supplier-step')")
    @PostMapping("/approve-get-better-supplier-step/{purchasingToDoId}")
    public ResponseEntity<?> approveGetBetterSupplierStep(@PathVariable("purchasingToDoId") PurchasingToDo purchasingToDo,
                                                          @RequestBody List<PurchaseItem> purchaseItemsReq) {
        for (PurchaseItem item : purchaseItemsReq) {
            PurchaseItem entity = purchaseItemRepository.findOne(item.getId());
            entity.setManagerNotes(item.getManagerNotes());
            entity.setQuantity(item.getQuantity());
            entity.setPackagePrice(item.getPackagePrice());
            entity.setPackageSize(item.getPackageSize());
            entity.calculateUnitPrice();
            entity.setCurrentSupplier(supplierRepository.findOne(item.getCurrentSupplier().getId()));
            entity.setSupplierStatus(PurchaseItemSupplierStatus.PENDING);
            purchaseItemRepository.save(entity);
        }
        getBetterSupplierStep.onDone(purchasingToDo);
        purchasingToDoRepository.save(purchasingToDo);
        return ResponseEntity.ok().build();
    }

    private void validateQuantity(PurchaseItem entity, BigDecimal originQuantity, BigDecimal newQuantity, BigDecimal newPackageSize) {
        if (originQuantity.subtract(newQuantity).compareTo(newPackageSize) > 0)
            throw new RuntimeException("quantity validation error for item " + entity.getItem().getName());
        if (newQuantity.subtract(originQuantity).compareTo(newPackageSize) > 0)
            throw new RuntimeException("quantity validation error for item " + entity.getItem().getName());
        if (newQuantity.remainder(newPackageSize).compareTo(BigDecimal.ZERO) != 0) {
            throw new RuntimeException("quantity validation error for item " + entity.getItem().getName());
        }
    }

    @NoPermission
    @RequestMapping("/get-purchase-orders-order-cycle-name/{purchasingToDoId}")
    public ResponseEntity<?> getPurchaseOrdersOrderCycleName(@PathVariable("purchasingToDoId") PurchasingToDo purchasingToDo) {
        //2021-02-14 2021-02-20 // February
        String result = "";
        String name = purchasingToDo.getOrderCycleName();
        if (name != null) {
            String[] split = name.split(" ");
            if (split.length == 1) {
                result = name;
            } else {
                result = "form " + split[0] + " to " + split[1];
            }
        }
        String finalResult = result;
        return ResponseEntity.ok(new Object() {
            public String label = finalResult;
        });
    }

    @NoPermission
    @RequestMapping("/get-purchase-orders-to-purchase-step/{purchasingToDoId}")
    public ResponseEntity<?> getPurchaseOrdersToPurchaseStep(@PathVariable("purchasingToDoId") PurchasingToDo purchasingToDo) {
        List<PurchaseOrderDto> items = purchaseOrderRepository
                .findByPurchasingToDo(purchasingToDo)
                .stream().filter(o -> o.getStatus().equals(PurchaseOrderStatus.PENDING_PURCHASING))
                .map(PurchaseOrderDto::new)
                .collect(Collectors.toList());
        return ResponseEntity.ok(items);
    }

    @PreAuthorize("hasPermission('purchasing-manager','cancel-item-form-purchase-order-in-purchase-step')")
    @RequestMapping("/cancel-item-form-purchase-order-in-purchase-step/{purchasingItemId}")
    public ResponseEntity<?> cancelItemFromOrderInPurchaseStep(@PathVariable("purchasingItemId") PurchaseItem purchaseItem) {
        purchaseItem.setItemInOrderStatus(PurchaseItemInOrderStatus.CANCELED);
        purchaseItemRepository.save(purchaseItem);
        return ResponseEntity.ok().build();
    }

    @NoPermission
    @RequestMapping("/get-bill-info-for-purchase/{purchaseOrderId}")
    public ResponseEntity<?> purchaseInOneBill(@PathVariable("purchaseOrderId") PurchaseOrder entity) {
        PurchaseBillInfoDto dto = new PurchaseBillInfoDto();
        dto.setId(entity.getId());
        dto.setPaymentMethod(entity.getSupplier().getPaymentMethod());
        dto.setTaxable(entity.getSupplier().getVatRegistered());
        dto.setTotalItemsCost(entity.calculateTotalItemsCost());
        return ResponseEntity.ok(dto);
    }

    @Autowired
    EmailTemplateService emailTemplateService;

    //call when press purchase in on bill
    @NoPermission
    @PostMapping("/purchase-in-one-bill/{purchaseOrderId}")
    public ResponseEntity<?> purchaseInOneBill(@PathVariable("purchaseOrderId") PurchaseOrder entity,
                                               @RequestBody PurchaseBillInfoDto request) {
        if (entity.getStatus() == null || entity.getStatus().equals(PurchaseOrderStatus.PENDING_PAYMENT))
            throw new RuntimeException("this order is already purchased");

        if (entity.calculateTotalItemsCost().subtract(request.getTotalItemsCost()).compareTo(BigDecimal.ZERO) != 0) {
            sendPurchaseOrderCostEditedMail(entity, request);
        }
        entity = saveEntityFromRequestAndUpdateStatus(entity, request);
        ExpenseRequestTodo expenseRequest = createExpenseRequestForOrder(entity);

        entity.setExpenseRequestTodo(expenseRequest);
        purchaseOrderRepository.save(entity);
        stockKeeperToDoService.businessAfterPurchaseOrderGoToPayment(entity);
        expenseRequest.createPayment();

        doneStepIfThereIsNoMorePendingOrders(entity);
        return ResponseEntity.ok().build();
    }

    private void sendPurchaseOrderCostEditedMail(
            PurchaseOrder entity, PurchaseBillInfoDto request) {

        Map<String, String> pars = new HashMap<String, String>() {{
            put("supplier_name", entity.getSupplier().getName());
            put("category", entity.getPurchasingToDo().getCategory().getName());
            put("date", new Date().toString());
            put("old_value", entity.calculateTotalItemsCost().toString());
            put("new_value", request.getTotalItemsCost().toString());
        }};

        emailTemplateService.sendExpenseRequestTodoEmail(
                Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PURCHASE_CONTROLLER_MAIL),
                "Purchase Order Cost Edited", "PURCHASE_ORDER_COST_EDITED", pars);
    }

    private ExpenseRequestTodo createExpenseRequestForOrder(PurchaseOrder entity) {
        ExpenseRequestTodo expenseRequestTodo = new ExpenseRequestTodo();
        expenseRequestTodo.setExpense(entity.getPurchasingToDo().getCategory().getExpense());
        expenseRequestTodo.setStatus(ExpenseRequestStatus.PENDING_PAYMENT);
        expenseRequestTodo.setAmount(entity.getBillAmount().doubleValue());
        expenseRequestTodo.setPaymentMethod(entity.getSupplier().getPaymentMethod());
        if (entity.getSupplier().getPaymentMethod() == null) {
            throw new RuntimeException("The payment method of this supplier is not specified");
        }
        expenseRequestTodo.setBeneficiaryType(ExpenseBeneficiaryType.SUPPLIER);
        expenseRequestTodo.setBeneficiaryId(entity.getSupplier().getId());
        expenseRequestTodo.setVatAmount(entity.getVatAmount() != null ? entity.getVatAmount().doubleValue() : null);
        expenseRequestTodo.setExpenseRequestType(ExpenseRequestType.PURCHASING);

        Attachment invoice = AttachmentHelper.getRequestAttachment(entity, AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString());
        if(invoice != null ) {
            Attachment requestInvoice = Storage.cloneTemporary(invoice, AttachmentTag.EXPENSE_REQUEST_INVOICE.toString());
            expenseRequestTodo.addAttachment(requestInvoice);
        }

        Attachment vatInvoice = AttachmentHelper.getRequestAttachment(entity, AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString());
        if (vatInvoice != null) {
            Attachment requestVatInvoice = Storage.cloneTemporary(vatInvoice, AttachmentTag.EXPENSE_REQUEST_VAT_INVOICE.toString());
            expenseRequestTodo.addAttachment(requestVatInvoice);
        }

        // ACC-3232 9
        expenseRequestTodo.setOrder(entity);
        expenseRequestTodoController.createEntity(expenseRequestTodo);
        return expenseRequestTodo;
    }

    private void doneStepIfThereIsNoMorePendingOrders(@PathVariable("purchaseOrderId") PurchaseOrder entity) {
        PurchasingToDo purchasingToDo = entity.getPurchasingToDo();
        List<PurchaseOrder> orders = purchaseOrderRepository.findByPurchasingToDoAndStatus(purchasingToDo, PurchaseOrderStatus.PENDING_PURCHASING);
        if (orders.size() == 0) {
            purchasingStep.onDone(purchasingToDo);
            purchasingToDoRepository.save(purchasingToDo);
        }
    }

    private PurchaseOrder saveEntityFromRequestAndUpdateStatus(PurchaseOrder entity, PurchaseBillInfoDto request) {
        entity.setBillAmount(request.getTotalBillAmount());
        entity.setVatAmount(request.getVatAmount());
        entity.setItemsCost(request.getTotalItemsCost());
        entity.setDeliveryService(request.getDeliveryService());
        entity.setTaxable(request.getTaxable());
        for (Attachment attachment : request.getAttachments()) {
            entity.addAttachment(attachementRepository.findOne(attachment.getId()));
        }
        entity.setStatus(PurchaseOrderStatus.PENDING_PAYMENT);
        return entity;
    }

    @Autowired
    ConsumptionRateService consumptionRateService;

    @RequestMapping("/test/{purchaseItemId}")
    public ResponseEntity<?> test(@PathVariable("purchaseItemId") PurchaseItem purchasingToDo) {
        BigDecimal s = consumptionRateService.calculateTheoreticalConsumption(purchasingToDo);
        return ResponseEntity.ok(s);
    }
}

package com.magnamedia.controller;

import com.google.common.collect.Iterables;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.Aggregate;
import com.magnamedia.core.helper.AggregateQuery;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.HousemaidTransaction;
import com.magnamedia.entity.projection.HousemaidTransactionProjection;
import com.magnamedia.extra.AccountingPage;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.extra.TransactionPage;
import com.magnamedia.module.type.VatType;
import com.magnamedia.repository.HousemaidTransactionRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Apr 18, 2020
 */
@RequestMapping("/housemaidtransactions")
@RestController
public class HousemaidTransactionController extends BaseRepositoryController<HousemaidTransaction> {

    @Autowired
    private HousemaidTransactionRepository housemaidTransactionRepository;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Override
    public BaseRepository<HousemaidTransaction> getRepository() {
        return housemaidTransactionRepository;
    }

    @PreAuthorize("hasPermission('transactions','advancesearch')")
    @RequestMapping(value = "/page/advancesearch",
            method = RequestMethod.POST)
    public ResponseEntity<?> advanceSearch(Pageable pageable,
                                           Sort sort,
                                           @RequestBody List<FilterItem> filters) {

        SelectQuery<HousemaidTransaction> query = new SelectQuery<>(HousemaidTransaction.class);
        //Jirra ACC-961
        SelectQuery<HousemaidTransaction> inQuery = new SelectQuery<>(HousemaidTransaction.class);
        SelectQuery<HousemaidTransaction> outQuery = new SelectQuery<>(HousemaidTransaction.class);
        //Joins
        query.leftJoin("transaction.fromBucket");
        query.leftJoin("transaction.toBucket");
        query.leftJoin("transaction.expense");
        query.leftJoin("transaction.revenue");
        //Process Filters
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(HousemaidTransaction.class));
        }
        SelectFilter inFilter = new SelectFilter(selectFilter);
        SelectFilter outFilter = new SelectFilter(selectFilter);

        query.filterBy(selectFilter);
        inQuery.filterBy(inFilter);
        outQuery.filterBy(outFilter);
        inQuery.filterBy("transaction.vatType", "=", VatType.IN);
        outQuery.filterBy("transaction.vatType", "=", VatType.OUT);

        AggregateQuery aggQuery = new AggregateQuery(query, Aggregate.Sum, "transaction.amount");
        AggregateQuery inAggQuery = new AggregateQuery(inQuery, Aggregate.Sum, "transaction.vatAmount");
        AggregateQuery outAggQuery = new AggregateQuery(outQuery, Aggregate.Sum, "transaction.vatAmount");
        Double balanceSum = aggQuery.execute().doubleValue();
        Double inBalanceSum = inAggQuery.execute().doubleValue();
        Double outBalanceSum = outAggQuery.execute().doubleValue();

        //Jirra ACC-1147
        inBalanceSum = Math.floor(inBalanceSum * 10) / 10;
        outBalanceSum = Math.floor(outBalanceSum * 10) / 10;
        //Sorting
        if (pageable.getSort() == null
                || Iterables.isEmpty(pageable.getSort())) {
            query.sortBy("transaction.date", false, true);
        }
        PageImpl s = (PageImpl) query.execute(pageable).map(obj
                -> projectionFactory.createProjection(
                        HousemaidTransactionProjection.class, obj));

        AccountingPage accountingPageResult =
                new TransactionPage(s.getContent(), pageable, s.getTotalElements(),
                        balanceSum, inBalanceSum, outBalanceSum);

        return new ResponseEntity<>(accountingPageResult, HttpStatus.OK);
    }

}
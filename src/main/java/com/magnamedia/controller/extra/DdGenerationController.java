package com.magnamedia.controller.extra;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.extra.DD_Monthly_CSV;
import com.magnamedia.extra.DD_OneTime_CSV;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.ConcurrentModificationHelper;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.module.type.ContractPaymentTermReason;
import com.magnamedia.module.type.DirectDebitType;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.ContractPaymentTermServiceNew;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import static com.magnamedia.controller.ContractPaymentTermController.FILE_TAG_PAYMENTS_RECEIPT;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Dec 17, 2020
 *         Jirra ACC-2906
 */

@RequestMapping("/dd-generation")
@RestController
public class DdGenerationController extends BaseController {
    protected static final Logger logger = Logger.getLogger(DdGenerationController.class.getName());

    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    @Autowired
    private ContractRepository contractRepo;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private Utils utils;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ContractPaymentTermRepository cptRepo;

    //Jirra ACC-2906
    @PreAuthorize("hasPermission('dd-generation','generateNewDDs/One_Time')")
    @RequestMapping(value = "/generateNewDDs/One_Time", method = RequestMethod.POST)
    public ResponseEntity generateNewOneTimeDDs(@RequestParam(name = "file", required = false) MultipartFile csvFile) throws Exception {
        String[] columns = new String[]{"Contract ID", "DD start date", "DD end date", "DD Amount", "DD type", "Payment type"};
        List<DD_OneTime_CSV> ddConfigList = CsvHelper.readCsv(utils.getInputStreamFromAttachmentOrMultiPartFile(csvFile), DD_OneTime_CSV.class, columns, true);

        if (ddConfigList == null) throw new RuntimeException("no DDs to be generated");

        Long time = new Date().getTime();

        for (DD_OneTime_CSV ddConfig : ddConfigList) {
            backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                    "Accounting_DD_Generating_OneTime_DDs" + ddConfig.getContractId() + "_" + time,
                    "ddGenerationController",
                    "accounting",
                    "processOneTime",
                    "Contract",
                    ddConfig.getContractId(),
                    true,
                    false,
                    new Class<?>[]{Map.class},
                    new Object[]{objectMapper.convertValue(ddConfig, Map.class)});
        }

        return okResponse();
    }

    //Jirra ACC-2962
    @PreAuthorize("hasPermission('dd-generation','generateNewDDs/Monthly')")
    @PostMapping("/generateNewDDs/Monthly")
    public ResponseEntity generateNewMonthlyDDs(
            @RequestParam(name = "file", required = false) MultipartFile csvFile) throws Exception {

        String[] columns = new String[]{"Contract ID", "DD start date", "DD end date", "DD Amount", "Discount", "Discount Effective After"};
        List<DD_Monthly_CSV> ddConfigList = CsvHelper.readCsv(utils.getInputStreamFromAttachmentOrMultiPartFile(csvFile), DD_Monthly_CSV.class, columns, true);

        if (ddConfigList == null) throw new RuntimeException("no DDs to be generated");

        Long time = new Date().getTime();

        for (DD_Monthly_CSV ddConfig : ddConfigList) {
            backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                    "Accounting_DD_Generating_Monthly_DDs_" + ddConfig.getContractId() + "_" + time,
                    "ddGenerationController",
                    "accounting",
                    "processMonthly",
                    "Contract",
                    ddConfig.getContractId(),
                    true,
                    false,
                    new Class<?>[]{Map.class},
                    new Object[]{objectMapper.convertValue(ddConfig, Map.class)});
        }

        return okResponse();
    }

    @Transactional
    public void processOneTime(Map map) throws Exception {
        DD_OneTime_CSV ddConfig = objectMapper.convertValue(map, DD_OneTime_CSV.class);
        String concurrentModificationLockingKey = DdGenerationController.class.getSimpleName() + "_GeneratingNewDDs_" + Contract.class.getSimpleName();

        ConcurrentModificationHelper.lockOrThrowException(concurrentModificationLockingKey, ddConfig.getContractId());
        try {
            Contract contract = contractRepo.findOne(ddConfig.getContractId());

            PicklistItem paymentType = Setup.getItem("TypeOfPayment",
                    ddConfig.getDdTypeAsEnum() != null && ddConfig.getDdTypeAsEnum().equals(DirectDebitType.ONE_TIME) ? 
                            ddConfig.getPaymentType() : "monthly_payment");

            contractPaymentTermServiceNew.addNewDD(contract, ddConfig.getDdStartDate(), ddConfig.getDdEndDate(),
                    null, null, null,
                    ddConfig.getDdAmount(), null, ddConfig.getDdTypeAsEnum(),
                    paymentType, true, null, false, false, true, null, true);

            ConcurrentModificationHelper.unLock(concurrentModificationLockingKey, ddConfig.getContractId());
        } catch (Exception e) {
            ConcurrentModificationHelper.unLock(concurrentModificationLockingKey, ddConfig.getContractId());

            throw e;
        }
    }

    @Transactional
    public void processMonthly(Map map) throws Exception {
        DD_Monthly_CSV ddConfig = objectMapper.convertValue(map, DD_Monthly_CSV.class);
        String concurrentModificationLockingKey = DdGenerationController.class.getSimpleName() +
                "_GeneratingNewDDs_" + Contract.class.getSimpleName();
        ConcurrentModificationHelper.lockOrThrowException(concurrentModificationLockingKey, ddConfig.getContractId());

        try {
            Contract contract = contractRepo.findOne(ddConfig.getContractId());
            createNewCPT(contract, ddConfig.getDdAmount(), ddConfig.getDiscount(), ddConfig.getDiscountEffectiveAfter());

            PicklistItem paymentType = Setup.getItem("TypeOfPayment", "monthly_payment");

            contractPaymentTermServiceNew.addNewDD(contract, ddConfig.getDdStartDate(), ddConfig.getDdEndDate(),
                    null, null, null,
                    ddConfig.getDdAmount(), null, DirectDebitType.MONTHLY,
                    paymentType, true, null, false, false, true, null, true);

            ConcurrentModificationHelper.unLock(concurrentModificationLockingKey, ddConfig.getContractId());
        } catch (Exception e) {
            ConcurrentModificationHelper.unLock(concurrentModificationLockingKey, ddConfig.getContractId());

            throw e;
        }
    }

    private ContractPaymentTerm createNewCPT(
            Contract contract,
            Double monthlyPayment,
            Double discount,
            Integer discountEffectiveAfter) {

        ContractPaymentTerm currentCPT = contract.getActiveContractPaymentTerm();
        if (currentCPT == null) return null;

        ContractPaymentTerm newCPT = new ContractPaymentTerm();
        newCPT.setContract(contract);
        newCPT.setIsProRated(currentCPT.isProRated());
        newCPT.setHousemaid(currentCPT.getHousemaid());
        newCPT.setPackageType(currentCPT.getPackageType());
        newCPT.setCreditNote(currentCPT.getCreditNote());
        newCPT.setCreditNoteMonths(currentCPT.getCreditNoteMonths());

        if (currentCPT.getPaymentTermConfig() != null) {
            newCPT.setPaymentTermConfigWithValues(currentCPT.getPaymentTermConfig());
        } else {
            newCPT.setPaymentTermConfig(currentCPT.getPaymentTermConfig());
            newCPT.setNationality(currentCPT.getNationality());
            newCPT.setContractProspectType(currentCPT.getContractProspectType());
            newCPT.setType(currentCPT.getType());
            newCPT.setAgencyFee(currentCPT.getAgencyFee());
            newCPT.setIsRemote(currentCPT.getIsRemote());
            newCPT.setWeeklyAmount(currentCPT.getWeeklyAmount());
            newCPT.setDailyRateAmount(currentCPT.getDailyRateAmount());
            newCPT.setCptFamily(currentCPT.getCptFamily());
        }

        newCPT.setMonthlyPayment(monthlyPayment);
        newCPT.setDiscount(discount);
        newCPT.setDiscountEffectiveAfter(discountEffectiveAfter);
        newCPT.setBank(currentCPT.getBank());
        newCPT.setBankName(currentCPT.getBankName());
        newCPT.setBankInfoTextBased(currentCPT.getBankInfoTextBased());
        newCPT.setIbanNumber(currentCPT.getIbanNumber());
        newCPT.setEid(currentCPT.getEid());
        newCPT.setAccountName(currentCPT.getAccountName());
        newCPT.setReason(ContractPaymentTermReason.AMEND_PAYMENTS);

        if(currentCPT.getWeeklyAmount() > 0)
            newCPT.setWeeklyAmount(currentCPT.getWeeklyAmount());
        if(currentCPT.getDailyRateAmount() > 0)
            newCPT.setDailyRateAmount(currentCPT.getDailyRateAmount());
//        else if(cptRepo.existsByContractAndWeeklyAmountGreaterThan0(contract)) {
//            PaymentTermConfig ptc = contractPaymentTermServiceNew.findSuitableConfig(
//                    newCPT.getNationality(), newCPT.getContractProspectType(),
//                    newCPT.getType(), newCPT.getPackageType());
//            newCPT.setWeeklyAmount(ptc.getWeeklyAmount());
//        }

        currentCPT.getAttachments().forEach(att -> {
            if (!att.getTag().equals(FILE_TAG_PAYMENTS_RECEIPT))
                newCPT.addAttachment(att);
        });
        currentCPT.setActive(Boolean.FALSE);
        cptRepo.save(currentCPT);

        newCPT.setIsActive(Boolean.TRUE);
        return cptRepo.save(newCPT);
    }
}
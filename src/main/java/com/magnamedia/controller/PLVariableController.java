package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.PLVariableNode;
import com.magnamedia.repository.BasePLVariableBucketRepository;
import com.magnamedia.repository.BasePLVariableRepository;
import com.magnamedia.repository.PLVariableBucketRepository;
import com.magnamedia.repository.PLVariableNodeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>> Created on Oct 6, 2018
 */
@RequestMapping("/plvariables")
@RestController
public class PLVariableController extends BasePLVariableController<PLVariableNode> {

	@Autowired
	private PLVariableNodeRepository pLVariableRepository;

	@Override
	public BasePLVariableRepository<PLVariableNode> getRepository() {
		return pLVariableRepository;
	}

	@Override
	public BasePLVariableBucketRepository getBasePLVariableBucketRepository() {
		return Setup.getRepository(PLVariableBucketRepository.class);
	}
}

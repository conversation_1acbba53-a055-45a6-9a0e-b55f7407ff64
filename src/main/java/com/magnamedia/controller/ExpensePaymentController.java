package com.magnamedia.controller;

import com.aspose.words.ConvertUtil;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.EnableSwaggerMethod;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.annotation.caching.ApiCacheable;
import com.magnamedia.core.controller.workflow.WorkflowController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.dto.*;
import com.magnamedia.entity.projection.ExpensePaymentNightReviewProjection;
import com.magnamedia.entity.projection.ExpensePaymentSecureInfoProjection;
import com.magnamedia.entity.projection.PayInvoiceProjection;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.AttachmentHelper;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import com.magnamedia.workflow.service.ExpensePaymentFlow;
import com.magnamedia.workflow.service.expensepayment.ExpensePaymentInCreditCardHolderStep;
import com.magnamedia.workflow.service.expensepayment.ExpensePaymentInPendingInvoiceStep;
import com.magnamedia.workflow.service.expensepayment.ExpensePaymentInPendingPaymentCashierStep;
import com.magnamedia.workflow.type.AttachmentTag;
import com.magnamedia.workflow.type.ExpensePaymentToDoType;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.controller.PaymentOrderController.CASHIER_POSITION_CODE;

/**
 * Mohammad Nosairat (Jan 20, 2021)
 */
@Transactional
@RestController
@RequestMapping("/expense-payment")
public class ExpensePaymentController extends WorkflowController<ExpensePayment, ExpensePaymentFlow> {
    Logger logger = Logger.getLogger(ExpensePaymentController.class.getName());

    @Autowired
    private ExpensePaymentService expensePaymentService;
    @Autowired
    private ExpensePaymentRepository expensePaymentRepository;
    @Autowired
    private ExpenseRequestTodoRepository expenseRequestTodoRepository;
    @Autowired
    private AttachementRepository attachementRepository;
    @Autowired
    private ExpenseRepository expenseRepository;
    @Autowired
    private ExpensePaymentInPendingPaymentCashierStep expensePaymentInPendingPaymentCashierStep;
    @Autowired
    private ExpensePaymentInPendingInvoiceStep expensePaymentInPendingInvoiceStep;
    @Autowired
    private ExpensePaymentInCreditCardHolderStep expensePaymentInCreditCardHolderStep;
    @Autowired
    private WordTemplateService wordTemplateService;
    @Autowired
    private BucketRepository bucketRepository;
    @Autowired
    private ExpensePaymentController selfCtrl;

    @Override
    protected SelectFilter filter(SelectFilter selectFilter, String s, List<String> list, Map<String, String> map) {
        return selectFilter;
    }


    @Transactional
    @Override
    public ResponseEntity<?> createEntity(ExpensePayment entity) {
        if (entity.getType() == null) {
            entity.setType(ExpensePaymentType.PAY);
        }

        List<Bucket> buckets;
        switch (entity.getMethod()) {
            case BANK_TRANSFER:
                buckets = bucketRepository.findByBucketType(BucketType.BANK_ACCOUNT);
                if (buckets.size() != 1) {
                    throw new RuntimeException("BANK_ACCOUNT buckets number is " + buckets.size());
                } else {
                    entity.setFromBucket(buckets.get(0));
                }
                break;
            case MONEY_TRANSFER:
                if (entity.getReplenishmentTodo() == null) {
                    buckets = bucketRepository.findByBucketType(BucketType.MONEY_TRANSFER);
                    if (buckets.size() != 1) {
                        throw new RuntimeException("MONEY_TRANSFER buckets number is " + buckets.size());
                    } else {
                        entity.setFromBucket(buckets.get(0));
                    }
                }
                break;
            default:
                break;
        }

        if(entity.getExpenseToPost() != null && entity.getExpenseToPost().getId() == null &&
                entity.getExpenseToPost().getCode() != null) {

            Expense ex = expenseRepository.findByCodeAndDeletedFalse(entity.getExpenseToPost().getCode());
            entity.setExpenseToPost(ex);
        }

        ResponseEntity<?> result = super.createEntity(entity);

        List<ExpenseRequestTodo> expenseRequestToDos = entity.getExpenseRequestTodos();
        for (ExpenseRequestTodo req : expenseRequestToDos) {
            ExpenseRequestTodo reqEntity = expenseRequestTodoRepository.findOne(req.getId());
            reqEntity.setExpensePayment(entity);
            expenseRequestTodoRepository.save(reqEntity);
        }

        expensePaymentService.initExpensePaymentFlow(entity);
        return result;
    }


    @Override
    public BaseRepository<ExpensePayment> getRepository() {
        return expensePaymentRepository;
    }

    @NoPermission
    @RequestMapping("/get-pending-cashier-payments")
    public ResponseEntity<?> getPendingCashierPayments(Pageable pageable) {

        User user = CurrentRequest.getUser();
        if (user == null || !user.hasPosition(CASHIER_POSITION_CODE)) {
            return new ResponseEntity<>("You don't have the required permission", HttpStatus.UNAUTHORIZED);
        }

        List<ExpensePayment> expensePayments = expensePaymentRepository
                .findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseContaining(
                        ExpensePaymentToDoType.TO_DO_IN_PENDING_PAYMENT_CASHIER_SCREEN.toString());

        List<Bucket> buckets = bucketRepository.findByHolderAndBucketType(CurrentRequest.getUser(), BucketType.CASH_BOX);
        Long bucketId = 0L;
        if (buckets.size() > 0) {
            bucketId = buckets.get(0).getId();
        }
        Long finalBucketId = bucketId;

        expensePayments = expensePayments.stream().filter(t -> t.getType() != ExpensePaymentType.COLLECT
                || (t.getToBucket() != null && t.getToBucket().getId().equals(finalBucketId))).collect(Collectors.toList());

        List<ExpensePaymentCashierDto> returned = expensePayments.stream()
                .filter(ep -> ep.getMethod().equals(ExpensePaymentMethod.CASH)
                        && ep.getStatus().equals(ExpensePaymentStatus.PENDING))
                .map(ExpensePaymentCashierDto::createExpensePaymentCashierDtoForCashier)
                .collect(Collectors.toList());

        Page<ExpensePaymentCashierDto> page = getPage(returned, pageable);

        return ResponseEntity.ok(page);
    }

    @NoPermission
    @RequestMapping("/get-pending-invoice-payments")
    public ResponseEntity<?> getPendingInvoicePayments(Pageable pageable) {

        User user = CurrentRequest.getUser();
        if (user == null || !user.hasPosition(CASHIER_POSITION_CODE)) {
            return new ResponseEntity<>("You don't have the required permission", HttpStatus.UNAUTHORIZED);
        }

//        List<ExpensePayment> expensePayments = expensePaymentRepository.findCashierPendingInvoicePayments();
        List<ExpensePayment> expensePayments = expensePaymentRepository
                .findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseContaining(
                        ExpensePaymentToDoType.TO_DO_IN_PENDING_INVOICE_SCREEN.toString()
                );
        List<ExpensePaymentCashierDto> returned = expensePayments.stream()
                .filter(ep -> !ep.isDoneInPendingInvoiceStep()
                        && ep.getMethod().equals(ExpensePaymentMethod.CASH)
                        && ep.getStatus().equals(ExpensePaymentStatus.PAID_PENDING_INVOICE)
                        && (ep.getConfirmed() == null || ep.getConfirmed().equals(Boolean.FALSE)))
                .map(ExpensePaymentCashierDto::createExpensePaymentDtoForPendingInvoice)
                .collect(Collectors.toList());

        Page<ExpensePaymentCashierDto> page = getPage(returned, pageable);

        return ResponseEntity.ok(page);
    }

    private <T> Page<T> getPage(List<T> returned, Pageable pageable) {
        final int start = (int)pageable.getOffset();
        final int end = Math.min((start + pageable.getPageSize()), returned.size());
        return new PageImpl<>(returned.subList(start, end), pageable, returned.size());
    }

    private <T> Page<T> getPage(List<T> finalList, Long totalElement, Pageable pageable) {
        return new PageImpl<>(finalList, pageable, totalElement);
    }

    @NoPermission
    @PostMapping("/cashier-pay-money/{paymentId}/{signature}")
    public ResponseEntity<?> cashierPayMoney(@PathVariable("paymentId") ExpensePayment entity,
                                             @RequestBody ExpensePayment paymentReq,
                                             @PathVariable("signature") Attachment signature) {

        Bucket bucket = bucketRepository.findFirstByHolderAndBucketType(CurrentRequest.getUser(), BucketType.CASH_BOX);
        if (bucket == null) throw new BusinessException("No cash bucket assigned to your user");

        entity.setFromBucket(bucket);

        entity.setPaidBy(CurrentRequest.getUser());
        entity.setPaymentDate(new Date());

        onCashierSubmit(entity, paymentReq);

        entity.addAttachment(signature);

        Attachment receiptFile = generateReceiptFile(entity, signature);
        entity.addAttachment(receiptFile);

        expensePaymentInPendingPaymentCashierStep.onDone(entity);

        expensePaymentRepository.save(entity);

        return ResponseEntity.ok(receiptFile);
    }

    private void onCashierSubmit(ExpensePayment entity, ExpensePayment paymentReq) {
        if (!entity.getRequiresInvoice()) return;

        Attachment paymentInvoice = null;
        if (paymentReq.getInvoiceAttached() != null && paymentReq.getInvoiceAttached().equals(Boolean.TRUE)) {
            Attachment expenseAttachment = entity.getAttachment(AttachmentTag.EXPENSE_REQUEST_INVOICE.toString());
            if (expenseAttachment == null) {
                logger.severe("there is no attachment with tag EXPENSE_REQUEST_INVOICE  for expense payment " + entity.getId());
                for (Attachment a : entity.getAttachments()) {
                    if (a.getTag().toUpperCase().contains("SIGNATURE")) continue;

                    logger.severe("insted of  EXPENSE_REQUEST_INVOICE the +" + a.getTag() + " is used for expense payment " + entity.getId());
                    expenseAttachment = a;
                    break;
                }
            }
            if (expenseAttachment == null)
                throw new RuntimeException("there is no invoice attached on the Expense Request.");
            paymentInvoice = Storage.cloneTemporary(expenseAttachment, AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString());
            entity.addAttachment(paymentInvoice);
        } else if (paymentReq.getInvoiceAttached() != null && paymentReq.getInvoiceAttached().equals(Boolean.FALSE)) {
            paymentInvoice = AttachmentHelper.getRequestAttachment(paymentReq, AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString());
            if (paymentInvoice != null) {
                entity.addAttachment(paymentInvoice);
            }
        }

//        entity.setTaxable(paymentReq.getTaxable());
        entity.setInvoiceAttached(paymentReq.getInvoiceAttached());

        entity.setTaxable(paymentReq.getTaxable());

        if (paymentReq.getVatAmount() != null)
            entity.setVatAmount(paymentReq.getVatAmount());

        entity.setAttachedValidVatInvoice(paymentReq.getAttachedValidVatInvoice());
        if (paymentReq.getAttachedValidVatInvoice() != null && paymentReq.getAttachedValidVatInvoice().equals(Boolean.TRUE)) {
            if (paymentInvoice != null) {
                Attachment paymentVatInvoice = Storage.cloneTemporary(paymentInvoice, AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString());
                entity.addAttachment(paymentVatInvoice);
            }
        } else {
            Attachment paymentVatInvoice = AttachmentHelper.getRequestAttachment(paymentReq, AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString());
            if (paymentVatInvoice != null) {
                entity.addAttachment(paymentVatInvoice);
            }
        }
    }

    private Attachment generateReceiptFile(ExpensePayment entity, Attachment signature) {
        Map<String, Object> parameters = new HashMap();
        {
            parameters.put("signature", signature != null ? new WordImage(Storage.getStream(signature),
                    ConvertUtil.pixelToPoint(160), ConvertUtil.pixelToPoint(47)) : "");
            parameters.put("beneficiary", entity.getBeneficiaryName() != null ? entity.getBeneficiaryName() : "");
            parameters.put("payment_amount", getPaymentAmountToReceipt(entity));
            parameters.put("paying_date", Instant.now().atZone(ZoneId.systemDefault()).toLocalDate().toString());
            parameters.put("date_of_signature", Instant.now().atZone(ZoneId.systemDefault()).toLocalDate().toString());
        }

        InputStream tmp = wordTemplateService.generateDocument("receipt_expense_payment", parameters);
        try {
            Attachment receiptAttachment = Storage.storeTemporary(entity.getBeneficiaryName() + "cachier " + Instant.now().atZone(ZoneId.systemDefault()).toLocalDate().toString() + " .pdf",
                    tmp, AttachmentTag.RECEIPT_EXPENSE_PAYMENT.toString(), false);
            return receiptAttachment;
        } finally {
            StreamsUtil.closeStream(tmp);
        }
    }

    private Object getPaymentAmountToReceipt(ExpensePayment entity) {
        if (entity.getLocalCurrencyAmount() == null) return "";

        DecimalFormat format = new DecimalFormat("0.#");
        return format.format(entity.getLocalCurrencyAmount());
    }

    @NoPermission
    @RequestMapping("/cashier-collect-money/{paymentId}/{attachmentId}")
    @Transactional
    public ResponseEntity<?> cashierCollectMoney(@PathVariable("paymentId") ExpensePayment entity,
                                                 @PathVariable("attachmentId") Attachment signature) {
        entity.setPaidBy(CurrentRequest.getUser());
        entity.setPaymentDate(new Date());
        entity.addAttachment(signature);

        Attachment receiptFile = generateReceiptFile(entity, signature);
        entity.addAttachment(receiptFile);

        expensePaymentInPendingPaymentCashierStep.onDone(entity);
        expensePaymentRepository.save(entity);
        return ResponseEntity.ok(receiptFile);
    }

    @NoPermission
    @Transactional
    @PostMapping("/cashier-collect-invoice/{paymentId}")
    public ResponseEntity<?> cashierCollectInvoice(@PathVariable("paymentId") ExpensePayment entity,
                                                   @RequestBody ExpensePayment paymentReq) {
        onCashierSubmit(entity, paymentReq);

        expensePaymentInPendingInvoiceStep.onDone(entity);
        expensePaymentRepository.save(entity);
        return ResponseEntity.ok().build();
    }

    @NoPermission
    @RequestMapping("/get-pay-invoice-list")
    public ResponseEntity<?> getPayInvoiceList(Pageable pageable) {
        List<ExpenseRequestTodo> expenseRequestTodos = expenseRequestTodoRepository
                .findByPaymentMethodAndStatusAndExpensePaymentIsNull(ExpensePaymentMethod.INVOICED, ExpenseRequestStatus.PENDING_PAYMENT);
        Map<PayInvoiceKey, List<ExpenseRequestTodo>> groupingResult = expenseRequestTodos.stream()
                .collect(Collectors.groupingBy(exp -> new PayInvoiceKey(exp.getBeneficiaryId(), exp.getExpense())));
        List<ExpensePaymentPayInvoiceDto> result = new ArrayList<>();
        groupingResult.forEach((k, v) -> {
            Double balance = v.stream().map(ExpenseRequestTodo::getAmount).reduce(0D, Double::sum);
            result.add(new ExpensePaymentPayInvoiceDto(k.beneficiaryId, k.expense.getId(), v.get(0).getBeneficiaryName(), balance));
        });

        return ResponseEntity.ok(getPage(result, pageable));
    }

    @NoPermission
    @RequestMapping(value = "/get-pay-invoice/{beneficiaryId}/{expenseId}", method = RequestMethod.POST)
    public ResponseEntity<?> getPayInvoice(
        @PathVariable("beneficiaryId") Long beneficiaryId,
        @PathVariable("expenseId") Expense expense,
        @RequestBody PayInvoiceSearchDto payInvoiceSearchDto,
        Pageable pageable) {

        Supplier supplier = Setup.getRepository(SupplierRepository.class).findOne(beneficiaryId);
        if (supplier == null)
            throw new RuntimeException("Supplier is not found!");
        // ACC-5659
        return ResponseEntity.ok(new ExpensePaymentPayInvoiceDto(
                Setup.getApplicationContext()
                        .getBean(QueryService.class)
                        .getPayInvoiceByBeneficiaryIdAndExpenseIdApi(
                                beneficiaryId, expense, payInvoiceSearchDto, pageable),
                supplier));
    }

    @NoPermission
    @RequestMapping(value = "/pay-invoice-exportToCsv/{beneficiaryId}/{expenseId}", method = RequestMethod.POST)
    @ResponseBody
    public void payInvoiceExportToCsv(
        @PathVariable("beneficiaryId") Long beneficiaryId,
        @PathVariable("expenseId") Expense expense,
        @RequestBody PayInvoiceSearchDto payInvoiceSearchDto,
        HttpServletResponse response,
        Pageable pageable) {

        Supplier supplier = Setup.getRepository(SupplierRepository.class).findOne(beneficiaryId);
        SelectQuery<ExpenseRequestTodo> selectQuery = new SelectQuery<>(ExpenseRequestTodo.class);
        selectQuery.filterBy("beneficiaryId", "=", beneficiaryId);
        selectQuery.filterBy("expense", "=", expense);
        selectQuery.filterBy("paymentMethod", "=", ExpensePaymentMethod.INVOICED);
        selectQuery.filterBy("status", "=", ExpenseRequestStatus.PENDING_PAYMENT);
        selectQuery.filterBy("expensePayment", "IS NULL", null);

        if (payInvoiceSearchDto.getFromDate() != null)
            selectQuery.filterBy("creationDate", ">=", payInvoiceSearchDto.getFromDate());
        if (payInvoiceSearchDto.getToDate() != null)
            selectQuery.filterBy("creationDate", "<", DateUtil.addDays(payInvoiceSearchDto.getToDate(), 1));
        if (payInvoiceSearchDto.getMaidName() != null && !payInvoiceSearchDto.getMaidName().isEmpty()) {
            List<Housemaid> housemaids = Setup.getRepository(HousemaidRepository.class).findByNameContains(payInvoiceSearchDto.getMaidName());
            List<Long> housemaidsIds = housemaids.stream().map(BaseEntity::getId).collect(Collectors.toList());
            selectQuery.filterBy("relatedToType", "=", ExpenseRelatedTo.ExpenseRelatedToType.MAID);
            selectQuery.filterBy("relatedToId", "in", housemaidsIds);
        }
        List<ExpenseRequestTodo> expenseRequestTodos = selectQuery.execute();

        InputStream inputStream = null;
        File excelFile;
        String fileName = (supplier != null ? supplier.getName() : "") + "Invoice " +DateUtil.formatDateDashedV2(new Date()) +".csv";
        try {
            String[] namesOrdered = {"expense", "employeeType", "employeeName", "amount", "creationDate"};

            String[] headers = {"Expense Name", "Employee’s type", "Name", "Amount", "Date"};

            excelFile = CsvHelper.generateCsv(expenseRequestTodos, PayInvoiceProjection.class,
                    headers, namesOrdered, fileName);
        } catch (IOException ex) {
            logger.log(Level.SEVERE, "payInvoiceExportToCsv Exception: " + ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        }

        try {
            inputStream = new FileInputStream(excelFile);
            if (inputStream != null) {
                createDownloadResponse(response, fileName, inputStream);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            StreamsUtil.closeStream(inputStream);
        }
    }

    @NoPermission
    @PostMapping("/pay-invoice")
    public ResponseEntity<?> getPayInvoice(@RequestBody ExpensePaymentPayInvoiceDto payInvoiceDto) {
        List<Bucket> buckets = bucketRepository.findByBucketType(BucketType.BANK_ACCOUNT);

        expensePaymentService.generateInvoicePayment(payInvoiceDto, buckets.get(0));
        return ResponseEntity.ok().build();
    }

    @NoPermission
    @RequestMapping("/get-pending-credit-card-payments")
    public ResponseEntity<?> getPendingCreditCardPayments(Pageable pageable, @RequestParam(value = "type", defaultValue = "") String type) {
        Page<ExpensePayment> expensePayments;

        switch (type){
            case "TICKETING":
                expensePayments = expensePaymentRepository.findByCreditCardHolderAndTypePage(ExpenseRequestType.TICKETING, pageable);
                break;
            default:
                expensePayments = expensePaymentRepository.findByCreditCardHolderHomePage(pageable);
                break;
        }

        List<ExpensePaymentCreditCardDto> returned = expensePayments.stream().map(ExpensePaymentCreditCardDto::new).collect(Collectors.toList());

        return ResponseEntity.ok(getPage(returned, expensePayments.getTotalElements(), pageable));
    }

    @NoPermission
    @RequestMapping("/get-credit-card-buckets")
    public ResponseEntity<?> getCreditCardBuckets(
            @RequestParam("search") String search, Pageable pageable) {

        SelectQuery<Bucket> query = new SelectQuery<>(Bucket.class);
        query.filterBy("bucketType", "=", BucketType.CREDIT_CARD);
        if (search != null && !search.isEmpty())
            query.filterBy("name", "like", "%" + search + "%");

        return ResponseEntity.ok(query.execute(pageable));
    }

    @NoPermission
    @Transactional
    @PostMapping("/pay-credit-card-payment/{paymentId}")
    public ResponseEntity<?> payCreditCardPayment(@PathVariable("paymentId") ExpensePayment entity,
                                                  @RequestBody ExpensePayment paymentReq) {

        expensePaymentService.payCreditCardPayment(entity, paymentReq, CurrentRequest.getUser());
        return ResponseEntity.ok().build();
    }


    @Autowired
    UserRepository userRepository;
    @Autowired
    AccountBalanceService accountBalanceService;

    @NoPermission
    @RequestMapping("/get-current-balance")
    public ResponseEntity<?> updateCashBox(Authentication authentication) {
        String username = authentication.getName();
        User user = userRepository.findByLoginName(username);
        List<Bucket> buckets = bucketRepository.findByHolderAndBucketType(user, BucketType.CASH_BOX);
        if (buckets == null || buckets.size() == 0)
            throw new BusinessException("No cash bucket assigned to your user");
        if (buckets.size() > 1)
            throw new BusinessException(buckets.size() + " cash bucket assigned to your user");

        //Double balanceValue = accountBalanceService.addAccountBalance(buckets.get(0));
        Bucket b = accountBalanceService.setBucketBalanceBasedOnTransaction(buckets.get(0));

        return ResponseEntity.ok(new Object() {
            public Double balance = b.getBalance();
        });
    }

    @PreAuthorize("hasPermission('expense-payment','get-modified-by-status-method-date/questioned-expenses')")
    @RequestMapping("/get-modified-by-status-method-date/questioned-expenses")
    public ResponseEntity getModifiedQuestionedPaymentsByStatusAndMethodAndDateAPI(@RequestParam ExpensePaymentMethod method,
                                                                                   Pageable pageable) {
        Date toDate = DateUtil.getDateAtHour(new Date(), Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR)));
        Date fromDate = new DateTime(toDate).minusDays(14).toDate();

        Page<ExpensePaymentRepository.IdAndRevision> page = expensePaymentRepository.getModifiedQuestionedPaymentsByStatusAndMethodAndDate(method.getValue(), fromDate, toDate, pageable);

        Page<ExpensePaymentNightReviewProjection> result = page
                .map(expensePaymentRev -> {
                    ExpensePayment expensePayment = getRepository().findOne(expensePaymentRev.getId());
                    expensePayment.setCooQuestionedPage(CooQuestion.QuestionedPage.NIGHT_REVIEW);
                    return project(expensePayment, ExpensePaymentNightReviewProjection.class);
                });

        return ResponseEntity.ok(result);
    }

    @PreAuthorize("hasPermission('expense-payment','get-modified-by-status-method-date')")
    @RequestMapping("/get-modified-by-status-method-date")
    public ResponseEntity getModifiedPaymentsByStatusAndMethodAndDateAPI(@RequestParam ExpensePaymentMethod method,
                                                                         @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                                                         @RequestParam boolean archive,
                                                                         Pageable pageable) {

        Page result = getModifiedPaymentsByStatusAndMethodAndDate(method, date, archive, pageable);
        return ResponseEntity.ok(result);
    }

    private Page<ExpensePaymentNightReviewProjection> getModifiedPaymentsByStatusAndMethodAndDate(ExpensePaymentMethod method,
                                                                                                  Date date,
                                                                                                  boolean archive,
                                                                                                  Pageable pageable) {
        Date toDate = DateUtil.getDateAtHour(date, Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR)));
        Date fromDate = new DateTime(toDate).minusDays(1).toDate();

        Page<ExpensePaymentNightReviewProjection> result = expensePaymentRepository.getModifiedPaymentsByStatusAndMethodAndDate(method.getValue(), archive, fromDate, toDate, pageable)
                .map(expensePaymentRev -> {
                    ExpensePayment expensePayment = getRepository().findOne(expensePaymentRev.getId());
                    expensePayment.setCooQuestionedPage(CooQuestion.QuestionedPage.NIGHT_REVIEW);
                    return project(expensePayment, ExpensePaymentNightReviewProjection.class);
                });

        return result;
    }

    @PreAuthorize("hasPermission('expense-payment','mark-as-done-by-coo')")
    @RequestMapping(value = "/mark-as-done-by-coo", method = RequestMethod.POST)
    public ResponseEntity markAsDoneByCooAPI(@RequestBody Long id) {

        selfCtrl.markAsDoneByCoo(id);

        return okResponse();
    }

    @PreAuthorize("hasPermission('expense-payment','mark-all-as-done-by-coo')")
    @RequestMapping(value = "/mark-all-as-done-by-coo", method = RequestMethod.POST)
    public ResponseEntity markAllAsDoneByCooAPI(@RequestParam(required = false) ExpensePaymentMethod method,
                                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                                @RequestParam boolean archive) {
        List<Long> ids = getModifiedPaymentsByStatusAndMethodAndDate(method, date, archive, PageRequest.of(0, Integer.MAX_VALUE))
                .map(element -> element.getId()).getContent();

        markAsDoneByCoo(ids);

        return okResponse();
    }

    @PreAuthorize("hasPermission('expense-payment','mark-all-as-done-by-coo')")
    @RequestMapping(value = "/mark-list-as-done-by-coo", method = RequestMethod.POST)
    public ResponseEntity markListAsDoneByCooAPI(@RequestBody List<Long> ids) {
        markAsDoneByCoo(ids);

        return okResponse();
    }

    private void markAsDoneByCoo(List<Long> ids) {
        if (ids == null) return;

        for (Long id : ids) {
            selfCtrl.markAsDoneByCoo(id);
        }
    }

    @Transactional
    public void markAsDoneByCoo(Long id) {
        if (id == null) return;

        ExpensePayment expensePayment = getRepository().findOne(id);
        expensePayment.setDoneByCoo(true);

        getRepository().save(expensePayment);
    }

    @PreAuthorize("hasPermission('expense-payment','getExpensePayment')")
    @GetMapping("/getExpensePayment/{id}")
    public ResponseEntity<?> getExpensePayment(@PathVariable("id") Long id) {

       return ResponseEntity.ok(project(
                   this.getRepository().findOne(id),
                   ExpensePaymentSecureInfoProjection.class));
    }

    //ACC-8419
    @JwtSecured
    @UsedBy(others = UsedBy.Others.New_GPT)
    @ApiCacheable
    @EnableSwaggerMethod
    @GetMapping(value = "/getMaidsLuggageCompensationExpenseRequestData")
    public ResponseEntity<?> getMaidsLuggageCompensationExpenseRequestData(
            @RequestParam(required = false) String mobileNumber,
            @RequestParam(required = false) String eidNumber,
            @RequestParam(required = false) String firstName,
            @RequestParam(required = false) String middleName,
            @RequestParam(required = false) String lastName,
            @RequestParam(required = false, name = "contractID") Long contractId) {

        return ResponseEntity.ok(expensePaymentService.getMaidsLuggageCompensationExpenseRequestData(
                mobileNumber, eidNumber, firstName, middleName, lastName, contractId));
    }
}

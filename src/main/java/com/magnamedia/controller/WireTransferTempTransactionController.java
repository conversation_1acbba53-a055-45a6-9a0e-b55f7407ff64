package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.WireTransferTempTransaction;
import com.magnamedia.module.type.WireTransferTempPaymentRelatesTo;
import com.magnamedia.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> masod <<EMAIL>>
 *         Created on Sep 22, 2020
 *         ACC-2570
 */

@RestController
@RequestMapping("/wireTransferTempTransaction")
public class WireTransferTempTransactionController extends BaseRepositoryController<WireTransferTempTransaction> {

    @Autowired
    private WireTransferTempTransactionRepository repository;
  /*  @Autowired
    private TransactionRepository transactionRepository;
    @Autowired
    private ExpectedWireTransferRepository expectedWireTransferRepository;
    @Autowired
    private PaymentController paymentController;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private BucketRepository bucketRepository;
    @Autowired
    private RevenueRepository revenueRepository;

    @PreAuthorize("hasPermission('wireTransferTempTransaction','savePayments')")
    @RequestMapping(value = "/savePayments/{relatesTo}/{relatedEntityId}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity savePayments(@RequestBody List<Payment> payments, @PathVariable("relatedEntityId") Long relatedEntityId,
                                       @PathVariable("relatesTo") String relatesTo) throws Exception {
        Transaction transaction;
        ExpectedWireTransfer expectedWireTransfer;
        List<Attachment> attachments = null;

        if (relatesTo.equals("Transaction")) {
            transaction = transactionRepository.findOne(relatedEntityId);
            attachments = transaction.getAttachments();
        }

        if (relatesTo.equals("ExpectedWireTransfer")) {
            expectedWireTransfer = expectedWireTransferRepository.findOne(relatedEntityId);
            attachments = expectedWireTransfer.getAttachments();
        }

        for (Payment payment : payments) {
            payment.setIgnorePostingEngineBR(true);
        }

        List<Payment> persistedPayments = new ArrayList();

        for (Payment payment : payments) {
            payment.setStatus(PaymentStatus.RECEIVED);
            payment.setMethodOfPayment(PaymentMethod.WIRE_TRANSFER);
            if (attachments != null) {
                for (Attachment attachment : attachments) {
                    Attachment newAttachment = Storage.storeTemporary(attachment.getName(), Storage.getStream(attachment), attachment.getTag(), Boolean.FALSE);
                    payment.addAttachment(newAttachment);
                }
            }

            persistedPayments.add(paymentRepository.findOne((Long) paymentController.creatPaymentsFromClientModule(payment).get("id")));
        }

        List<Long> tempTransactions = new ArrayList();

        for (Payment payment : persistedPayments) {
            WireTransferTempTransaction tempTransaction = createRelatedTransaction(payment);
            tempTransactions.add(repository.save(tempTransaction).getId());
        }

        return new ResponseEntity(tempTransactions, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('wireTransferTempTransaction','confirmTempTransactions')")
    @RequestMapping(value = "/confirmTempTransactions/{relatesTo}/{relatedEntityId}", method = RequestMethod.POST)
    public ResponseEntity confirmPayments(@RequestBody List<Long> tempTransactionsIDs, @PathVariable("relatedEntityId") Long relatedEntityId,
                                          @PathVariable("relatesTo") String relatesTo) {

        if (tempTransactionsIDs == null || tempTransactionsIDs.isEmpty()) {
            throw new RuntimeException("no transactions to confirm Found");
        }

        List<WireTransferTempTransaction> tempTransactions = repository.findAll(tempTransactionsIDs);

        if (tempTransactions == null || tempTransactions.isEmpty()) {
            throw new RuntimeException("no transactions to confirm Found");
        }

        updateRelatedEntity(tempTransactions.get(0), relatesTo, relatedEntityId);

        if (tempTransactions.size() > 1) {
            for (Integer index = 1; index < tempTransactions.size(); index++) {
                WireTransferTempTransaction tempTransaction = tempTransactions.get(index);
                createTransaction(tempTransaction);
            }
        }

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    private WireTransferTempTransaction createRelatedTransaction(Payment payment) {
        WireTransferTempTransaction tempTransaction = new WireTransferTempTransaction();
        tempTransaction.setPayment(payment);
        tempTransaction.setAmount(payment.getAmountOfPayment());
        tempTransaction.setVatType(VatType.OUT);
        tempTransaction.setTransactionDate(Date.valueOf(LocalDate.now()));
        tempTransaction.setPnlValueDate(Date.valueOf(LocalDate.now()));

        tempTransaction.setToBucket(bucketRepository.findByCode("BC 10"));
        tempTransaction.setVatType(VatType.OUT);
        tempTransaction.setLicense(PicklistHelper.getItemNoException(PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
        if (payment.getContract().getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))) {
            tempTransaction.setRevenue(revenueRepository.findByCode("FTR 02"));
        }

        if (payment.getContract().getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {
            tempTransaction.setRevenue(revenueRepository.findByCode("MVR 02"));
        }

        String description = "wire transfer received ";
        if (payment.getContract() != null && payment.getContract().getClient() != null && payment.getContract().getClient().getName() != null) {
            description += payment.getContract().getClient().getName() + " /";
        }

        if (payment.getContract() != null && payment.getContract().getId() != null) {
            description += payment.getContract().getId() + " /";
        }

        tempTransaction.setDescription(description);
        return tempTransaction;
    }

    private void updateRelatedEntity(WireTransferTempTransaction tempTransaction, String relatesTo, Long relatedEntityId) {
        Transaction transaction = null;

        if (relatesTo.equals("Transaction")) {
            transaction = transactionRepository.findOne(relatedEntityId);
        }

        if (relatesTo.equals("ExpectedWireTransfer")) {
            transaction = expectedWireTransferRepository.findOne(relatedEntityId).getReconciliationTransaction();
        }

        setTransactionDetails(transaction, tempTransaction);

        transactionRepository.save(transaction);
    }

    private void createTransaction(WireTransferTempTransaction tempTransaction) {
        Transaction transaction = new Transaction();
        setTransactionDetails(transaction, tempTransaction);

        transactionRepository.save(transaction);
    }

    private void setTransactionDetails(Transaction transaction, WireTransferTempTransaction tempTransaction) {
        Payment payment = tempTransaction.getPayment();

        transaction.setPayment(tempTransaction.getPayment());
        transaction.setAmount(tempTransaction.getAmount());
        transaction.setDate(tempTransaction.getTransactionDate());
        transaction.setPnlValueDate(tempTransaction.getPnlValueDate());
        transaction.setVatAmount(tempTransaction.getVatAmount());

        if (payment.getTypeOfPayment().equals(getItem("TypeOfPayment", "monthly_payment"))) {
            transaction.setToBucket(bucketRepository.findByCode("BC 10"));
            transaction.setVatType(VatType.OUT);
            transaction.setLicense(PicklistHelper.getItemNoException(PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));

            transaction.setTransactionType(TransactionEntityType.CLIENT);
            ClientTransaction clientTransaction = new ClientTransaction();
            clientTransaction.setClient(payment.getContract().getClient());
            transaction.setClients(Arrays.asList(clientTransaction));

            String description = "wire transfer received ";
            if (payment.getContract() != null && payment.getContract().getClient() != null && payment.getContract().getClient().getName() != null) {
                description += payment.getContract().getClient().getName() + " /";
            }

            if (payment.getContract() != null && payment.getContract().getId() != null) {
                description += payment.getContract().getId() + " /";
            }

            tempTransaction.setDescription(description);

            if (payment.getContract().getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))) {
                transaction.setRevenue(revenueRepository.findByCode("FTR 02"));
            }

            if (payment.getContract().getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {
                transaction.setRevenue(revenueRepository.findByCode("MVR 02"));
            }

        } else {
            transaction.setVatType(tempTransaction.getVatType());
            transaction.setToBucket(tempTransaction.getToBucket());
            transaction.setRevenue(tempTransaction.getRevenue());
            transaction.setLicense(tempTransaction.getLicense());
            transaction.setDescription(tempTransaction.getDescription());
        }
    }*/

    @PreAuthorize("hasPermission('wireTransferTempTransaction','getTransactions')")
    @RequestMapping(value = "/getTransactions/{relatesTo}/{relatedEntityId}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity getTransactions(@PathVariable("relatesTo") WireTransferTempPaymentRelatesTo relatesTo, @PathVariable("relatedEntityId") Long relatedEntityId) {
        SelectQuery<WireTransferTempTransaction> selectQuery = new SelectQuery(WireTransferTempTransaction.class);
        selectQuery.filterBy("wireTransferPayment.relatesTo", "=", relatesTo);
        selectQuery.filterBy("wireTransferPayment.relatedEntityId", "=", relatedEntityId);

        return new ResponseEntity(selectQuery.execute(), HttpStatus.OK);
    }

    @Override
    public BaseRepository<WireTransferTempTransaction> getRepository() {
        return repository;
    }
}
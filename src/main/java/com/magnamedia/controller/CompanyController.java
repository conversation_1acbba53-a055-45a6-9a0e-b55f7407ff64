package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Company;
import com.magnamedia.entity.PLCompany;
import com.magnamedia.repository.CompanyRepository;
import com.magnamedia.repository.PLCompanyRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@RequestMapping("/companies")
@RestController
public class CompanyController extends BaseRepositoryController<Company> {

    @Autowired
    private CompanyRepository companyRepository;
    @Autowired
    private PLCompanyRepository plCompanyRepository;

    @Override
    protected ResponseEntity<?> createEntity(Company entity) {
        Company company = (Company) super.createEntity(entity).getBody();
        PLCompany plCompany = new PLCompany();
        plCompany.setCompany(company);
        plCompany.setName(company.getName());
        plCompanyRepository.save(plCompany);

        return new ResponseEntity(company, HttpStatus.OK);
    }

    @Override
    public BaseRepository<Company> getRepository() {
        return companyRepository;
    }
}

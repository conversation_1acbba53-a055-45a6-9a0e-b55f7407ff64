package com.magnamedia.service;



import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.DDBankMessaging;
import com.magnamedia.entity.DDMessaging;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.module.type.DDMessagingSubType;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.module.type.DirectDebitMessagingScheduleTermCategory;
import com.magnamedia.module.type.DirectDebitRejectCategory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class NotificationTemplateService {
    
    protected static final Logger logger = Logger.getLogger(NotificationTemplateService.class.getName());
    
    
    public List<String> invalidAccountRejectionHideOnContractTerminated() {
    
        //ACC-5214
        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.DirectDebitRejected);
        query.filterBy("rejectCategory", "=", DirectDebitRejectCategory.Invalid_Account);
        query.filterBy("trials", "=", "1");
        query.filterBy("contractProspectTypes", "like", "%maidvisa.ae_prospect%");
        
        SelectFilter filter1 = new SelectFilter("reminders", "=", "0");
        SelectFilter filter2 = new SelectFilter("reminders", "=", "1");
        SelectFilter filter3 = new SelectFilter("reminders", "=", "2")
            .and("scheduleTermCategory", "=", DirectDebitMessagingScheduleTermCategory.GToday);
        query.filterBy(filter1.or(filter2).or(filter3));

        List<DDMessaging> l = query.execute();
        List<String> names = l.stream().filter(d -> d.getClientTemplate() != null)
            .map(d -> d.getClientTemplate().getName()).collect(Collectors.toList());

        names.addAll(addDdBankMessagingTemplates(l));

        //ACC-5235
        query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.DirectDebitRejected);
        query.filterBy("rejectCategory", "=", DirectDebitRejectCategory.Invalid_Account);
        query.filterBy("trials", "=", "1");
        query.filterBy("contractProspectTypes", "like", "%maids.cc_prospect%");
    
        filter1 = new SelectFilter("reminders", "=", "0").and("trials", "=", "1");
        filter2 = new SelectFilter("reminders", "=", "1").and("trials", "=", "1");
        filter3 = new SelectFilter("reminders", "=", "0").and("trials", "=", "2");

        query.filterBy(filter1.or(filter2).or(filter3));
        l = query.execute();
        names.addAll(l.stream().filter(d -> d.getClientTemplate() != null)
            .map(d -> d.getClientTemplate().getName()).collect(Collectors.toList()));

        names.addAll(addDdBankMessagingTemplates(l));

        return names;
    }
    
    
    public List<String> rejectedAuthorization() {
        // ACC-5214 ACC-5235
        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.DirectDebitRejected);
        query.filterBy("rejectCategory", "=", DirectDebitRejectCategory.Authorization);
        query.filterBy("reminders", "=", "0");
        
        SelectFilter filter1 = new SelectFilter("trials", "=", "1");
        SelectFilter filter2 = new SelectFilter("trials", "=", "2");
        SelectFilter filter3 = new SelectFilter("trials", "=", "3");
        query.filterBy(filter1.or(filter2).or(filter3));

        List<DDMessaging> l = query.execute();
        List<String> names = l.stream().filter(d -> d.getClientTemplate() != null)
                .map(d -> d.getClientTemplate().getName()).collect(Collectors.toList());

        names.addAll(addDdBankMessagingTemplates(l));

        return names;
    }
    
    //ACC-5214 ACC-5235
    public List<String> bouncedPaymentsHideAfterOneDay() {
    
        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.BouncedPayment);
        query.filterBy("reminders", "=", "0");
        query.filterBy("trials", "=", "0");
        query.filterBy("contractProspectTypes", "like", "%maidvisa.ae_prospect%");
        
        SelectFilter filter1 = new SelectFilter("bouncedPaymentStatus.code", "=", "bounced_payment_received");
        SelectFilter filter2 = new SelectFilter("bouncedPaymentStatus.code", "=", "bounced_payment_received_-_not_including_worker_salary");
        query.filterBy(filter1.or(filter2));

        List<DDMessaging> l = query.execute();
        List<String> names = l.stream().filter(d -> d.getClientTemplate() != null)
            .map(d -> d.getClientTemplate().getName()).collect(Collectors.toList());

        names.addAll(addDdBankMessagingTemplates(l));

        query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.BouncedPayment);
        query.filterBy("reminders", "=", "0");
        query.filterBy("trials", "=", "0");
        query.filterBy("contractProspectTypes", "like", "%maids.cc_prospect%");
        query.filterBy("bouncedPaymentStatus.code", "=", "bounced_payment_received_-_not_including_worker_salary");

        l = query.execute();
        names.addAll(l.stream().filter(d -> d.getClientTemplate() != null)
            .map(d -> d.getClientTemplate().getName()).collect(Collectors.toList()));

        names.addAll(addDdBankMessagingTemplates(l));

        return names;
    }

    // ACC-4715 ACC-5214
    public List<String> ddSigningOfferTemplate() {

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.ClientsPayingViaCreditCard);
        query.filterBy("subType", "=", DDMessagingSubType.DD_SIGNING_OFFER);

        List<DDMessaging> l = query.execute();
        List<String> names = l.stream().map(d -> d.getClientTemplate().getName()).collect(Collectors.toList());

        names.addAll(addDdBankMessagingTemplates(l));
        return names;
    }

    // ACC-4715 ACC-5214
    public List<String> rejectedDdsSubmittedGetTemplate() {

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.ClientsPayingViaCreditCard);
        query.filterBy("subType", "=", DDMessagingSubType.DD_Rejection);

        List<DDMessaging> l = query.execute();
        List<String> names = l.stream().map(d -> d.getClientTemplate().getName()).collect(Collectors.toList());

        names.addAll(addDdBankMessagingTemplates(l));

        return names;
    }

    // ACC-6544
    private List<String> addDdBankMessagingTemplates(List<DDMessaging> l) {
        if (l.isEmpty()) return new ArrayList<>();

        SelectQuery<DDBankMessaging> q = new SelectQuery<>(DDBankMessaging.class);
        q.filterBy("ddMessaging", "in", l);
        return q.execute()
                .stream()
                .filter(d -> d.getClientTemplate() != null)
                .map(d -> d.getClientTemplate().getName())
                .collect(Collectors.toList());
    }

    // ACC-6795
    public List<String> terminationMessageGetTemplates() {

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.Termination);

        List<String> l = query.execute().stream().map(d -> d.getClientTemplate().getName()).collect(Collectors.toList());

        SelectQuery<DDBankMessaging> ddBankQuery = new SelectQuery<>(DDBankMessaging.class);
        ddBankQuery.filterBy("ddMessaging.event", "=", DDMessagingType.Termination);
        l.addAll(ddBankQuery.execute().stream().map(d -> d.getClientTemplate().getName()).collect(Collectors.toList()));

        return l;
    }

    public List<String> getReceivePaymentNotificationCodes() {

        return Arrays.asList(CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_FROM_ONE_TO_X_MINUS_ONE_MONTHS_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_FROM_X_PLUS_ONE_ONWARDS_MONTHS_NOTIFICATION.toString(),
                MvNotificationTemplateCode.MV_PAYMENT_RECEIVED_FROM_ONE_TO_X_MINUS_ONE_MONTHS_NOTIFICATION.toString(),
                MvNotificationTemplateCode.MV_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION.toString(),
                MvNotificationTemplateCode.MV_PAYMENT_RECEIVED_FROM_X_PLUS_ONE_ONWARDS_MONTHS_NOTIFICATION.toString());
    }
}


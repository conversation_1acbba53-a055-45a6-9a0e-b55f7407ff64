package com.magnamedia.service;

import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.repository.ParameterRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.type.*;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.DirectDebitGenerationPlanProjection;
import com.magnamedia.entity.projection.DirectDebitGenerationPlaneMigrationReportCSVProjection;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.extra.*;
import com.magnamedia.helper.*;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.Months;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Date;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;


//ACC-3741
@Service
public class DirectDebitGenerationPlanService {

    @Autowired
    private DirectDebitGenerationPlanRepository directDebitGenerationPlanRepository;

    @Autowired
    private DirectDebitController directDebitController;

    @Autowired
    private ContractPaymentTermHelper contractPaymentTermHelper;

    @Autowired
    private MessagingService pushNotificationService;

    @Autowired
    private PushNotificationHelper pushNotificationHelper;

    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;

    private static final Logger logger = Logger.getLogger(DirectDebitGenerationPlanService.class.getName());

    public static final List<DirectDebitGenerationPlan.DdGenerationPlanStatus> pendingStatus = Arrays.asList(
            DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING,
            DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING_PAYING_VIA_CREDIT_CARD);

    public Map<String, Object> buildDirectDebitGenerationPlans(
            ContractPaymentType contractPaymentType,
            DateTime fromDate, DateTime toDate) {
       return buildDirectDebitGenerationPlans(contractPaymentType, fromDate, toDate, false);
    }

    public Map<String, Object> buildDirectDebitGenerationPlans(
            ContractPaymentType contractPaymentType,
            DateTime fromDate, DateTime toDate,
            boolean justCreatePayment) {

        Map<String, Object> output = new HashMap<>();
        List<DirectDebitGenerationPlan> plans = new ArrayList<>();

        int postponePeriod  = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.DD_GEN_PRE_POSTPONE_PERIOD));
        int startDayPeriod  = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DD_GEN_POSTPONE_START_AFTER_X_DAY));
        logger.info("contractPaymentType id: " + contractPaymentType.getId());

        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
        
        DateTime previousDDPlanDate = new DateTime();
        Integer recurrence = contractPaymentType.getRecurrence();
        Integer startsOn = contractPaymentType.getStartsOn();
        boolean isFirstCP = true;

        if ((recurrence == null || recurrence.equals(0)) &&
                !justCreatePayment &&
                Setup.getApplicationContext()
                        .getBean(FlowProcessorService.class)
                        .isPayingViaCreditCard(contractPaymentType.getContractPaymentTerm().getContract())) {
            DateTime startDate = fromDate.plusMonths(startsOn).dayOfMonth().withMinimumValue().withTimeAtStartOfDay();
            DateTime generationDate = startDate.minusDays(postponePeriod + startDayPeriod);

            plans.add(buildGenerationPlan(contractPaymentType, startDate, generationDate, ontTimeDDMonthDuration));
            output.put("plans", plans);
            return output;
        }

        for (DateTime dateTime = fromDate.plusMonths(startsOn).plusDays(startDayPeriod).withTimeAtStartOfDay();
             ((dateTime.getYear() < toDate.getYear())
                     || (dateTime.getYear() == toDate.getYear() && dateTime.getMonthOfYear() <= toDate.getMonthOfYear()));
             dateTime = dateTime.plusMonths(recurrence)) {

            DateTime currentDDPlanDate;

            if (contractPaymentType.getEndsAfter() != null
                    && dateTime.isAfter(fromDate
                    .plusMonths(contractPaymentType.getEndsAfter())
                    .dayOfMonth().withMinimumValue().withTimeAtStartOfDay())) {
                
                logger.log(Level.SEVERE, "Reach to endAfter");
                break;
            }

            if (dateTime.minusDays(startDayPeriod).toString("yyyy-MM")
                    .equals(new LocalDate(contractPaymentType.getContractPaymentTerm().getContract()
                            .getStartOfContract()).toString("yyyy-MM"))) {
                logger.log(Level.SEVERE, "start on <= postponePeriod create one dd immediately");

                output.put("contractPayment", contractPaymentType.generateContractPayment(dateTime.minusDays(startDayPeriod)));
                if (recurrence == null || recurrence.equals(0)) break;
                continue;
            } else if (isFirstCP) {
                currentDDPlanDate = dateTime.minusDays(postponePeriod);
                isFirstCP = false;
            } else {
                currentDDPlanDate = previousDDPlanDate.plusMonths(recurrence);
            }

            if (!justCreatePayment && currentDDPlanDate.isAfterNow()) {
                plans.add(buildGenerationPlan(contractPaymentType, dateTime,
                    currentDDPlanDate.minusDays(startDayPeriod), ontTimeDDMonthDuration));
            }

            if (recurrence == null || recurrence.equals(0)) {
                break;
            }

            previousDDPlanDate = currentDDPlanDate;
        }

        output.put("plans", plans);
        return output;
    }

    public DirectDebitGenerationPlan buildGenerationPlan(
            ContractPaymentType contractPaymentType, DateTime cpDate,
            DateTime currentDDPlanDate, Integer ontTimeDDMonthDuration) {

        Map<String, Object> map = Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .getContractPaymentAmount(cpDate, contractPaymentType);
        logger.log(Level.SEVERE, "Create new generation plan generate at {0}", currentDDPlanDate);
        logger.log(Level.SEVERE, "DD start at {0}", cpDate);

        DirectDebitGenerationPlan directDebitGenerationPlan = new DirectDebitGenerationPlan();
        directDebitGenerationPlan.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING);
        directDebitGenerationPlan.setDDGenerationDate(new Date(currentDDPlanDate.toDate().getTime()));
        directDebitGenerationPlan.setContractPaymentType(contractPaymentType);
        directDebitGenerationPlan.setContract(contractPaymentType.getContractPaymentTerm().getContract());
        directDebitGenerationPlan.setDDSendDate(new Date(cpDate.toDate().getTime()));
        directDebitGenerationPlan.setDDExpiryDate(new Date(cpDate.plusMonths(ontTimeDDMonthDuration)
                .toDate().getTime()));
        directDebitGenerationPlan.setAdditionalDiscountAmount((Double) map.get("additionalDiscountAmount"));
        directDebitGenerationPlan.setAmount((Double) map.get("amount"));
        if (map.get("discount") != null)
            directDebitGenerationPlan.setDiscountAmount((Double) map.get("discount"));

        if (map.containsKey("moreAdditionalDiscount")) {
            directDebitGenerationPlan.setMoreAdditionalDiscount((Double) map.get("moreAdditionalDiscount"));
        }

        return directDebitGenerationPlan;
    }

    @Transactional
    public void saveAllGeneratedPlans(List<DirectDebitGenerationPlan> newPlans, List<Map<String, String>> ddsMap) {

        if (newPlans.isEmpty()) return;

        // find Min ddGenerationDate
        Date ddGenerationDateMin = newPlans.stream()
                .map(DirectDebitGenerationPlan::getDDGenerationDate)
                .min(java.util.Date::compareTo)
                .get();
        logger.info("ddGenerationDateMin : " + ddGenerationDateMin);

        // Set ddGenerationDateMin on all ddGenerationDate for New Plans and Save
        newPlans.forEach(plan -> {
            plan.setDDGenerationDate(ddGenerationDateMin);

            String status = Objects.requireNonNull(ddsMap.stream()
                            .filter(dd -> plan.getDdCloned().getId().equals(Long.parseLong(dd.get("id"))))
                            .findFirst()
                            .orElse(null))
                    .get("status");
            plan.setDdStatus(DirectDebitStatus.valueOf(status));

            saveGenerationPlanAfterValidate(plan, new HashMap<>());
        });
    }

    public boolean existsPaymentReceivedOrCoveredContractPayment(
            Contract contract, String codeType,
            java.util.Date startDate, java.util.Date endDate) {

        logger.info("contract: " + contract.getId() + ", type: " + codeType +
                ", startDate: " + new LocalDate(startDate).toString("yyyy-MM-dd") +
                ", endDate: " + new LocalDate(endDate).toString("yyyy-MM-dd"));

        // check if exists payment received matched
        if (Setup.getRepository(PaymentRepository.class)
                .existsByContractAndStatusAndTypeOfPayment_CodeAndDateOfPaymentBetween(
                        contract, PaymentStatus.RECEIVED, codeType, startDate, endDate)) return true;

        // check if exists contract payment covered matched
        ContractPaymentRepository contractPaymentRepository = Setup.getRepository(ContractPaymentRepository.class);
        if (contractPaymentRepository.existsCardContractPaymentPaidByTypeAndDateAndStatus(
                contract.getId(), codeType, startDate, endDate)) return true;

        if (Setup.getRepository(ContractPaymentConfirmationToDoRepository.class)
                .existRunningFlowHandledPayment(contract, codeType, startDate, endDate)) return true;

        return contractPaymentRepository.existsActiveDdContractPaymentByTypeAndDateAndStatus(
                contract.getId(), codeType, startDate, endDate, DirectDebitService.notAllowedStatuses);
    }

    @Transactional
    public void saveGenerationPlan(DirectDebitGenerationPlan plan, Map<String, Object> m) {
        if (plan.getPaymentType() != null &&
                directDebitGenerationPlanRepository.findAllPlansByContractAndDdSendDate(
                        plan.getContract(), plan.getDDSendDate())
                        .stream()
                        .anyMatch(p -> p.getPaymentType().getCode().equals(plan.getPaymentType().getCode()))) {

            logger.log(Level.SEVERE, "Client already have scheduled {0} dd on {1}",
                    new Object[] { plan.getPaymentType().getName(),
                            new LocalDate(plan.getDDSendDate()).toString("yyyy-MM-dd")});
            return;
        }

        LocalDate startDate = new LocalDate(plan.getDDSendDate());
        if(existsPaymentReceivedOrCoveredContractPayment(
                plan.getContract(), plan.getPaymentType().getCode(),
                startDate.dayOfMonth().withMinimumValue().toDate(),
                startDate.dayOfMonth().withMaximumValue().toDate())) {
            logger.info("This Plan is Covered by Payment Received Or Contract Payment -> exiting");
            return;
        }

        // Save New Plan Or DDs immediately (no postponing)
        saveGenerationPlanAfterValidate(plan, m);
    }

    @Transactional
    public void saveGenerationPlanAfterValidate(DirectDebitGenerationPlan plan, Map<String, Object> m) {

        LocalDate generationDate = new LocalDate(plan.getDDGenerationDate());
        if (generationDate.isBefore(new LocalDate().dayOfMonth().withMinimumValue()) &&
                new LocalDate(plan.getDDSendDate()).isBefore(new LocalDate().dayOfMonth().withMinimumValue())){
            logger.info("plan of contract id: " + plan.getContract().getId() + " passed generation date -> exiting");
        } else if (generationDate.isAfter(new LocalDate())) {
            directDebitGenerationPlanRepository.save(plan);
        } else {
            logger.info("plan id: " + plan.getId() + " already passed generation date");
            int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
            if (new LocalDate(plan.getDDSendDate()).isBefore(new LocalDate())) {
                plan.setDDSendDate(new Date(new LocalDate().toDate().getTime()));
                plan.setDDExpiryDate(new Date(new LocalDate().plusDays(ontTimeDDMonthDuration).toDate().getTime()));
            }

            generateDDFromGenerationPlan(plan, m);
        }
    }

    @Transactional
    public void generateDDFromGenerationPlan(Long planId) {
        generateDDFromGenerationPlan(directDebitGenerationPlanRepository.findOne(planId), new HashMap<>());
    }

    @Transactional
    public void generateDDFromGenerationPlan(DirectDebitGenerationPlan p, Map<String, Object> m) {
        if (p == null) return;

        logger.info("directDebitGenerationPlan id: " + p.getId());

        Contract contract = p.getContract();
        if (contract.getStatus() == ContractStatus.CANCELLED || contract.getStatus() == ContractStatus.EXPIRED) {
            logger.log(Level.INFO, "Contract is canceled or expired" + p.getId());

            List<DirectDebitGenerationPlan> plans = directDebitGenerationPlanRepository
                    .findByContractAndDdGenerationPlanStatusIn(contract, DirectDebitGenerationPlanService.pendingStatus);

            if (!plans.isEmpty()) {
                for (DirectDebitGenerationPlan plan : plans) {
                    plan.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED);
                    directDebitGenerationPlanRepository.save(plans);
                }
            }

            return;
        }
        if ((boolean) m.getOrDefault("isPayingViaCreditCard", false) ||
                Setup.getApplicationContext()
                .getBean(FlowProcessorService.class)
                .isPayingViaCreditCard(contract)) {
            createTodoAndStartReminderFlow(contract, p, p.getDDSendDate());
            return;
        }

        if (p.getPaymentType().getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE)) { //ACC-4742
            List<DirectDebit> directDebits = directDebitRepository.findALlActiveDDbByContract(
                        p.getContract().getId(),
                        new DateTime(p.getDDSendDate()).plusHours(1).toDate(),
                        new DateTime(p.getDDExpiryDate()).minusHours(1).toDate(),
                        DirectDebitService.notAllowedStatuses);

            if (!directDebits.isEmpty()) {
                directDebits.forEach(dd -> {
                    if (!dd.isAddedByOecFlow()) {
                        logger.info("Monthly payment DDB not canceled with same period id : " + directDebits.get(0).getId());
                        p.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED);
                        directDebitGenerationPlanRepository.save(p);
                    }});
                logger.info("Monthly payment All DDBs canceled");
                return;
            }
        }

        Long ddId = p.getOneTime() ? generateDda(p) :
                (p.getDdCloned() != null ?
                        generateDdb(p) : //ACC-4577
                    createIncompleteDd(p));  // ACC-5825

        startRejectionFlow(p, ddId);

        logger.log(Level.SEVERE, "End Generate Direct Debit");

        if (ddId != null) {
            p.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CREATED);
            p.setDDID(ddId);
            // ACC-6444
            if (!p.getMessageSent() && p.getSendNotificationToClient()) {
                sendNotificationToClient(p);
            }
        } else {
            p.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED);
        }

        directDebitGenerationPlanRepository.save(p);
    }

    @Transactional
    public void sendNotificationAndStartReminderFlow(DirectDebitGenerationPlan plan) {
        logger.log(Level.INFO, "Start send notifications for plan Id " + plan.getId());
        if (!plan.getMessageSent() && plan.getSendNotificationToClient()) {
            sendNotificationToClient(plan);
        }
        //run reminder flow
        createTodoAndStartReminderFlow(
                plan.getContract(), plan, plan.getDDGenerationDate());
    }

    // ACC-8875
    public void createTodoAndStartReminderFlow(Contract contract, DirectDebitGenerationPlan p, Date executionDate) {
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        Map<String, Object> m = new HashMap<>();
        Setup.getApplicationContext()
                .getBean(UnpaidOnlineCreditCardPaymentService.class)
                .createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
                        p.getContractPaymentsCloned().isEmpty() ?
                                Collections.singletonList(p.generateCardPayment(cpt)) :
                                p.getContractPaymentsCloned(),
                        cpt, new LocalDate(executionDate).toDate(), m);

        p.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CREATED);
        directDebitGenerationPlanRepository.save(p);
    }

    private Long generateDda(DirectDebitGenerationPlan p) {
        logger.info("generateDda");

        ContractPaymentTerm cpt = p.getContract().getActiveContractPaymentTerm();
        if (p.getDdCloned() != null && p.getDdCloned().getType().equals(DirectDebitType.MONTHLY) &&
                (directDebitRepository.getOverlappingDD(cpt, p.getDDSendDate(), p.getDdCloned().getExpiryDate()) ||
                directDebitRepository.getOverlappingInCompleteDDs(cpt, p.getDDSendDate(), p.getDdCloned().getExpiryDate()))) {
            logger.info("overlap by another dd, p id: " + p.getId());
            return null;
        }

        if (p.getDdStatus() == null || !p.getDdStatus().equals(DirectDebitStatus.REJECTED)) {
            logger.info("cpt id " + cpt.getId());

            return createDdWithSignatures(
                    p.getContractPaymentsCloned().isEmpty() ?
                            Collections.singletonList(createPayment(p, cpt, p.getDDSendDate())) :
                            p.getContractPaymentsCloned()
                                    .stream()
                                    .map(cp -> cp.toBuilder().directDebit(null).contractPaymentTerm(cpt).build())
                                    .collect(Collectors.toList()),
                    cpt);
        }

        //ACC-4577
        if (p.getDdCloned() == null) return null;
        return createRejectedDd(p);
    }


    private Long generateDdb(DirectDebitGenerationPlan p) {
        logger.info("generateDdb");

        ContractPaymentTerm cpt = p.getContract().getActiveContractPaymentTerm();

        if (p.getDdCloned() != null &&
                (directDebitRepository.getOverlappingDD(cpt, p.getDDSendDate(), p.getDdCloned().getExpiryDate()) ||
                directDebitRepository.getOverlappingInCompleteDDs(cpt, p.getDDSendDate(), p.getDdCloned().getExpiryDate()))) {
            logger.info("overlap by another dd, p id: " + p.getId());
            return null;
        }

        if (p.getDdStatus() == null || !p.getDdStatus().equals(DirectDebitStatus.REJECTED)) {
            DateTime d = new DateTime(p.getDDSendDate());
            DateTime e = new DateTime(p.getDdCloned().getExpiryDate());
            List<ContractPayment> payments = new ArrayList<>();

            while(d.isBefore(e) || d.equals(e)) {
                payments.add(createPayment(p, cpt, new java.sql.Date(d.toDate().getTime())));
                d = d.plusMonths(1);
            }

            return createDdWithSignatures(payments, cpt);
        }

        if (p.getDdCloned() == null) return null;
        return createRejectedDd(p);
    }

    private Long createDdWithSignatures(List<ContractPayment> payments, ContractPaymentTerm cpt) {
        Map<String, Object> signatureType = Setup.getApplicationContext()
                .getBean(DirectDebitSignatureService.class)
                .getLastSignatureType(cpt, false, false);

        boolean useOldSignatures = ((Boolean) signatureType.get("useApprovedSignature") ||
                (Boolean) signatureType.get("useNonRejectedSignature"));
        logger.info("useOldSignatures " + useOldSignatures);

        List<DirectDebit> dds = directDebitController.generateDD(payments, null, cpt, useOldSignatures,
                true, true, false, false,
                false, false);

        return dds.isEmpty() ? null : dds.get(0).getId();
    }

    private Long createRejectedDd(DirectDebitGenerationPlan p) {
        logger.info("createRejectedDd");
        Map<String, Object> m = new HashMap<>();
        if (p.getOneTime()) {
            m.put("expiryDate",  new LocalDate(p.getDDExpiryDate()));
            m.put("directDebitCategory", DirectDebitCategory.A);
            m.put("directDebitType", DirectDebitType.ONE_TIME);
            m.put("ddStartDate", new LocalDate(p.getDDSendDate()));
        }

        DirectDebit dd = p.getDdCloned().clone(p.getDDSendDate(), m);
        dd.setMStatus(p.getDdStatus());
        dd.setStatus(p.getDdStatus());
        dd.setRejectCategory(p.getDdCloned().getRejectCategory());
        dd.setRejectionReason(p.getDdCloned().getRejectionReason());

        directDebitRepository.save(dd);

        p.getDdCloned().cloneChildDds(dd, 0);

        DirectDebit ddFinal = directDebitRepository.findOne(dd.getId());

        directDebitFileRepository.findByDirectDebit_Id(ddFinal.getId())
                .forEach(ddf -> {
                    ddf.setStatus(DirectDebitFileStatus.REJECTED);
                    ddf.setDdStatus(DirectDebitStatus.REJECTED);
                    ddf.setRejectCategory(ddFinal.getRejectCategory());
                    ddf.setRejectionReason(ddFinal.getRejectionReason());
                    ddf.setStartDate(ddFinal.getStartDate());
                    directDebitFileRepository.save(ddf);
                });

        List<ContractPayment> payments = new ArrayList<>(p.getDdCloned().getContractPayments());

        for (ContractPayment cp : payments) {
            cp.setDirectDebit(ddFinal);
            contractPaymentRepository.save(cp);

            p.getDdCloned().getContractPayments().remove(cp);
        }
        directDebitRepository.save(p.getDdCloned());

        return ddFinal.getId();
    }

    private ContractPayment createPayment(
            DirectDebitGenerationPlan p,
            ContractPaymentTerm cpt,
            java.sql.Date d) {

        ContractPayment cp = new ContractPayment();
        cp.setContractPaymentTerm(cpt);
        cp.setPaymentType(p.getPaymentType());
        cp.setSubType(p.getPaymentSubType());
        cp.setAmount(p.getAmount());
        cp.setAdditionalDiscountAmount(p.getAdditionalDiscountAmount());
        cp.setDate(d);
        cp.setIsCalculated(true);
        cp.setOneTime(p.getOneTime());
        cp.setPaymentMethod(PaymentMethod.DIRECT_DEBIT);
        cp.setDescriptionForSigningScreen(contractPaymentTermHelper
                .getPaymentDescriptionForSigningScreen(cp));

        boolean isMonthly = p.getContractPaymentType() != null &&
                PaymentHelper.isMonthlyPayment(p.getContractPaymentType().getType());
        cp.setDiscountAmount(isMonthly && Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .isDuringPremiumPeriod(cpt, d) ?
                p.getContractPaymentType().getDiscount() :
                p.getDiscountAmount());
        cp.setMoreAdditionalDiscount(p.getMoreAdditionalDiscount());

        // ACC-8422
        if ((isMonthly || p.getContractPaymentType() == null) && p.getDdCloned() != null && p.getDdCloned().getContractPayments() != null) {
            ContractPayment cpCloned = p.getContractPaymentType() == null ?
                    p.getDdCloned().getContractPayments().get(0) :
                    p.getDdCloned().getContractPayments().stream()
                    .filter(payment -> payment.getPaymentType().getCode()
                            .equals(p.getContractPaymentType().getType().getCode()) &&
                            new LocalDate(payment.getDate()).toString("yyyy-MM")
                                    .equals(new LocalDate(d).toString("yyyy-MM")))
                    .findFirst()
                    .orElse(null);
            if (cpCloned != null) {
                cp.setIncludeWorkerSalary(cpCloned.getIncludeWorkerSalary());
                cp.setWorkerSalary(cpCloned.getWorkerSalary());
                cp.setDescription(cpCloned.getDescription());
                cp.setAffectsPaidEndDate(cpCloned.getAffectsPaidEndDate());
                cp.setDiscountAmount(cpCloned.getDiscountAmount());
                cp.setMoreAdditionalDiscount(cpCloned.getMoreAdditionalDiscount());
            }
        } else {
            cp.setDescription(p.getContractPaymentType().getDescription());
            cp.setAffectsPaidEndDate(p.getContractPaymentType().getAffectsPaidEndDate());
        }

        return cp;
    }

    private Long createIncompleteDd(DirectDebitGenerationPlan p) {
        logger.info("p id: " + p.getId());
        Long ddId = null;
        try {
            List<ContractPayment> l = Setup.getApplicationContext()
                    .getBean(DirectDebitGenerationPlanService.class)
                    .generateNewDd(p.getContract().getActiveContractPaymentTerm(),
                            new DateTime(p.getDDSendDate()).withTimeAtStartOfDay(),
                            false);

            if (!l.isEmpty()) ddId = l.get(0).getDirectDebit().getId();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ddId;
    }

    private void startRejectionFlow(DirectDebitGenerationPlan p, Long ddId) {
        if (p.getDdStatus() == null || ddId == null ||
                !p.getDdStatus().equals(DirectDebitStatus.REJECTED)) return;
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "startRejectionFlow" + ddId,
                        "accounting",
                        "directDebitGenerationPlanService",
                        "startRejectionFlow")
                        .withRelatedEntity("DirectDebit", ddId)
                        .withParameters(new Class[] {Long.class, Long.class},
                                new Object[] {p.getId(), ddId})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    public void startRejectionFlow(Long pId, Long ddId) {
        logger.info("startRejectionFlow");
        DirectDebitGenerationPlan p = directDebitGenerationPlanRepository.findOne(pId);
        DirectDebit dd = directDebitRepository.findOne(ddId);

        logger.info("directDebit contract payment size : " + (dd.getContractPayments() != null ?
                dd.getContractPayments().size() : "null"));

        if (dd.getContractPayments() == null || dd.getContractPayments().isEmpty()) {
            logger.log(Level.INFO, "directDebit startDate : {0}; endDate : {1}; cpt id : {2}",
                    new Object[]{new Date(dd.getStartDate().getTime()),
                            new Date(dd.getExpiryDate().getTime()),
                            dd.getContractPaymentTerm().getId()});

            SelectQuery<ContractPayment> query = new SelectQuery<>(ContractPayment.class);
            query.filterBy("contractPaymentTerm.id", "=", dd.getContractPaymentTerm().getId());
            query.filterBy("paymentMethod", "=", PaymentMethod.DIRECT_DEBIT);
            query.filterBy("paymentType.code", "=", "monthly_payment");
            query.filterBy("date", ">=", new Date(dd.getStartDate().getTime()));
            query.filterBy("date", "<=", new Date(dd.getExpiryDate().getTime()));
            query.sortBy("date", false, true);

            List<ContractPayment> contractPayments = query.execute();
            logger.log(Level.INFO, "contractPayments size : {0}", contractPayments.size());

            if (p.getOneTime()) {
                contractPayments = contractPayments.stream()
                        .filter(cp -> new LocalDate(cp.getDate()).toString("yyyy-MM")
                                .equals(new LocalDate(dd.getStartDate()).toString("yyyy-MM")))
                        .collect(Collectors.toList());
            }

            Map<Long, DirectDebit> oldDds = new HashMap<>();
            Map<String, ContractPayment> contractPaymentsDistinct = new HashMap<>();

            contractPayments.forEach(cp -> {
                String key =  new LocalDate(cp.getDate()).toString("yyyy-MM-dd") + "_" + cp.getAmount();
                logger.info("key : " + key);

                if (contractPaymentsDistinct.containsKey(key)) return;

                Long oldDdId = cp.getDirectDebit().getId();

                if (oldDds.containsKey(oldDdId)) {
                    oldDds.get(oldDdId).getContractPayments().remove(cp);
                    oldDds.get(oldDdId).getPayments().remove(cp);
                } else {
                    DirectDebit oldDd = directDebitRepository.findOne(cp.getDirectDebit().getId());
                    oldDd.getContractPayments().remove(cp);
                    oldDd.getPayments().remove(cp);
                    oldDds.put(oldDd.getId(), oldDd);
                }

                cp.setDirectDebit(null);
                contractPaymentsDistinct.put(key, cp);
            });

            logger.info("old dds size : " + oldDds.size());
            directDebitRepository.save(new ArrayList<>(oldDds.values()));

            List<ContractPayment> lastCps = Setup.getRepository(ContractPaymentRepository.class)
                    .findAll(contractPaymentsDistinct.values()
                            .stream()
                            .map(BaseEntity::getId)
                            .collect(Collectors.toList()));

            lastCps.forEach(cp -> {
                cp.setDirectDebit(dd);
                if (p.getOneTime()) { // ACC-9070
                    cp.setOneTime(true);
                }
            });
            dd.setPayments(new ArrayList<>(contractPaymentsDistinct.values()));
            directDebitRepository.save(dd);

            logger.info("directDebit contract payment size : " + contractPaymentsDistinct.size());
        }

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "startRejectionFlowAfterGeneratedPlan" + dd.getId(),
                            "accounting",
                            "directDebitGenerationPlanService",
                            "startRejectionFlowAfterGeneratedPlan")
                        .withRelatedEntity("DirectDebit", dd.getId())
                        .withParameters(new Class[] {Long.class},
                                new Object[] {dd.getId()})
                            .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .withDelay(p.getOneTime() ? 0L : 60L * 1000L)
                        .build());
    }

    public void startRejectionFlowAfterGeneratedPlan(Long ddId) {
        logger.info("directDebit id: " + ddId);

        Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class)
                .startFlow(directDebitRepository.findOne(ddId));
    }

    public void sendNotificationToClient(DirectDebitGenerationPlan plan) {

        if (plan == null) return;

        if(!plan.getContract().isActive() || Setup.getApplicationContext()
                .getBean(FlowProcessorService.class)
                .isPayingViaCreditCard(plan.getContract())) return;

        logger.log(Level.INFO, "client ID: " + plan.getContract().getClient().getId() + "; plan ID: " + plan.getId() +
                "; type: " + plan.getPaymentType().getCode());

        Map<String, String> parameters = new HashMap<>();
        parameters.put("Client_Name", (plan.getContract().getClient().getTitle() != null ? plan.getContract().getClient().getTitle().getName() + " " : "") +
                plan.getContract().getClient().getName());
        parameters.put("client_nickname_or_name", StringUtils.getClientNicknameOrName(plan.getContract().getClient()));

        Map<String, AppAction> cta = new HashMap<>();

        parameters.put("maids_cc_client_call_sms_link", Setup.getApplicationContext()
                .getBean(Utils.class)
                .shorteningUrl("https://wa.me/" +
                        StringHelper.NormalizePhoneNumber(Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_MV_CLIENT_CALL))));

        AppAction a = new AppAction();
        a.setType(AppActionType.BUTTON);
        a.setText("Chat With Us");
        a.setFunctionType(FunctionType.WEB_SERVICE);
        a.setNavigationType(NavigationType.WEB);
        a.setHyperlink(parameters.get("maids_cc_client_call_sms_link"));
        a.setAppRouteName("");
        a.setAppRouteArguments(new HashMap<String, String>() {{
            put("contractId", plan.getContract().getId().toString());
            put("contractUuid", plan.getContract().getUuid());
        }});

        cta.put("maids_cc_client_call_notification_link", a);
        parameters.put("maids_cc_client_call_notification_link", "@maids_cc_client_call_notification_link@");
        parameters.put("payment_date", DateUtil.formatTo_MM_DD_YYYY(new LocalDate(plan.getDDSendDate())));
        parameters.put("maid_name", plan.getContract() != null && plan.getContract().getHousemaid() != null ?
                plan.getContract().getHousemaid().getName() : "");

        String templateName;
        switch (plan.getPaymentType().getCode()) {
            case AbstractPaymentTypeConfig.INSURANCE_TYPE_CODE:
                templateName = plan.getContract().isMaidCc() ? DirectDebitGenerationPlanTemplate.INSURANCE.toString() :
                                MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_INSURANCE_NOTIFICATION.toString();
                parameters.put("insurance_DD_amount", String.valueOf(plan.getAmount().intValue()));
                break;
            case AbstractPaymentTypeConfig.AGENCY_FEE_TYPE_CODE:
                templateName = plan.getContract().isMaidCc() ? DirectDebitGenerationPlanTemplate.SAME_DAY_RECRUITMENT_FEE.toString() :
                                MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_SAME_DAY_RECRUITMENT_FEE_NOTIFICATION.toString();
                parameters.put("SDR_DD_Amount", String.valueOf(plan.getAmount().intValue()));
                break;
            default:
                templateName = plan.getContract().isMaidCc() ? DirectDebitGenerationPlanTemplate.OTHER_DD_TYPE.toString() :
                                MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_OTHER_DD_TYPE_NOTIFICATION.toString();
                parameters.put("payment_amount", String.valueOf(plan.getAmount().intValue()));
                parameters.put("DD_TYPE", plan.getContractPaymentType().getDescription());
                break;
        }

        logger.log(Level.INFO, "sending Notification");

        pushNotificationService.sendMessageToClient(plan.getContract(),
                parameters,
                cta,
                plan.getContract().getId(),
                "Contract",
                TemplateUtil.getTemplate(templateName));

        plan.setMessageSent(true);
    }

    public void sendFailedDDsGenerationReport(
            List<DirectDebitGenerationPlan> directDebitGenerationPlans){

        try {
            logger.info("directDebitGenerationPlans size" + directDebitGenerationPlans.size());
            String[] namesOrdered = {"id", "ddSendDate", "ddExpiryDate", "ddGenerationDate",
                    "amount", "ddType", "contractId"};

            String emails = Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.FAILED_DDS_GENERATION_REPORT_RECIPIENT);
            if (emails == null || emails.isEmpty()) return;

            File file = CsvHelper.generateCsv(directDebitGenerationPlans,
                    DirectDebitGenerationPlanProjection.class,
                    namesOrdered, namesOrdered, "FailedDDsGenerationReport", ".csv");

            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaffWithAttachments(DirectDebitGenerationPlanTemplate.FAILED_DDS_GENERATION_REPORT.toString(),
                            new HashMap<>(), emails, Collections.singletonList(Storage.storeTemporary(file.getName(),
                                    new FileInputStream(file),null,false)),
                            "Failed postpone dds generation report");

        } catch (Exception e) {
            logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
        }
    }

    public void hideGenerationPlanPushNotification(Payment payment) {
        logger.info("payment id: " + payment.getId());

        if (payment.getDirectDebit() == null) return;

        DirectDebitGenerationPlan directDebitGenerationPlan = directDebitGenerationPlanRepository
                .findByDirectDebitIDAndMessageSentTrue(payment.getDirectDebit().getId());

        if (directDebitGenerationPlan == null) return;

        String type;
        PicklistItem t = Setup.getRepository(PicklistItemRepository.class).findOne(payment.getTypeOfPayment().getId());

        switch (t.getCode()) {
            case AbstractPaymentTypeConfig.INSURANCE_TYPE_CODE:
                type = DirectDebitGenerationPlanTemplate.INSURANCE.toString();
                break;
            case AbstractPaymentTypeConfig.AGENCY_FEE_TYPE_CODE:
                type = DirectDebitGenerationPlanTemplate.SAME_DAY_RECRUITMENT_FEE.toString();
                break;
            default:
                type = DirectDebitGenerationPlanTemplate.OTHER_DD_TYPE.toString();
                break;
        }

        logger.info("payment type: " + type);

        Contract contract = Setup.getRepository(ContractRepository.class).findOne(payment.getContract().getId());

        List<PushNotification> clientNotifications = pushNotificationHelper
                .getNotificationsToMoveInbox("Client", contract.getClient().getId(), type);
        pushNotificationHelper.stopDisplaying(clientNotifications);
    }

    @Transactional
    public void generatePlansAfterCancelMonthlyPaymentDd(
            ContractPaymentTerm contractPaymentTerm,
            DateTime paidEndDate,
            List<DirectDebit> futureDd) {

        generatePlansAfterCancelPaymentDd(contractPaymentTerm, paidEndDate, futureDd,
                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.ADJUSTED_END_DATE_DD_GEN_PRE_POSTPONE_PERIOD)));
    }

    @Transactional
    public void generatePlansAfterCancelPaymentDd(
            ContractPaymentTerm contractPaymentTerm,
            DateTime paidEndDate,
            List<DirectDebit> futureDd,
            int postponePeriod) {

        generatePlansAfterCancelPaymentDdWithoutSave(contractPaymentTerm, paidEndDate, futureDd, postponePeriod)
                .forEach(p -> saveGenerationPlan(p, new HashMap<>()));
    }

    // ACC-4577
    public List<DirectDebitGenerationPlan> generatePlansAfterCancelPaymentDdWithoutSave(
            ContractPaymentTerm contractPaymentTerm,
            DateTime paidEndDate,
            List<DirectDebit> futureDd,
            int postponePeriod) {

        logger.info("contractPaymentTerm id : " + contractPaymentTerm.getId());

        ContractPaymentType monthlyContractPaymentType = contractPaymentTerm.getContractPaymentTypes().stream()
                .filter(contractPaymentType -> contractPaymentType.getType().getCode()
                        .equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE))
                .findFirst().orElse(null);

        if (monthlyContractPaymentType == null) {
            logger.log(Level.SEVERE, "no monthly payment type found");
            return new ArrayList<>();
        }


        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));

        Map<String, Object> map = new HashMap<>();
        map.put("contract", contractPaymentTerm.getContract());
        map.put("ontTimeDDMonthDuration", ontTimeDDMonthDuration);

        futureDd = futureDd.stream()
                .sorted(Comparator.comparing(BaseEntity::getCreationDate)
                .reversed()).collect(Collectors.toList());

        List<DirectDebitGenerationPlan> newPlans = new ArrayList<>();
        Set<String> generatedPlans = new HashSet<>();
        futureDd.forEach(dd -> {
            DirectDebitGenerationPlan plan;
            map.put("directDebit", dd);

            logger.log(Level.INFO, "dd id : {0}; dd startDate : {1}; dd expiryDate : {2}",
                    new Object[]{dd.getId(), dd.getStartDate(), dd.getExpiryDate()});
            SelectQuery<DirectDebitGenerationPlan> plans = new SelectQuery<>(DirectDebitGenerationPlan.class);
            plans.filterBy("ddCloned.id", "=", dd.getId());
            plans.setLimit(1);

            if (!plans.execute().isEmpty()) return;


            if (dd.getCategory().equals(DirectDebitCategory.A)) {
                map.put("contractPaymentType", contractPaymentTerm.getContractPaymentTypes().stream()
                        .filter(contractPaymentType -> contractPaymentType.getType().getCode()
                                .equals(dd.getPaymentType().getCode()))
                        .findFirst().orElse(null));
                DateTime ddStartDate = new DateTime(Stream.of(dd.getStartDate(), new java.util.Date()).max(java.util.Date::compareTo).get());
                map.put("ddGenerationDate", new Date(ddStartDate.toLocalDate().minusDays(postponePeriod).toDate().getTime()));
                map.put("ddStartDate", ddStartDate);
                map.put("isOneTime", true);
                plan = generateMonthlyDdPlanWithoutSave(map, generatedPlans);
                if (plan != null) { newPlans.add(plan); }
                return;
            }

            DateTime ddaStartDate = paidEndDate.plusMonths(1).withDayOfMonth(1);
            map.put("ddGenerationDate", new Date(ddaStartDate.minusDays(postponePeriod).toDate().getTime()));
            map.put("contractPaymentType", monthlyContractPaymentType);
            if (dd.getExpiryDate().getTime() <= paidEndDate.toDate().getTime()) return;

            //ACC-4821 #2 create DDA + DDB
            if (new DateTime(dd.getStartDate()).minusHours(1).isBefore(
                        paidEndDate.plusMonths(1).withDayOfMonth(1).withTimeAtStartOfDay()) &&
                    new DateTime(dd.getExpiryDate()).isAfter(
                            paidEndDate.plusMonths(2).withDayOfMonth(1).withTimeAtStartOfDay())) {

                map.put("ddStartDate", ddaStartDate);
                map.put("isOneTime", true);
                plan = generateMonthlyDdPlanWithoutSave(map, generatedPlans);
                if (plan != null) { newPlans.add(plan); }

                map.put("ddStartDate", ddaStartDate.plusMonths(1));
                map.put("isOneTime", false);
                plan = generateMonthlyDdPlanWithoutSave(map, generatedPlans);
                if (plan != null) { newPlans.add(plan); }
            //ACC-4821 #3 create DDA
            } else if (new DateTime(dd.getStartDate()).minusHours(1).isBefore(
                        paidEndDate.plusMonths(1).withDayOfMonth(1).withTimeAtStartOfDay()) &&
                    new DateTime(dd.getExpiryDate()).isBefore(
                            paidEndDate.plusMonths(2).withDayOfMonth(1).withTimeAtStartOfDay())) {

                map.put("ddStartDate", ddaStartDate);
                map.put("isOneTime", true);
                plan = generateMonthlyDdPlanWithoutSave(map, generatedPlans);
                if (plan != null) { newPlans.add(plan); }
            //ACC-4821 #4
            } else if (new DateTime(dd.getStartDate()).plusHours(1).isAfter(
                    paidEndDate.plusMonths(2).withDayOfMonth(1).withTimeAtStartOfDay())) {

                map.put("ddGenerationDate", new Date(new DateTime(dd.getStartDate()).minusDays(postponePeriod).toDate().getTime()));
                map.put("ddStartDate", new DateTime(dd.getStartDate()));
                map.put("isOneTime", false);
                plan = generateMonthlyDdPlanWithoutSave(map, generatedPlans);
                if (plan != null) { newPlans.add(plan); }
            }
        });

        return newPlans;
    }

    public void generateMonthlyDdPlan(
            Map<String, Object> map,
            Set<String> generatedPlans) {

        DirectDebitGenerationPlan plan = generateMonthlyDdPlanWithoutSave(map, generatedPlans);
        if (plan == null) return;
        directDebitGenerationPlanRepository.saveAndFlush(plan);
    }

    public DirectDebitGenerationPlan generateMonthlyDdPlanWithoutSave(
            Map<String, Object> map,
            Set<String> generatedPlans) {

        DirectDebit dd = (DirectDebit) map.get("directDebit");
        DateTime ddStartDate = (DateTime) map.get("ddStartDate");
        boolean isOneTime = (boolean) map.get("isOneTime") ;
        Integer ontTimeDDMonthDuration = (Integer) map.get("ontTimeDDMonthDuration");

        String key = ddStartDate + "_" + dd.getAmount();
        if (generatedPlans.contains(key)) {
            logger.log(Level.INFO, "a plan already created for start date: {0}, dd id: {1}",
                    new Object[]{ddStartDate, dd.getId()});
            return null;
        }
        generatedPlans.add(key);

        DirectDebitGenerationPlan directDebitGenerationPlanDd = new DirectDebitGenerationPlan();
        directDebitGenerationPlanDd.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING);
        directDebitGenerationPlanDd.setContractPaymentType((ContractPaymentType) map.get("contractPaymentType"));
        directDebitGenerationPlanDd.setContract((Contract) map.get("contract"));
        directDebitGenerationPlanDd.setDdStatus(dd.getMStatus());
        directDebitGenerationPlanDd.setSendNotificationToClient(false);
        directDebitGenerationPlanDd.setAmount(dd.getAmount());
        directDebitGenerationPlanDd.setDdCloned(dd);
        directDebitGenerationPlanDd.setOneTime(isOneTime);
        directDebitGenerationPlanDd.setDDSendDate(new Date(ddStartDate.toDate().getTime()));
        directDebitGenerationPlanDd.setDDExpiryDate(isOneTime ? new Date(ddStartDate.plusMonths(ontTimeDDMonthDuration)
                .toDate().getTime()) : new Date(dd.getExpiryDate().getTime()));
        directDebitGenerationPlanDd.setDDGenerationDate((Date) map.get("ddGenerationDate"));
        return directDebitGenerationPlanDd;
    }

    public DirectDebitGenerationPlan generateDdPlanFromContractPaymentsWithoutSave(
            Map<String, Object> map,
            Set<String> generatedPlans) {

        List<ContractPayment> contractPayments = (List<ContractPayment>) map.getOrDefault("contractPayments", new ArrayList<>());
        DateTime ddStartDate = (DateTime) map.get("ddStartDate");
        boolean isOneTime = (boolean) map.get("isOneTime") ;
        Integer ontTimeDDMonthDuration = (Integer) map.get("ontTimeDDMonthDuration");
        ContractPaymentTerm cpt = (ContractPaymentTerm) map.get("contractPaymentTerm");

        String key = ddStartDate + "_" + contractPayments.get(0).getAmount();
        if (generatedPlans.contains(key)) {
            logger.info("a plan already created for start date: {0}" + ddStartDate.toString("yyyy-MM-dd HH:mm:ss") +
                    ", dd id: " + contractPayments.stream().map(ContractPayment::getId).collect(Collectors.toList()));
            return null;
        }
        generatedPlans.add(key);

        DirectDebitGenerationPlan directDebitGenerationPlanDd = new DirectDebitGenerationPlan();
        directDebitGenerationPlanDd.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING);
        directDebitGenerationPlanDd.setContractPaymentType((ContractPaymentType) map.get("contractPaymentType"));
        directDebitGenerationPlanDd.setContract(cpt.getContract());
        directDebitGenerationPlanDd.setSendNotificationToClient(true);
        directDebitGenerationPlanDd.setAmount(contractPayments.stream().mapToDouble(ContractPayment::getAmount).sum());
        directDebitGenerationPlanDd.setContractPaymentsCloned(contractPayments);
        directDebitGenerationPlanDd.setOneTime(isOneTime);
        directDebitGenerationPlanDd.setDDSendDate(new Date(ddStartDate.toDate().getTime()));
        directDebitGenerationPlanDd.setDDExpiryDate(
                new Date(isOneTime ?
                        ddStartDate.plusMonths(ontTimeDDMonthDuration).toDate().getTime() :
                        Setup.getApplicationContext()
                                .getBean(DirectDebitService.class)
                                .getEndDateOfDDB(cpt).toDate().getTime()));
        directDebitGenerationPlanDd.setDDGenerationDate((Date) map.get("ddGenerationDate"));
        return directDebitGenerationPlanDd;
    }

    //ACC-4742
    public boolean updatePlansAfterMonthlyPaymentReceived(
            ContractPaymentTerm contractPaymentTerm,
            DateTime paidEndDate) {

        logger.log(Level.SEVERE, "contractId : {0}", contractPaymentTerm.getContract().getId());
        SelectQuery<DirectDebitGenerationPlan> query = new SelectQuery<>(DirectDebitGenerationPlan.class);
        query.filterBy("contract.id", "=", contractPaymentTerm.getContract().getId());
        query.filterBy("ddGenerationPlanStatus", "=", DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING);
        query.filterBy("contractPaymentType.type.code", "=", "monthly_payment");
        
        List<DirectDebitGenerationPlan> directDebitGenerationPlans = query.execute();
        if (directDebitGenerationPlans.isEmpty()) return false;
        logger.log(Level.SEVERE,  "directDebitGenerationPlans size : {0}", directDebitGenerationPlans.size());
        
        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
        DateTime ddaStartDate = new DateTime(paidEndDate).plusMonths(1).withDayOfMonth(1);
        int postponePeriod = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
            AccountingModule.ADJUSTED_END_DATE_DD_GEN_PRE_POSTPONE_PERIOD));
        Date generationDate = new Date(ddaStartDate.minusDays(postponePeriod).toDate().getTime());
        
        
        directDebitGenerationPlans.forEach(ddp -> {
                    logger.log(Level.SEVERE,  "directDebitGenerationPlan id : {0}", ddp.getId());
                    if (ddp.getOneTime()) {
                        if (ddp.getDDSendDate().getTime() < paidEndDate.toDate().getTime())
                            ddp.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED);
                    } else {
                        if (ddp.getDDExpiryDate().getTime() < paidEndDate.plusMonths(1)
                            .withDayOfMonth(1).withTimeAtStartOfDay().toDate().getTime()) {
                            ddp.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED);
                            
                        } else if (new LocalDate(ddp.getDDExpiryDate()).toString("yyyy-MM")
                            .equals(new LocalDate(paidEndDate.plusMonths(1)).toString("yyyy-MM"))) {
                            ddp.setOneTime(true);
                            ddp.setDDSendDate(new Date(paidEndDate.plusMonths(1).dayOfMonth().withMinimumValue().toDate().getTime()));
                            
                        } else if (ddp.getDDSendDate().getTime() < paidEndDate.plusMonths(2).withDayOfMonth(1).toDate().getTime()
                                && ddp.getDDExpiryDate().getTime() > paidEndDate.plusMonths(2).withDayOfMonth(1).toDate().getTime()) {
                            ddp.setDDSendDate(new Date(ddaStartDate.plusMonths(1).toDate().getTime()));
    
                            Map<String, Object> m = new HashMap<>();
                            m.put("contract", contractPaymentTerm.getContract());
                            m.put("ontTimeDDMonthDuration", ontTimeDDMonthDuration);
                            m.put("contractPaymentType", ddp.getContractPaymentType());
                            m.put("ddGenerationDate", generationDate);
                            ddp.getDdCloned().setMStatus(ddp.getDdStatus());
                            m.put("directDebit", ddp.getDdCloned());
                            m.put("ddStartDate", ddaStartDate);
                            m.put("isOneTime", true);
                            generateMonthlyDdPlan(m, new HashSet<>());
                        }
                    }
                    ddp.setDDGenerationDate(generationDate);
                    directDebitGenerationPlanRepository.save(ddp);
                });
        return true;
    }

    //ACC-4742
    public void cancelAllMonthlyPlans(Long contractId) {

        SelectQuery<DirectDebitGenerationPlan> query = new SelectQuery<>(DirectDebitGenerationPlan.class);
        query.filterBy("contract.id", "=", contractId);
        query.filterBy("ddGenerationPlanStatus", "=", DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING);
        query.filterBy("contractPaymentType.type.code", "=", "monthly_payment");
        List<DirectDebitGenerationPlan> plans = query.execute();

        plans.forEach(p -> {
            logger.info("directDebitGenerationPlan id: " + p.getId());
            p.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED);
            directDebitGenerationPlanRepository.save(p);
        });
    }

    public void generateDdPassedInClientPayingViaCreditCard(Contract contract) {

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "generateDdPassedInClientPayingViaCreditCard" + new java.util.Date().getTime(),
                        "accounting",
                        "directDebitGenerationPlanService",
                        "generateDdPassedInClientPayingViaCreditCard")
                        .withRelatedEntity("contract", contract.getId())
                        .withParameters(
                                new Class[] {Long.class},
                                new Object[] {contract.getId()})
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .withDelay(120L * 1000L)
                        .build());
    }

    // ACC-5183
    @Transactional
    public void generateDdPassedInClientPayingViaCreditCard(Long contractId) {
        SelectQuery<DirectDebitGenerationPlan> query = new SelectQuery<>(DirectDebitGenerationPlan.class);
        query.filterBy("contract.id", "=", contractId);
        query.filterBy("ddGenerationPlanStatus", "=",
                DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING_PAYING_VIA_CREDIT_CARD);
        query.filterBy("ddSendDate", ">", new Date(new LocalDate().toDate().getTime()));

        query.execute().forEach(p -> generateDDFromGenerationPlan(p, new HashMap<>()));
    }

    public void cancelledNonMonthlyPlanUponPaymentReceived(Payment p) {
        logger.info("payment id: " + p.getId());

        // ACC-5183 ACC-6182
        SelectQuery<DirectDebitGenerationPlan> query = new SelectQuery<>(DirectDebitGenerationPlan.class);
        query.filterBy("contract.id", "=", p.getContract().getId());
        query.filterBy("ddGenerationPlanStatus", "in", Arrays.asList(
                DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING,
                DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING_PAYING_VIA_CREDIT_CARD));
        query.filterBy("ddSendDate", "=", p.getDateOfPayment());
        query.filterBy("contractPaymentType.type.code", "=", p.getTypeOfPayment().getCode());
        query.filterBy("amount", "=", p.getAmountOfPayment());

        query.execute().forEach(d -> {
            logger.info("directDebitGenerationPlan id: " + d.getId());
            d.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED);
            directDebitGenerationPlanRepository.save(d);
        });
    }

    // ACC-5825
    public void regeneratePlansUponCptChanged(ContractPaymentTerm cpt) {
        boolean hasActiveDdb = Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .hasActiveDdb(cpt.getContract().getId());

        directDebitGenerationPlanRepository
                .findByContractAndDdGenerationPlanStatusIn(cpt.getContract(), DirectDebitGenerationPlanService.pendingStatus)
                .forEach(p -> {
                    if(!hasActiveDdb && p.getPaymentType().getCode().equals("monthly_payment"))
                        regenerateMonthlyPlansUponCptChanged(p, cpt);

                    p.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED);
                    directDebitGenerationPlanRepository.saveAndFlush(p);
                });
    }

    // ACC-5825
    private void regenerateMonthlyPlansUponCptChanged(
            DirectDebitGenerationPlan p,
            ContractPaymentTerm cpt) {

        DirectDebitGenerationPlan newPlan = p.clone();
        newPlan.setId(null);
        newPlan.setDdCloned(null);
        newPlan.setDdStatus(DirectDebitStatus.IN_COMPLETE);
        Map<String, Object> m = Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, new LocalDate(p.getDDSendDate()));
        newPlan.setAmount((Double) m.get("amount"));
        newPlan.setAdditionalDiscountAmount((Double) m.getOrDefault("additionalDiscountAmountPerPayment", 0.0));
        newPlan.setMoreAdditionalDiscount((Double) m.getOrDefault("moreAdditionalDiscount", 0.0));
        newPlan.setContractPaymentType((ContractPaymentType) cpt.getMonthlyPaymentType());
        directDebitGenerationPlanRepository.save(newPlan);
    }

    @Transactional
    public List<ContractPayment> generateNewDd(
            ContractPaymentTerm cpt,
            DateTime d,
            boolean withDda) throws Exception {

        logger.log(Level.INFO,  "contract id : {0}; d: {1}", new Object[]{cpt.getContract().getId(), d});

        Map m = Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class)
                .getContractPaymentTermByContractWithDirectDebitPayments(cpt);

        List<ContractPayment> payments = (List<ContractPayment>) m.get("payments");
        Collections.reverse(payments);

        List<ContractPayment> l = Setup.getApplicationContext().getBean(ContractPaymentService.class)
                .getUniqueAndSortedPayments(cpt.getContract(), payments, d);
        Collections.reverse(l);

        payments.forEach(cp -> cp.setDirectDebit(null));
        // Make first payment Dda
        if (withDda && !l.isEmpty()) payments.get(0).setOneTime(true);

        Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class)
                .updateContractPaymentTermWithPayments(cpt, true,
                        false, l, null, false,
                        false, true, false,
                        false, true, false, new HashMap<>());

        return l;
    }

    public void updateMessagesAcc6444(MultipartFile file) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet == null) throw new BusinessException("No sheet found");

        AccountingTemplateService accountingTemplateService =
                Setup.getApplicationContext().getBean(AccountingTemplateService.class);
        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;
            try {
                logger.info("Row Num: " + row.getRowNum());

                if (row.getCell(0).getStringCellValue() == null ||
                        row.getCell(0).getStringCellValue().isEmpty()) break;

                String n = row.getCell(1).getStringCellValue();
                String s = row.getCell(2).getStringCellValue();
                switch (row.getCell(0).getStringCellValue()) {
                    case "insurance":
                        accountingTemplateService.updateNewModelTemplateForAcc6444(
                                DirectDebitGenerationPlanTemplate.INSURANCE.toString(),
                                n, s, 2);
                        accountingTemplateService.updateNewModelTemplateForAcc6444(
                                MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_INSURANCE_NOTIFICATION.toString(),
                                n, s, 2);
                        break;
                    case "same_day_recruitment_fee":
                        accountingTemplateService.updateNewModelTemplateForAcc6444(
                                DirectDebitGenerationPlanTemplate.SAME_DAY_RECRUITMENT_FEE.toString(),
                                n, s, 2);
                        accountingTemplateService.updateNewModelTemplateForAcc6444(
                                MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_SAME_DAY_RECRUITMENT_FEE_NOTIFICATION.toString(),
                                n, s, 2);
                        break;
                    case "other_type":
                        accountingTemplateService.updateNewModelTemplateForAcc6444(
                                DirectDebitGenerationPlanTemplate.OTHER_DD_TYPE.toString(),
                                n, s, 2);
                        accountingTemplateService.updateNewModelTemplateForAcc6444(
                                MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_OTHER_DD_TYPE_NOTIFICATION.toString(),
                                n, s, 2);
                        break;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void dateCorrectionAcc6444(String email, boolean withSave) throws IOException {
        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
        int startDayPeriod  = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DD_GEN_POSTPONE_START_AFTER_X_DAY));

        ParameterRepository parameterRepository = Setup.getRepository(ParameterRepository.class);
        Parameter postponePeriod = parameterRepository.findByModuleAndCode(Setup.getCurrentModule(), AccountingModule.DD_GEN_PRE_POSTPONE_PERIOD);
        if (postponePeriod.getValue().equals("2")) {
            postponePeriod.setValue("1");
            parameterRepository.save(postponePeriod);
        }

        List<DirectDebitGenerationPlaneMigrationReportCSV> csvDataList = new ArrayList<>();
        List<DirectDebitGenerationPlan> l;
        Long lastId = -1L;
        int number = 1;
        do {
            SelectQuery<DirectDebitGenerationPlan> q = new SelectQuery<>(DirectDebitGenerationPlan.class);
            q.filterBy("id", ">", lastId);
            q.filterBy("ddGenerationPlanStatus", "=", DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING);
            q.filterBy("ddCloned", "is null", null);
            q.filterBy("ddID", "is null", null);
            q.filterBy("contractPaymentType.type.code", "<>", "monthly_payment");
            q.filterBy("contract.status", "not in", Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED));
            q.setLimit(200);
            l = q.execute();

            for (DirectDebitGenerationPlan p : l) {
                DirectDebitGenerationPlaneMigrationReportCSV d = new DirectDebitGenerationPlaneMigrationReportCSV();
                d.setNumber(number++);
                d.setContractId(p.getContract().getId().toString());
                d.setPlanId(p.getId().toString());
                d.setDdSendDate(new LocalDate(p.getDDSendDate()).toString("yyyy-MM-dd"));
                d.setDdExpiryDate(new LocalDate(p.getDDExpiryDate()).toString("yyyy-MM-dd"));
                d.setDdGenerationDate(new LocalDate(p.getDDGenerationDate()).toString("yyyy-MM-dd"));
                d.setNotes("");
                try {
                    if (Months.monthsBetween(new LocalDate(p.getDDGenerationDate()), new LocalDate(p.getDDSendDate())).getMonths() < 2) continue;

                    int contractStartDay = new LocalDate(p.getContract().getStartOfContract()).getDayOfMonth();
                    LocalDate ddStartDate = new LocalDate(p.getDDSendDate()).withDayOfMonth(contractStartDay).plusDays(startDayPeriod);

                    p.setDDSendDate(java.sql.Date.valueOf(ddStartDate.toString("yyyy-MM-dd")));
                    p.setDDExpiryDate(java.sql.Date.valueOf(ddStartDate.plusMonths(ontTimeDDMonthDuration).toString("yyyy-MM-dd")));
                    p.setDDGenerationDate(java.sql.Date.valueOf(ddStartDate.minusMonths(1).minusDays(startDayPeriod).toString("yyyy-MM-dd")));

                    logger.info("plan id: " + p.getId() +
                            "; DDSendDate: " + new LocalDate(p.getDDSendDate()).toString("yyyy-MM-dd") +
                            "; DDExpiryDate: " + new LocalDate(p.getDDExpiryDate()).toString("yyyy-MM-dd") +
                            "; DDGenerationDate: " + new LocalDate(p.getDDGenerationDate()).toString("yyyy-MM-dd"));

                    if (new LocalDate(p.getDDGenerationDate()).isBefore(new LocalDate())) {
                        d.setNotes("DD generation date before current date");
                    }
                    if (new LocalDate(p.getDDSendDate()).isBefore(new LocalDate())) {
                        d.setNotes(d.getNotes() + (d.getNotes().isEmpty() ? "" : ", ") +
                                "DD start date before current date");
                    }
                    if (new LocalDate(p.getDDExpiryDate()).isBefore(new LocalDate())) {
                        d.setNotes(d.getNotes() + (d.getNotes().isEmpty() ? "" : ", ") +
                                "DD expiry date before current date");
                    }

                    d.setNewDdSendDate(new LocalDate(p.getDDSendDate()).toString("yyyy-MM-dd"));
                    d.setNewDdExpiryDate(new LocalDate(p.getDDExpiryDate()).toString("yyyy-MM-dd"));
                    d.setNewDdGenerationDate(new LocalDate(p.getDDGenerationDate()).toString("yyyy-MM-dd"));

                    if (withSave) {
                        saveGenerationPlan(p, new HashMap<>());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    d.setNewDdSendDate("Not updated");
                    d.setNewDdExpiryDate("Not updated");
                    d.setNewDdGenerationDate("Not updated");
                    d.setNotes("An error happened: " + e.getMessage());
                }

                csvDataList.add(d);
            }

            if (!l.isEmpty()) lastId = l.get(l.size() - 1).getId();

        } while (!l.isEmpty());

        if (email == null || email.isEmpty()) return;
        String[] headers = {"#", "Contract ID", "Plan ID",
                "DD Generation Date", "DD Send Date", "DD Expiry Date",
                "New DD Generation Date", "New DD Send Date", "New DD Expiry Date", "Notes"};

        String[] names = {"number", "contractId", "planId",
                "ddGenerationDate", "ddSendDate", "ddExpiryDate",
                "newDdGenerationDate", "newDdSendDate", "newDdExpiryDate", "notes"};

        File file = CsvHelper.generateCsv(csvDataList, DirectDebitGenerationPlaneMigrationReportCSVProjection.class, headers, names,
                "Updates on Postponed DDs Logic Migration Data Report_" + new DateTime().toString("yyyy-MM-dd HH:mm:ss"), ".csv");
        TextEmail mail = new TextEmail("Updates on Postponed DDs Logic Migration Data Report", "");
        mail.addAttachement(file);
        Setup.getMailService()
                .sendEmail(new Recipient(email, email), mail, EmailReceiverType.Office_Staff);
    }

    // ACC-5825
    public void updatePlansUponActiveCptChanged() {
        logger.log(Level.INFO, "Start");
        Long lastCptId = -1L;
        List<ContractStatus> allowedStatus = Arrays.asList(ContractStatus.ACTIVE,
                ContractStatus.PLANNED_RENEWAL, ContractStatus.POSTPONED);

        List<ContractPaymentTerm> page = directDebitGenerationPlanRepository.findInactiveCptHasActivePlan(
                lastCptId, DirectDebitGenerationPlanService.pendingStatus, allowedStatus, PageRequest.of(0, 100)).getContent();

        while (!page.isEmpty()) {
            page.forEach(c -> {
                logger.log(Level.INFO, "cpt id; {0}", c.getId());
                regeneratePlansUponCptChanged(
                        c.getContract().getActiveContractPaymentTerm());
            });

            lastCptId = page.get(page.size() - 1).getId();
            page = directDebitGenerationPlanRepository.findInactiveCptHasActivePlan(
                    lastCptId, DirectDebitGenerationPlanService.pendingStatus, allowedStatus, PageRequest.of(0, 100)).getContent();
        }

        logger.log(Level.INFO, "End");
    }

    @Transactional
    public void generatePlansForFutureContractPayments(ContractPaymentTerm cpt, List<ContractPayment> contractPayments, Map<String, Object> m) {

        if (contractPayments == null || contractPayments.isEmpty()) return;
        logger.info("contractPayments size: " + contractPayments.size());

        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
        int postponePeriod = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.DD_GEN_PRE_POSTPONE_PERIOD));
        int startDayPeriod  = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DD_GEN_POSTPONE_START_AFTER_X_DAY));

        Map<String, Object> map = new HashMap<>();
        map.put("ontTimeDDMonthDuration", ontTimeDDMonthDuration);
        map.put("postponePeriod", postponePeriod);
        map.put("startDayPeriod", startDayPeriod);
        map.put("contractPaymentTerm", cpt);

        Set<String> generatedPlans = new HashSet<>();
        List<DirectDebitGenerationPlan> newPlans = new ArrayList<>();
        contractPayments
                .stream()
                .collect(Collectors.groupingBy(cp -> cp.getDirectDebit().getId()))
                .forEach((ddId, payments) -> newPlans.add(generatePlanFromContractPayments(payments, generatedPlans, map)));

        logger.info("newPlans size: " + newPlans.size());

        ContractPaymentRepository contractPaymentRepository = Setup.getRepository(ContractPaymentRepository.class);
        newPlans.forEach(p -> {
            saveGenerationPlan(p, m);
            // Check if the Generation Plan has been successfully saved.
            if(p.getId() != null) {
                p.getContractPaymentsCloned()
                        .forEach(cp -> {
                            cp.setDirectDebitGenerationPlan(p);
                            contractPaymentRepository.save(cp);
                        });
            }
        });
    }

    public DirectDebitGenerationPlan generatePlanFromContractPayments(List<ContractPayment> contractPayments, Set<String> generatedPlans, Map<String, Object> m) {

        DateTime ddaStartDate = new DateTime(contractPayments.get(0).getDirectDebit().getStartDate());
        Date generationDate = new Date(ddaStartDate.minusDays((int) m.get("postponePeriod") + (int) m.get("startDayPeriod")).toDate().getTime());

        m.put("ddGenerationDate", generationDate);
        m.put("contractPayments", contractPayments);
        m.put("ddStartDate", ddaStartDate);
        m.put("isOneTime", true);
        return generateDdPlanFromContractPaymentsWithoutSave(m, generatedPlans);
    }
}
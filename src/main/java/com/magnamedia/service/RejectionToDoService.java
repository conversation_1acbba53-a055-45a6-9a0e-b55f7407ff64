package com.magnamedia.service;

import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */

@Service
public class RejectionToDoService {
    
    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;
    @Autowired
    private DirectDebitRepository ddRepository;
    
    public void stopDDRejectionToDos(Contract contract) {
        ContractPaymentTerm activeCPT = contract.getActiveContractPaymentTerm();
        List<DirectDebit> dds = ddRepository.findByContractPaymentTerm(activeCPT);
        for (DirectDebit dd : dds) {
            DirectDebitRejectionToDo directDebitRejectionToDo = dd.getDirectDebitRejectionToDo();
            if (directDebitRejectionToDo != null) {
                stopDDRejectionToDo(directDebitRejectionToDo);
            }

            DirectDebitRejectionToDo directDebitBouncingRejectionToDo = dd.getDirectDebitBouncingRejectionToDo();
            if (directDebitBouncingRejectionToDo != null) {
                stopDDRejectionToDo(directDebitBouncingRejectionToDo);
            }
        }
    }
    
    private void stopDDRejectionToDo(DirectDebitRejectionToDo ddRejectionToDo) {
        ddRejectionToDo.setCompleted(true);
        ddRejectionToDo.setStopped(true);
        directDebitRejectionToDoRepository.save(ddRejectionToDo);
    }
    
}

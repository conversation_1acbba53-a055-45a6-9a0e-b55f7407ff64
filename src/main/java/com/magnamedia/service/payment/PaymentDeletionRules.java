package com.magnamedia.service.payment;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.extra.ContractScheduleForTerminationUtils;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.ClientMessagingAndRefundService;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

// ACC-6527
@Service
public class PaymentDeletionRules {

    protected static final Logger logger = Logger.getLogger(PaymentDeletionRules.class.getName());

    @Autowired
    private ContractRepository contractRepository;

    public Map<String, Object> handlePayment(Payment payment) {

        return validate(payment) ? execute(payment) : new HashMap<>();
    }

    private boolean validate(Payment entity) {
        logger.info("payment id: " + entity.getId());
        Contract contract = contractRepository.findOne(entity.getContract().getId());

        if (!entity.getStatus().equals(PaymentStatus.BOUNCED) || entity.isReplaced()) {
            return false;
        }

        // ACC-7313
        if (contract.getFreezingDate() != null) {
            logger.info("freeze contract");
            return true;
        }

        if(!Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED)
                .contains(contract.getStatus()) ||
                contract.getDateOfTermination() == null ||
                entity.getPaymentScheduleDateOfTerminationValue() != null) {
            logger.info("contract is active");
            return false;
        }

        if (contract.isCancelledWithinFirstXDays()) {
            logger.info("contract isCancelledWithinFirstXDays");
            return true;
        }

        Map<String, Object> proratedContractConditions = Setup.getApplicationContext()
                .getBean(ClientMessagingAndRefundService.class)
                .checkProratedContractConditions(
                contract, contract.getDateOfTermination());

        if ((boolean) proratedContractConditions.get("clientCancelledWithinXDays") &&
                !new LocalDate(entity.getDateOfPayment())
                        .isBefore(new LocalDate(contract.getDateOfTermination())) &&
                new LocalDate(contract.getPaidEndDate())
                        .isAfter(new LocalDate(contract.getDateOfTermination())
                                .minusMonths(1).dayOfMonth().withMinimumValue())) {
            logger.info("contract is clientCancelledWithinXDays");
            return true;
        }

        // If a CC or MV client gets terminated due to bounced payment and he doesn't settle the payment later,
        // then only that payment should be kept as bounced and all later months payments should be deleted once they get bounced.
        return contract.getReasonOfTerminationList() != null &&
                ContractScheduleForTerminationUtils.isTerminationReasonDueBouncedPayment(
                        contract.getReasonOfTerminationList().getCode()) &&
                new DateTime(entity.getDateOfPayment()).plusMillis(1)
                        .isAfter(new DateTime(contract.getDateOfTermination()).plusMonths(1).dayOfMonth().withMinimumValue().withTimeAtStartOfDay());
    }


    public Map<String, Object> execute(Payment entity) {
        logger.info("payment id: " + entity.getId());
        Map<String, Object> result = new HashMap<>();

        result.put("status", PaymentStatus.DELETED.toString());
        return result;
    }
}
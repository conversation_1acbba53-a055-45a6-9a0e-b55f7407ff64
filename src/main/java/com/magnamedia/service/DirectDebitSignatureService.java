package com.magnamedia.service;

import com.aspose.words.ConvertUtil;
import com.google.common.collect.Iterables;
import com.magnamedia.controller.ContractController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.Fingerprint;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.entity.AppsServiceDDApprovalTodo.DdcTodoType;
import com.magnamedia.extra.DDSignatureTempDto;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.module.type.DirectDebitSignatureStatus;
import com.magnamedia.repository.*;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.io.IOUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.Raster;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR> Mahfoud
 */

@Service
public class DirectDebitSignatureService {

    protected static final Logger logger = Logger.getLogger(DirectDebitSignatureService.class.getName());

    @Autowired
    private Utils utils;
    @Autowired
    private DirectDebitSignatureRepository directDebitSignatureRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private AttachementRepository attachementRepository;
    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRepository;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private InterModuleConnector interModuleConnector;
    @Autowired
    private DirectDebitRepository directDebitRepository;

    public List<DirectDebitSignature> saveNewDirectDebitFileSignatures(
            List<Attachment> signatures,
            ContractPaymentTerm cpt) {

        List<byte[]> signatureByteArrays = new ArrayList<>();
        List<DirectDebitSignature> directDebitSignatures = new ArrayList<>();
        logger.log(Level.INFO, "start cpt id " + cpt.getId());

        List<DirectDebitSignature> availableSignatures = getSignatureSortedList(cpt.getContract().getClient(), cpt.getEid());

        if (signatures == null || signatures.isEmpty()) {
            logger.info("no new signatures; available signatures size " + availableSignatures.size());
            return availableSignatures.isEmpty() ? null : availableSignatures;
        }

        //ACC-5196
        logger.info("available signatures size " + availableSignatures.size());
        logger.info("signatures size " + signatures.size());

        List<DirectDebitSignature> allClientSignatures =
                directDebitSignatureRepository.findOldSignaturesByClient(cpt.getContract().getClient());
        // ACC-5798
        signatures.stream()
                .sorted(Comparator.comparing(BaseEntity::getId))
                .forEach(signature -> {
                    try {
                        signatureByteArrays.add(
                                getImageByteArray(utils.getInputStreamFromAttachmentOrMultiPartFile(signature)));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
        });
        logger.info("signatureByteArrays size " + signatureByteArrays.size());

        Map<Long, String> uniqueSignatures = new HashMap<>();
        if (!allClientSignatures.isEmpty()) {
            for (DirectDebitSignature sign : allClientSignatures) {
                Attachment attachment = sign.getSignatureAttachment();
                if (attachment == null) continue;

                InputStream stream = Storage.getStream(attachment);
                byte[] imageBytes = getImageByteArray(stream);
                String encoded = Base64.getEncoder().encodeToString(imageBytes);
                if (uniqueSignatures.values().contains(encoded)) continue;

                uniqueSignatures.put(sign.getId(), encoded);
            }
        }
        logger.info("uniqueSignatures size " + uniqueSignatures.values().size());

        int order = 1;
        int setOrder = directDebitSignatureRepository.getMaxSetOrderByContract(cpt.getContract()) + 1;
        boolean acc7535Done = cpt.getContract().getAttachments()
                .stream()
                .anyMatch(att -> att.getTag().equals(ContractController.SIGNATURE_FILE_TAG_NAME));

        for (byte[] signatureByte : signatureByteArrays) {
            String encoded = Base64.getEncoder().encodeToString(signatureByte);
            if (uniqueSignatures.values().contains(encoded)) {
                DirectDebitSignature oldSign = uniqueSignatures.entrySet().stream()
                        .filter(e -> e.getValue().equals(encoded))
                        .map(e -> directDebitSignatureRepository.findOne(e.getKey()))
                        .findFirst().orElse(null);

                // ACC-7537
                if (oldSign != null &&
                        !oldSign.getSignatureStatus().equals(DirectDebitSignatureStatus.REJECTED) &&
                        !oldSign.isDisable()) {
                    logger.info("matched with old one id: " + oldSign.getId());

                    directDebitSignatures.add(oldSign);
                    continue;
                }
            }

            InputStream signatureStream = new ByteArrayInputStream(signatureByte);
            try {
                Attachment signatureAttachment = Storage.storeTemporary("Direct Debit Signature.png",
                        signatureStream, DirectDebitFile.FILE_TAG_DD_SIGNATURE, true, true);
                logger.log(Level.SEVERE, "signatureAttachment id " + signatureAttachment.getId());

                DirectDebitSignature sign = new DirectDebitSignature();
                sign.setEid(cpt.getEid());
                sign.addAttachment(signatureAttachment);
                sign.setContractPaymentTerm(cpt);
                sign.setSignatureOrder(order++); // ACC-5812
                sign.setSetOrder(setOrder);
                sign = directDebitSignatureRepository.saveAndFlush(sign);

                sign.setNewSignature(true);

                directDebitSignatures.add(sign);
                uniqueSignatures.put(sign.getId(), encoded);
                if (acc7535Done) continue;
                generateContractSignatureFileBgt(cpt.getContract(), sign.getId());
                acc7535Done = true;
            } finally {
                StreamsUtil.closeStream(signatureStream);
            }
        }

        logger.info("returned signatures size " + directDebitSignatures.size());
        return directDebitSignatures.isEmpty() ? null : directDebitSignatures;
    }

    private void generateContractSignatureFileBgt(Contract contract, Long signatureId) {

        backgroundTaskService.create(new BackgroundTask.builder(
                "generateContractSignatureFile_" + contract.getId(),
                "accounting",
                "directDebitSignatureService",
                "generateContractSignatureFile")
                .withRelatedEntity(contract.getEntityType(), contract.getId())
                .withParameters(new Class[] {Long.class, Long.class},
                        new Object[] {contract.getId(), signatureId } )
                .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                .withDelay(3 * 60 * 1000L)
                .build());
    }

    public void generateContractSignatureFile(Long contractId, Long signatureId) {
        logger.info("contract id : " + contractId);
        Contract c = contractRepository.findOne(contractId);
        DirectDebitSignature signature = directDebitSignatureRepository.findOne(signatureId);

        Attachment signatureAttachmentForContract = Storage.storeTemporary("Contract Signature.png",
                Storage.getStream(signature.getSignatureAttachment()),
                ContractController.SIGNATURE_FILE_TAG_NAME, true, true);

        c.addAttachment(signatureAttachmentForContract);
        contractRepository.save(c);

        interModuleConnector.getAsync("/sales/contract/generatecontractfiles/" + c.getId(), Object.class);
    }

    public DirectDebitSignature getLastRejectedSignature(ContractPaymentTerm cpt) {
        logger.log(Level.INFO, "cpt id " + cpt.getId() + "; eid " + cpt.getEid());

        List<DirectDebitSignature> rejectedSignatures = directDebitSignatureRepository.findRejectedSignaturesByClient(
                cpt.getContract().getClient(), cpt.getEid());

        logger.log(Level.INFO, "rejected Signatures size: " + rejectedSignatures.size());
        return rejectedSignatures.isEmpty() ? null : rejectedSignatures.get(rejectedSignatures.size() - 1);
    }

    public Map<String, Object> getLastSignatureTypeByEid(ContractPaymentTerm cpt, boolean withSignatures, boolean justApproved, String eid) {
        logger.log(Level.INFO, "cpt id " + cpt.getId() + "; eid " + eid);

        return getLastSignatureTypeProcess(
                getSignatureSortedList(cpt.getContract().getClient(), eid),
                withSignatures, justApproved);
    }

    public Map<String, Object> getLastSignatureType(ContractPaymentTerm cpt, boolean withSignatures, boolean justApproved) {
        logger.log(Level.INFO, "cpt id " + cpt.getId() + "; eid " + cpt.getEid());

        return getLastSignatureTypeProcess(
                getSignatureSortedList(cpt.getContract().getClient(), cpt.getEid()),
                withSignatures, justApproved);
    }

    public Map<String, Object> getLastSignatureType(Client client, String eid, boolean withSignatures, boolean justApproved) {
        logger.log(Level.INFO, "client id " + client.getId());
        return getLastSignatureTypeProcess(
                getSignatureSortedList(client, eid),
                withSignatures, justApproved);
    }

    public Map<String, Object> getLastSignatureType(Contract contract, String eid, boolean withSignatures, boolean justApproved) {
        logger.log(Level.INFO, "contract id " + contract.getId());
        return getLastSignatureTypeProcess(
                directDebitSignatureRepository.findSignaturesByContractWithDdf(contract, eid),
                withSignatures, justApproved);
    }

    public Map<String, Object> getLastSignatureTypeConsiderDisabled(ContractPaymentTerm cpt, boolean withSignatures, boolean justApproved) {
        logger.log(Level.INFO, "cpt id " + cpt.getId() + "; eid " + cpt.getEid());

        return getLastSignatureTypeProcess(
                getSignatureSortedListIgnoreDisabled(cpt.getContract().getClient(), cpt.getEid()),
                withSignatures, justApproved);
    }

    public List<DirectDebitSignature> getSignatureSortedList(Client client, String eid) {
        return Stream.of(directDebitSignatureRepository.findSignaturesByClientWithDdf(client, eid, false),
                        directDebitSignatureRepository.findSignaturesByClientWithoutDdf(client, eid, false))
                .flatMap(Collection::stream)
                .sorted(Comparator.comparingInt(this::getSortedValue)).collect(Collectors.toList());
    }

    public List<DirectDebitSignature> getDisabledSignatureSortedList(Client client, String eid) {
        return Stream.of(directDebitSignatureRepository.findSignaturesByClientWithDdf(client, eid, true),
                        directDebitSignatureRepository.findSignaturesByClientWithoutDdf(client, eid, true))
                .flatMap(Collection::stream)
                .sorted(Comparator.comparingInt(this::getSortedValue)).collect(Collectors.toList());
    }

    public List<DirectDebitSignature> getSignatureSortedListIgnoreDisabled(Client client, String eid) {
        return Stream.of(directDebitSignatureRepository.findSignaturesByClientWithDdfConsiderDisabled(client, eid),
                        directDebitSignatureRepository.findSignaturesByClientWithoutDdfConsiderDisabled(client, eid))
                .flatMap(Collection::stream)
                .sorted(Comparator.comparingInt(this::getSortedValue)).collect(Collectors.toList());
    }

    private int getSortedValue(DirectDebitSignature directDebitSignature) {
        switch (directDebitSignature.getSignatureStatus()) {
            case APPROVED: return 1;
            case UNUSED: return 2;
            case UNDER_PROCESS: return 3;
            default: return 4;
        }
    }

    public Map<String, Object> getLastSignatureTypeProcess(
            List<DirectDebitSignature> directDebitSignatures,
            boolean withSignatures,
            boolean justApproved) {

        Map<String, Object> result = new HashMap<>();

        result.put("useApprovedSignature", directDebitSignatures.stream().anyMatch(directDebitSignature ->
                directDebitSignature.getSignatureStatus() == DirectDebitSignatureStatus.APPROVED));

        result.put("useNonRejectedSignature", directDebitSignatures.stream().anyMatch(directDebitSignature ->
                Arrays.asList(DirectDebitSignatureStatus.UNUSED, DirectDebitSignatureStatus.UNDER_PROCESS)
                        .contains(directDebitSignature.getSignatureStatus())));

        logger.log(Level.SEVERE, "directDebitSignatures size " + directDebitSignatures.size());

        if (!directDebitSignatures.isEmpty()) {
            if (justApproved) {
                directDebitSignatures = directDebitSignatures.stream()
                    .filter(dds -> dds.getSignatureStatus() == DirectDebitSignatureStatus.APPROVED)
                    .collect(Collectors.toList());
            } else {
                int signatureNumber = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES));
                directDebitSignatures = directDebitSignatures.stream().limit(signatureNumber).collect(Collectors.toList());
            }
        }

        logger.log(Level.SEVERE, "directDebitSignatures size " + directDebitSignatures.size());

        if (withSignatures) {
            result.put("currentSignatures", directDebitSignatures.isEmpty() ? null : directDebitSignatures);
        }

        return result;
    }

    public List<Attachment> getSignatureAttachmentsOnly(List<DirectDebitSignature> directDebitSignatures) {
        List<Attachment> signatures = new ArrayList<>();

        if (directDebitSignatures != null) {
            for (DirectDebitSignature directDebitSignature : directDebitSignatures) {
                signatures.add(directDebitSignature.getSignatureAttachment());
            }

            return signatures;
        }
        return null;
    }

    public byte[] getImageByteArray(InputStream inputStream) {
        byte[] bytes = null;
        try {
            bytes = IOUtils.toByteArray(inputStream);
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }
        return bytes;
    }

    public DirectDebitSignature selectSignature(
            List<DirectDebitSignature> directDebitSignatures,
            Integer numOfDDfs,
            Integer numberCurrentDDM,
            Integer numberCurrentDDF) {

        if (directDebitSignatures != null) {
            directDebitSignatures = directDebitSignatures.stream()
                    .filter(s -> {
                        logger.info("status: " + s.getSignatureStatus() + "; approvedOnce: " + s.isApprovedOnce());
                        return s.getSignatureStatus() != DirectDebitSignatureStatus.REJECTED || s.isApprovedOnce();
                    })
                    //ACC-5196
                    .collect(Collectors.toList());

            logger.log(Level.SEVERE, "directDebitSignatures size " + directDebitSignatures.size());
            logger.log(Level.SEVERE, "numOfDDFs " + numOfDDfs);
            logger.log(Level.SEVERE, "numberCurrentDDM " + numberCurrentDDM);
            logger.log(Level.SEVERE, "numberCurrentDDF " + numberCurrentDDF);

            if (!directDebitSignatures.isEmpty()) {
                Integer index = ((numberCurrentDDM * numOfDDfs) + numberCurrentDDF)
                        % directDebitSignatures.size();

                DirectDebitSignature directDebitSignature = directDebitSignatures.get(index);
                directDebitSignature = directDebitSignatureRepository.findOne(directDebitSignature.getId());
                directDebitSignature.setUsedNumber(directDebitSignature.getUsedNumber() + 1);
                directDebitSignatureRepository.save(directDebitSignature);

                logger.log(Level.SEVERE, "directDebitSignatures " + directDebitSignature.getId());
                return directDebitSignature;
            }
        }

        return null;
    }

    public void updateSignatureStatus(
        DirectDebitFile ddf, DirectDebitSignatureStatus signatureStatus) {

        DirectDebitSignature directDebitSignature = ddf.getDirectDebitSignature();
        logger.log(Level.SEVERE, "directDebitSignature id " + directDebitSignature.getId());
        logger.log(Level.SEVERE, "updateSignatureStatus old status " + directDebitSignature.getSignatureStatus());

        switch (signatureStatus) {
            case UNUSED:
                List<DirectDebitFile> ddfs = directDebitFileRepository
                    .findByDirectDebitSignatureAndStatusIsSent(ddf.getId(),directDebitSignature);

                if(!ddfs.isEmpty()) return;
                break;
            case APPROVED:
                directDebitSignature.setApprovedOnce(true);
                break;
        }

        directDebitSignature.setSignatureStatus(signatureStatus);
        logger.log(Level.SEVERE, "new status " + directDebitSignature.getSignatureStatus());

        directDebitSignatureRepository.save(directDebitSignature);
    }

    public void dataMigrationACC4493SecondStep(Integer limit) {
        int page = 0;
        Page<Long> cptIds = directDebitSignatureRepository.getNotCorrectedCptSecondStep(PageRequest.of(page, 50));
        logger.log(Level.INFO, "cpts size {0}", cptIds.getSize());

        int i = 0;
        while (cptIds.hasContent() && i < limit) {
            i++;
            Long lastCptId = Iterables.getLast(cptIds);

            try {
                logger.log(Level.INFO, "create background task cpt id {0}", lastCptId);

                backgroundTaskService.create(new BackgroundTask.builder(
                        "dataMigrationACC4493SecondStep_" + lastCptId,
                        "accounting",
                        "directDebitSignatureService",
                        "dataMigrationACC4493ProcessSS")
                        .withRelatedEntity( "ContractPaymentTerm", null)
                        .withParameters( new Class<?>[] {List.class},
                                new Object[] { cptIds.stream().map(x -> x.toString()).collect(Collectors.toList()) } )
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .build());
            } catch (Exception e) {
                e.printStackTrace();
            }

            cptIds = directDebitSignatureRepository.getNotCorrectedCptSecondStep(PageRequest.of(++page, 50));
        }

        logger.log(Level.INFO, "end create background tasks");
    }


    public void dataMigrationACC4493ProcessSS(List<String> cptIds) {
        logger.log(Level.INFO, "cptIds ids {0}", String.join(",", cptIds));

        cptIds.forEach(id -> {
            try {
                dataMigrationACC4493SingleProcessSS(Long.parseLong(id));
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    @Transactional
    public void dataMigrationACC4493SingleProcessSS(Long cptId) {
        logger.log(Level.INFO, "cptId: {0}", cptId);

        if (directDebitSignatureRepository.existsByContractPaymentTerm_Id(cptId))
            return;
        
        List<DDSignatureTempDto> ddSignatureTempList = directDebitSignatureRepository.getNotCorrectedCPTSignAttachments(cptId);
        logger.log(Level.INFO, "ddfList size: {0}", ddSignatureTempList.size());
        
        ContractPaymentTerm contractPaymentTerm = contractPaymentTermRepository.findOne(cptId);
        Map<Long, DirectDebitSignature> newSigns = new HashMap<>();
        
        ddSignatureTempList.forEach(tempSign -> {
             logger.log(Level.INFO, "DirectDebitFileId: {0}; eid: {1}; status: {2}", new Object[]{tempSign.getDirectDebitFileId(),
                     tempSign.getEid(), tempSign.getDirectDebitFileStatus()});

            if (!newSigns.containsKey(tempSign.getNewAttachmentId())) {
                DirectDebitSignature directDebitSignature = new DirectDebitSignature();
                directDebitSignature.addAttachment(attachementRepository.findOne(tempSign.getNewAttachmentId()));
                directDebitSignature.setContractPaymentTerm(contractPaymentTerm);
                directDebitSignature.setEid(tempSign.getEid());
                switch (tempSign.getDirectDebitFileStatus()) {
                    case REJECTED:
                        directDebitSignature.setSignatureStatus(DirectDebitSignatureStatus.REJECTED);
                        break;
                    case APPROVED:
                        directDebitSignature.setSignatureStatus(DirectDebitSignatureStatus.APPROVED);
                        break;
                    case SENT:
                        directDebitSignature.setSignatureStatus(DirectDebitSignatureStatus.UNDER_PROCESS);
                        break;
                    case NOT_COMPLETED:
                    case NOT_SENT:
                        directDebitSignature.setSignatureStatus(DirectDebitSignatureStatus.UNUSED);
                        break;
                }
                directDebitSignature.setUsedNumber(1);
                directDebitSignature = directDebitSignatureRepository.save(directDebitSignature);
                newSigns.put(tempSign.getNewAttachmentId(), directDebitSignature);
            }

            DirectDebitFile directDebitFile = directDebitFileRepository.findOne(tempSign.getDirectDebitFileId());
            directDebitFile.setDirectDebitSignature(newSigns.get(tempSign.getNewAttachmentId()));
            directDebitFileRepository.save(directDebitFile);
        });
        
        logger.log(Level.SEVERE, "end cpt id: {0}", cptId);
    }

    public void redistributionOfSignatures(List<DirectDebit> ddms, List<DirectDebitSignature> directDebitSignatures) {
        if (ddms == null) return;
        logger.log(Level.SEVERE, "ddms size " + ddms.size());

        for (int ddmIndex = 0; ddmIndex < ddms.size(); ddmIndex++) {
            logger.log(Level.INFO, "DD ID: " + ddms.get(ddmIndex).getId());
            List<DirectDebitFile> ddfs = directDebitFileRepository.findByDirectDebit_Id(
                    ddms.get(ddmIndex).getId());

            if (ddfs.isEmpty()) continue;

            logger.log(Level.INFO, "All ddfs size: " + ddfs.size());

            if(directDebitSignatures != null && !directDebitSignatures.isEmpty()) {
                redistributionOfSignaturesProcess(ddfs.stream().filter(directDebitFile ->
                                directDebitFile.getDdMethod().equals(DirectDebitMethod.AUTOMATIC))
                        .sorted(Comparator.comparing(DirectDebitFile::getApplicationId))
                        .collect(Collectors.toList()), directDebitSignatures, ddmIndex);

                redistributionOfSignaturesProcess(ddfs.stream().filter(directDebitFile ->
                                directDebitFile.getDdMethod().equals(DirectDebitMethod.MANUAL))
                        .sorted(Comparator.comparing(DirectDebitFile::getApplicationId)).
                        collect(Collectors.toList()), directDebitSignatures, ddmIndex);
            }

            ddfs.forEach(f -> regenerateActivationFile(f));
        }
    }

    private void redistributionOfSignaturesProcess(
            List<DirectDebitFile> ddfs,
            List<DirectDebitSignature> directDebitSignatures,
            int ddmIndex) {

        logger.log(Level.SEVERE, "ddfs size " + ddfs.size());

        for (int ddfIndex = 0; ddfIndex < ddfs.size(); ddfIndex++) {
            DirectDebitFile ddf = ddfs.get(ddfIndex);
            logger.log(Level.SEVERE, "ddf id " + ddf.getId());

            ddf.setDirectDebitSignature(selectSignature(
                    directDebitSignatures, ddfs.size(), ddmIndex, ddfIndex));
        }
    }

    private void regenerateActivationFile(DirectDebitFile ddf) {
        Attachment ddActivationFile = ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION);

        if (ddActivationFile != null) {
            logger.log(Level.SEVERE, "has a direct debit activation file true");
            attachementRepository.delete(ddActivationFile);
            ddf.getAttachments().remove(ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION));
        }

        if (!Setup.getApplicationContext().getBean(DirectDebitService.class)
                .createDirectDebitActivationAttachmentIfNotExist(ddf)) {

            directDebitFileRepository.save(ddf);
        }
    }

    public void dataMigrationACC4493SecondStepRefresh(Integer limit) {
        int page = 0;
        Page<Long> ddfIds = directDebitSignatureRepository.getNotCorrectedCptSecondStepRefresh(PageRequest.of(page, 50));
        logger.log(Level.INFO, "cpts size {0}", ddfIds.getSize());

        int i = 0;
        while (ddfIds.hasContent() && i < limit) {
            i++;
            Long lastDdfId = Iterables.getLast(ddfIds);

            try {
                logger.log(Level.INFO, "create background task lastDdfId id {0}", lastDdfId);

                backgroundTaskService.create(new BackgroundTask.builder(
                    "dataMigrationACC4493SingleProcessSSRefreshWithEmail_" + lastDdfId,
                    "accounting",
                    "directDebitSignatureService",
                    "dataMigrationACC4493SingleProcessSSRefreshWithEmail")
                    .withRelatedEntity( "ContractPaymentTerm", null)
                    .withParameters( new Class<?>[] {List.class},
                        new Object[] { ddfIds.stream().map(x -> x.toString()).collect(Collectors.toList()) } )
                    .withQueue(BackgroundTaskQueues.SequentialQueue)
                    .build());
            } catch (Exception e) {
                e.printStackTrace();
            }

            ddfIds = directDebitSignatureRepository.getNotCorrectedCptSecondStepRefresh(PageRequest.of(++page, 50));
        }

        logger.log(Level.INFO, "end create background tasks");
    }

    public void dataMigrationACC4493SingleProcessSSRefreshWithEmail(List<String> ddfIds) {
        logger.log(Level.INFO, "ddfIds ids {0}", String.join(",", ddfIds));

        dataMigrationACC4493SingleProcessSSRefresh(ddfIds);
    }

    @Transactional
    public void dataMigrationACC4493SingleProcessSSRefresh(List<String> ddfIds) {
        logger.log(Level.INFO, "cptIds ids {0}", String.join(",", ddfIds));

        List<DDSignatureTempDto> ddSignatureTempList =
            directDebitSignatureRepository.getNotCorrectedCPTSignAttachmentsRefresh(ddfIds);
        logger.log(Level.INFO, "ddfList size: {0}", ddSignatureTempList.size());

        Map<Long, DirectDebitSignature> newSigns = new HashMap<>();

        ddSignatureTempList.forEach(tempSign -> {
            try {
                logger.log(Level.INFO, "DirectDebitFileId: {0}; eid: {1}; status: {2}", new Object[]{tempSign.getDirectDebitFileId(),
                    tempSign.getEid(), tempSign.getDirectDebitFileStatus()});
                ContractPaymentTerm contractPaymentTerm = contractPaymentTermRepository.findOne(tempSign.getNewAttachmentId());

                if (!newSigns.containsKey(tempSign.getNewAttachmentId())) {
                    DirectDebitSignature directDebitSignature = new DirectDebitSignature();
                    directDebitSignature.addAttachment(attachementRepository.findOne(tempSign.getNewAttachmentId()));
                    directDebitSignature.setContractPaymentTerm(contractPaymentTerm);
                    directDebitSignature.setEid(tempSign.getEid());
                    switch (tempSign.getDirectDebitFileStatus()) {
                        case REJECTED:
                            directDebitSignature.setSignatureStatus(DirectDebitSignatureStatus.REJECTED);
                            break;
                        case APPROVED:
                            directDebitSignature.setSignatureStatus(DirectDebitSignatureStatus.APPROVED);
                            break;
                        case SENT:
                            directDebitSignature.setSignatureStatus(DirectDebitSignatureStatus.UNDER_PROCESS);
                            break;
                        case NOT_COMPLETED:
                        case NOT_SENT:
                            directDebitSignature.setSignatureStatus(DirectDebitSignatureStatus.UNUSED);
                            break;
                    }
                    directDebitSignature.setUsedNumber(1);
                    directDebitSignature = directDebitSignatureRepository.save(directDebitSignature);
                    newSigns.put(tempSign.getNewAttachmentId(), directDebitSignature);
                }

                DirectDebitFile directDebitFile = directDebitFileRepository.findOne(tempSign.getDirectDebitFileId());
                directDebitFile.setDirectDebitSignature(newSigns.get(tempSign.getNewAttachmentId()));
                directDebitFileRepository.save(directDebitFile);
            } catch (Exception e) {
                e.printStackTrace();
            }

        });
        logger.log(Level.SEVERE, "dataMigrationACC3989ProcessRefresh end");
    }

    public boolean newMonthlyDdSigned(
            ContractPaymentTerm cpt,
            DirectDebit directDebit,
            List<DirectDebitSignature> signatures) {

        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());

        boolean newSignatures =  signatures != null && signatures.stream()
                .anyMatch(s -> s.isNewSignature());
        PicklistItem type = directDebit.getPaymentType();
        logger.info("ACC-5096 newSignatures: " + newSignatures + "; type: " + type);

        if (!newSignatures ||
                (type != null && !type.getCode().equals("monthly_payment"))) {

            logger.log(Level.INFO, "invalid case -> exiting");
            return false;
        }

        // ACC-5156
        Setup.getApplicationContext().getBean(FlowProcessorService.class)
                .retractContractTermination(cpt, null);

        return true;
    }

    // ACC-5156
    public List<DirectDebitSignature> getRejectedSignatureApprovedOnce(ContractPaymentTerm cpt) {
        logger.log(Level.SEVERE, "cpt id " + cpt.getId());
        logger.log(Level.SEVERE, "eid " + cpt.getEid());

        List<DirectDebitSignature> rejectedSignatures = directDebitSignatureRepository
                .findRejectedSignaturesByClientAndApprovedOnce(
                        cpt.getContract().getClient(), cpt.getEid());

        logger.log(Level.INFO, "rejected Signatures size: " + rejectedSignatures.size());
        return rejectedSignatures;
    }

    public void dataMigrationACC5196CanBeUsedManually() {
        int page = 0;
        Page<BigInteger> signsIds = directDebitSignatureRepository.findByDDFApproved(PageRequest.of(page, 500));
        logger.log(Level.INFO, "start");

        while (signsIds.hasContent()) {
            try {
                logger.log(Level.INFO, "lastSignId: {0}", Iterables.getLast(signsIds));

                backgroundTaskService.create(new BackgroundTask.builder(
                    "dataMigrationACC5196CanBeUsedManuallyProcess" + Iterables.getLast(signsIds),
                    "accounting",
                    "directDebitSignatureService",
                    "dataMigrationACC5196CanBeUsedManuallyProcess")
                    .withRelatedEntity( "DirectDebitSignature", null)
                    .withParameters( new Class<?>[] {List.class},
                        new Object[] { signsIds.stream().map(x -> String.valueOf(x)).collect(Collectors.toList()) } )
                    .withQueue(BackgroundTaskQueues.SequentialQueue)
                    .build());
            } catch (Exception e) {
                e.printStackTrace();
            }
            signsIds = directDebitSignatureRepository.findByDDFApproved(PageRequest.of(++page,500));
        }
        logger.log(Level.INFO, "end");
    }

    @Transactional
    public void dataMigrationACC5196CanBeUsedManuallyProcess(List<String> signsIds) {
        logger.log(Level.INFO, "signIds ids {0}", String.join(",", signsIds));
        List<DirectDebitSignature> signs = directDebitSignatureRepository.findAll(signsIds.stream()
                .map(s -> Long.parseLong(s)).collect(Collectors.toList()));
        signs.forEach(d -> {
            logger.log(Level.INFO, "dds id: {0}", d.getId());
            d.setApprovedOnce(true);
        });
        directDebitSignatureRepository.save(signs);
    }

    @Transactional
    public void createNewDdcTodo(ContractPaymentTerm cpt, DdcTodoType ddcType, List<DirectDebit> dds) throws Exception {

        Long newDdcId = Setup.getApplicationContext()
                .getBean(InterModuleConnector.class)
                .call("sales",
                        "appsServiceDDApprovalTodoController",
                        "createConfirmationDDCTodo",
                        Long.class,
                        new Class[] { Long.class, String.class },
                        new Object[] { cpt.getContract().getId(), ddcType.toString() });
        logger.info("response DDC id: " + newDdcId);

        if (cpt.getDdcId() == null) {
            cpt.setDdcId(newDdcId);
            contractPaymentTermRepository.silentSave(cpt);
        }

        logger.info("newDdcId: " + newDdcId);
        for (DirectDebit dd : dds) {
            logger.info("link new DdcId with dd id: " + dd.getId());
            dd.setDdcId(newDdcId);
            directDebitRepository.silentSave(dd);
        }
    }

    public WordImage getSignatureImage(byte[] byteArray) {
        WordImage image = null;
        InputStream input1 = null;
        InputStream input2 = null;

        try {
            input1 = new ByteArrayInputStream(byteArray);
            BufferedImage bufImg = ImageIO.read(input1);
            int maxWidth = 158, maxHeight = 45;
            if (bufImg.getWidth() == bufImg.getHeight()) { // for old signatures with square dimensions
                maxWidth = 80;
                maxHeight = 80;
            }
            double imgWidth = bufImg.getWidth(), imgHeight = bufImg.getHeight();

            try {
                if (imgHeight > maxHeight || imgWidth > maxWidth) {
                    bufImg = cropSignature(bufImg);
                }
            }catch(Exception e) {
                logger.log(Level.SEVERE, "Error while cropping signature", e);
            }

            imgWidth = bufImg.getWidth();
            imgHeight = bufImg.getHeight();

            // check image height > max height
            if (imgHeight > maxHeight) {
                double ratio = maxHeight / imgHeight;
                imgHeight = Math.round(imgHeight * ratio);
                imgWidth = Math.round(imgWidth * ratio);
            }
            // check image width > max width
            if (imgWidth > maxWidth) {
                double ratio = maxWidth / imgWidth;
                imgHeight = Math.round(imgHeight * ratio);
                imgWidth = Math.round(imgWidth * ratio);
            }
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(bufImg, "png", os);
            input2 = new ByteArrayInputStream(os.toByteArray());
            image = new WordImage(input2, ConvertUtil.pixelToPoint(imgWidth), ConvertUtil.pixelToPoint(imgHeight));
        } catch (IOException ex) {
            Logger.getLogger(DirectDebitSignatureService.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            StreamsUtil.closeStream(input1);
        }
        return image;
    }

    public BufferedImage cropSignature(BufferedImage image) {
        int minY = 0, maxY = 0, minX = Integer.MAX_VALUE, maxX = 0;
        boolean isBlank, minYIsDefined = false;
        Raster raster = image.getRaster();

        for (int y = 0; y < image.getHeight(); y++) {
            isBlank = true;

            for (int x = 0; x < image.getWidth(); x++) {
                if (raster.getSample(x, y, 2) != 255) { // 255 represents the background of image "white"
                    isBlank = false;

                    if (x < minX) minX = x;
                    if (x > maxX) maxX = x;
                }
            }

            if (!isBlank) {
                if (!minYIsDefined) {
                    minY = y;
                    minYIsDefined = true;
                } else {
                    if (y > maxY) maxY = y;
                }
            }
        }

        return image.getSubimage(minX, minY, maxX - minX + 1, maxY - minY + 1);
    }

    public boolean hasSignature(ContractPaymentTerm cpt) {

        return hasSignature(cpt, cpt.getEid());
    }

    public boolean hasSignature(ContractPaymentTerm cpt, String eid) {

        return hasSignature(cpt, false, eid);
    }

    public boolean hasSignature(ContractPaymentTerm cpt, boolean approved, String eid) {

        return directDebitSignatureRepository
                .existsSignaturesByClientWithDdf(cpt.getContract().getClient(), eid,
                        approved ? DirectDebitSignatureStatus.APPROVED : null)
                || directDebitSignatureRepository
                .existSignaturesByClientWithoutDdf(cpt.getContract().getClient(), eid,
                        approved ? DirectDebitSignatureStatus.APPROVED : null);
    }

    // ACC-7478 Extract Signature by OCR
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Attachment extractSignature(InputStream signatureFile, String signatureName, String tag) {

        if(signatureFile == null) return null;

        InputStream signatureStream = Fingerprint.extract(signatureFile, null, null, null, null);

        if (signatureStream == null) return null;

        Attachment signature = null;
        try {
            signature = Storage.storeTemporary(signatureName, signatureStream, tag, Boolean.FALSE, true);
            logger.info("signature id: " + signature.getId() +
                    "; signature uuid: " + signature.getUuid());
            signatureStream.close();
        }catch (Exception ex){
            ex.printStackTrace();
        } finally {
            StreamsUtil.closeStream(signatureStream);
        }

        return signature;
    }
}
package com.magnamedia.service;

import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.core.helper.GoogleAnalyticsService;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.repository.TagRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowProgressPeriod;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.entity.workflow.VoiceResolverToDo;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.magnamedia.module.AccountingModule.*;

/**
 *
 * <AUTHOR> Hachem
 */

@Service
public class FlowProcessorService {
    private static final Logger logger = Logger.getLogger(FlowProcessorService.class.getName());

    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private DDMessagingService ddMessagingService;
    @Autowired
    private GoogleAnalyticsService googleAnalyticsService;
    @Autowired
    private AfterCashFlowService afterCashFlowService;
    @Autowired
    private PushNotificationHelper pushNotificationHelper;
    @Autowired
    private DisablePushNotificationRepository disablePushNotificationRepository;
    @Autowired
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;
    @Autowired
    private OneMonthAgreementFlowService oneMonthAgreementFlowService;
    @Autowired
    private FlowEventConfigRepository flowEventConfigRepository;
    @Autowired
    private UnpaidOnlineCreditCardPaymentService unpaidOnlineCreditCardPaymentService;
    @Autowired
    private FlowProgressPeriodRepository flowProgressPeriodRepository;
    @Autowired
    private IncompleteDirectDebitService incompleteDirectDebitService;
    @Autowired
    private FlowSubEventConfigRepository flowSubEventConfigRepository;

    @Transactional
    public void processFlowSubEventConfig(FlowProcessorEntity entity) {

        logger.log(Level.INFO, "entity id: {0}; subEvent id: {1}", new Object[]{entity.getId(), entity.getCurrentSubEvent().getId()});

        if (!entity.getFlowEventConfig().hasTag("defaultDDSendTime"))
            throw new RuntimeException(entity.getFlowEventConfig().getName().getMessagingType().getLabel() +
                    " Flow does not have a default DD send time tag");

        if(validateFlowStopping(entity)) {
            logger.info("Flow Stopped");
            return;
        }

        Map<String, Object> m = checkNextStep(entity);
        if (!(boolean) m.get("proceed")) {
            logger.info("Period not met yet");
            return;
        }

        resolveDDMessaging(entity, m);

        applyAfterProcessFlowSubEventConfig(entity, m);
    }

    @Transactional
    public FlowProcessorEntity createFlowProcessor(
            FlowEventConfig.FlowEventName flowEventName,
            FlowSubEventConfig.FlowSubEventName flowSubEventName,
            ContractPaymentTerm cpt,
            Map<String, Object> map) {

        FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(flowEventName);
        FlowSubEventConfig flowSubEventConfig = flowSubEventConfigRepository
                .findByNameAndFlowEventConfig(flowSubEventName, flowEventConfig);

        return createFlowProcessor(flowEventConfig, flowSubEventConfig, cpt, map);
    }

    @Transactional
    public FlowProcessorEntity createFlowProcessor(
            FlowEventConfig flowEventConfig,
            FlowSubEventConfig flowSubEventConfig,
            ContractPaymentTerm contractPaymentTerm,
            Map<String, Object> map) {


        if (contractPaymentTerm == null) {
            logger.log(Level.INFO, "Can't find contractPaymentTerm");
            return null;
        }

        if (getRunningFlow(flowEventConfig.getName(), flowSubEventConfig.getName(),
                contractPaymentTerm.getContract(), (ContractPaymentConfirmationToDo) map.get("todo")) != null) {
            logger.log(Level.INFO, "contractPaymentTerm have a running flow");
            return null;
        }

        logger.log(Level.INFO, "FlowEventConfig id: {0}; flowSubEventConfig id: {1}; contractPaymentTerm id: {2}",
                new Object[] { flowEventConfig.getId(), flowSubEventConfig.getId(), contractPaymentTerm.getId()});

        FlowProcessorEntity entity = new FlowProcessorEntity();
        entity.setContractPaymentTerm(contractPaymentTerm);
        entity.setCurrentFlowRun(1);
        entity.setCurrentSubEvent(flowSubEventConfig);
        entity.setFlowEventConfig(flowEventConfig);
        entity.setReminders((int) map.get("reminders"));
        entity.setTrials((int) map.get("trials"));

        if (map.containsKey("directDebit")) {
            entity.setDirectDebit((DirectDebit) map.get("directDebit"));
        }

        entity.setLastExecutionDate(
            new DateTime((Date) map.get("lastExecutionDate"))
            .withHourOfDay(10)
            .withMinuteOfHour(0)
            .withSecondOfMinute(0).toDate());

        if (map.get("additionalInfo") != null) {
            entity.setAdditionalInfo((Map<String, Object>) map.get("additionalInfo"));
        }

        if (map.get("todo") != null) {
            entity.setContractPaymentConfirmationToDo((ContractPaymentConfirmationToDo) map.get("todo"));
        }

        return flowProcessorEntityRepository.save(entity);
    }

    @Transactional
    public boolean incrementTrialOnly(FlowProcessorEntity entity) {
        logger.log(Level.INFO, "flowProcessorEntity id: {0}; subEvent id: {1}",
                new Object[] { entity.getId(), entity.getCurrentSubEvent().getId()});

        entity.setIncrementedTrials(entity.getTrials() + 1);
        entity.setIncrementedReminders(1);
        return nextStepPeriodPassed(entity);
    }

    @Transactional
    public boolean incrementReminderOnly(FlowProcessorEntity entity) {
        logger.log(Level.INFO, "flowProcessorEntity id: {0}; subEvent id: {1}",
                new Object[] { entity.getId(), entity.getCurrentSubEvent().getId()});

        entity.setIncrementedTrials(entity.getTrials());
        entity.setIncrementedReminders(entity.getReminders() + 1);
        return nextStepPeriodPassed(entity);
    }

    @Transactional
    public Map<String, Object> checkNextStep(FlowProcessorEntity entity) {
        boolean incrementReminders = entity.getCurrentSubEvent().isInfiniteReminder() ||
                (entity.getReminders() < entity.getCurrentSubEvent().getMaxReminders() && entity.getTrials() != 0);
        boolean proceed = incrementReminders ?
                incrementReminderOnly(entity) : incrementTrialOnly(entity);
        Map<String, Object> m = new HashMap<>();
        m.put("proceed", proceed);

        if(!proceed) return m;

        if (validateSkipFlow(entity)) {
            m.put("proceed", false);
            return m;
        }

        if(entity.getCurrentSubEvent().isInfiniteReminder()) return m;


        if (incrementReminders && entity.getIncrementedReminders() >= entity.getCurrentSubEvent().getMaxReminders() &&
                applyCheckMaxTrialsAndReminders(entity)) {
            logger.log(Level.INFO, "Reminder greater than max reminder");

            if(entity.getCurrentSubEvent().isTerminateContractOnMaxReminders()) {
                logger.log(Level.INFO, "terminating contract");
                m.putAll(handleScheduledContractForTermination(entity));
                entity.setStopped(true);
            }


        } else if (!incrementReminders && entity.getIncrementedTrials() >= entity.getCurrentSubEvent().getMaxTrials() &&
                applyCheckMaxTrialsAndReminders(entity)) {
            logger.log(Level.INFO, "Trials greater than max trials");

            if (entity.isPauseEventTermination()) {
                logger.log(Level.INFO, "pausing termination");
                entity.setPauseEventTermination(false);
                flowProcessorEntityRepository.save(entity);
                m.put("proceed", false);
            } else if (entity.getCurrentSubEvent().isTerminateContractOnMaxTrials()) {
                m.putAll(handleScheduledContractForTermination(entity));
            }

            entity.setStopped(true);
        }

        if(entity.isStopped()) {
            closeToDosUponCompletion(entity);
            // ACC-6624 disableOldNotification(entity); // ACC-4466
        }

        return m;
    }

    private boolean nextStepPeriodPassed(FlowProcessorEntity entity) {
        boolean infiniteReminder = entity.getCurrentSubEvent().isInfiniteReminder() &&
                entity.getIncrementedReminders() > entity.getCurrentSubEvent().getMaxReminders();
        int reminder = infiniteReminder ? entity.getCurrentSubEvent().getMaxReminders() : entity.getIncrementedReminders();

        if (entity.getLastExecutionDate() != null) {
            FlowProgressPeriod progressPeriod = null;

            if (!infiniteReminder) {
                int trial = entity.getIncrementedTrials();
                progressPeriod = entity.getCurrentSubEvent().getProgressPeriods().stream()
                        .filter(p -> p.getTrials() == trial && p.getReminders() == reminder)
                        .findFirst().orElse(null);
            }

            int periodInHours = progressPeriod != null ?
                    progressPeriod.getPeriodInHours() : entity.getCurrentSubEvent().getDefaultPeriod();

            periodInHours = updatePeriodInHours(entity, periodInHours);

            if (new DateTime(entity.getLastExecutionDate()).plusHours(periodInHours)
                    .isAfter(new DateTime().plusMinutes(30))) {

                logger.log(Level.INFO, "trials: {0}; reminder: {1}; periodInHours: {2}, progressPeriod id: {3}",
                        new Object[]{entity.getIncrementedTrials(), entity.getIncrementedReminders(),
                                new DateTime(entity.getLastExecutionDate()).plusHours(periodInHours),
                                progressPeriod == null ? null : progressPeriod.getId()});
                return false;
            }
        }
        return true;
    }

    private void setExecutionDateValidation(FlowProcessorEntity entity) {

        if (entity.getIncrementedReminders() != null) {
            entity.setReminders(entity.getIncrementedReminders());
        }

        if (entity.getIncrementedTrials() != null) {
            entity.setTrials(entity.getIncrementedTrials());
        }

        entity.setLastExecutionDate(new DateTime().toDate());
        entity.setAdditionalValue("lastTrialSendDate", new DateTime().toString("yyyy-MM-dd HH:mm:ss"));
    }

    private boolean validateDdMessaging(FlowProcessorEntity entity){
        Contract contract = entity.getContractPaymentTerm().getContract();

        if (entity.getFlowEventConfig().isStopMessagesOnContractCancellation() &&
                (contract.getStatus().equals(ContractStatus.CANCELLED) ||
                        contract.getStatus().equals(ContractStatus.EXPIRED))) {

            logger.log(Level.INFO, "Contract id {0} status is cancelled", contract.getId());
            return false;
        }

        /*switch (entity.getFlowEventConfig().getName()) {
            case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
              return afterCashFlowService.applyDDMessaging(entity);
        }*/

        return true;
    }

    private boolean validateSkipFlow(FlowProcessorEntity entity) {

        switch (entity.getFlowEventConfig().getName()) {
            case CLIENTS_PAYING_VIA_Credit_Card:
                if (clientPayingViaCreditCardService.skipReminderFlow(entity, entity.getContractPaymentTerm())) {
                    if (entity.getIncrementedTrials() == entity.getCurrentSubEvent().getMaxTrials()) {
                        entity.setStopped(true);
                        flowProcessorEntityRepository.save(entity);
                    }
                    return true;
                }

                if (ClientPayingViaCreditCardService.recurringFailureFlowsWithExpiredCard
                            .contains(entity.getCurrentSubEvent().getName()) &&
                      clientPayingViaCreditCardService.skipExceedsCardLimitAndInsufficientFundsFlow(entity)) {
                    logger.info("entity sub flow: " + entity.getCurrentSubEvent().getName() + " -> existing");
                    return true;
                }

                break;
        }

        return false;
    }

    private boolean applyCheckMaxTrialsAndReminders(FlowProcessorEntity entity) {

//        switch (entity.getFlowEventConfig().getName()) {
//            case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
//                return afterCashFlowService.applyCheckMaxTrialsAndReminders(entity);
//        }
        return true;
    }


    private int updatePeriodInHours(FlowProcessorEntity entity, int periodInHours) {
//        switch (entity.getFlowEventConfig().getName()) {
//            case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
//                return afterCashFlowService.updatePeriodInHours(entity.getIncrementedTrials(), periodInHours);
//        }

        return periodInHours;
    }

    @Transactional
    public boolean validateFlowStopping(FlowProcessorEntity entity) {

        if ((entity.getCurrentSubEvent().getFlowEventConfig().isStopMessagesOnContractCancellation() &&
                entity.getCurrentSubEvent().getFlowEventConfig().isStopTodosOnContractCancellation()) &&
                (entity.getContract().getStatus().equals(ContractStatus.CANCELLED) ||
                        entity.getContract().getStatus().equals(ContractStatus.EXPIRED)) &&
                (entity.getFlowEventConfig().isStopFlowsCreatedAfterTermination() ||
                        (entity.getContract().getDateOfTermination() != null &&
                                entity.getCreationDate().getTime() <= entity.getContract().getDateOfTermination().getTime()))) {

            logger.log(Level.INFO, "entity id: {0}; Flow stopping because contract is canceled and no messaging configured", new Object[]{entity.getId()});
            entity.setStopped(true);
            entity.setStoppedDueContractTerminated(true);
            flowProcessorEntityRepository.save(entity);

            return true;
        }

        switch (entity.getCurrentSubEvent().getRequiredAction()) {
            case CLIENT_PROVIDES_SIGNATURE:
                if (clientProvidesSignatureAndBankInfo(entity.getContract())) {
                    logger.log(Level.INFO, "entity id: {0}; Flow stopping because client provides signatures", entity.getId());

                    switch (entity.getFlowEventConfig().getName()) {
                        case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
                            afterCashFlowService.disableThankYouMessage(entity.getContract()); // ACC-5214
                            break;
//                        case ONE_MONTH_AGREEMENT:
//                            oneMonthAgreementFlowService.flowStoppedResetFlag(entity.getContract());
//                            break;
                    }

                    entity.setCompleted(true);
                    flowProcessorEntityRepository.save(entity);
                    return true;
                }
                break;

            case ONLINE_CREDIT_CARD_PAYMENT_RECEIVED:
                if (entity.getContractPaymentConfirmationToDo() == null) return false;

                if (entity.getContractPaymentConfirmationToDo().getContractPaymentList()
                        .stream().allMatch(w -> {
                            ContractPayment p = w.getContractPayment();
                            LocalDate currentPaymentDate = DateUtil.getStartDayPaymentDateOfMonth(entity.getContract(), p.getDate());
                            return paymentRepository.paymentReceived(
                                    entity.getContract(), p.getPaymentType(),
                                    currentPaymentDate.toDate(),
                                    currentPaymentDate.plusMonths(1).toDate());
                        })) {

                    logger.log(Level.INFO, "entity id: {0}; Flow stopping because the payment status changed", entity.getId());
                    entity.setCompleted(true);
                    flowProcessorEntityRepository.save(entity);
                    return true;
                }
                break;
            case CLIENT_PROVIDES_MISSING_BANK_INFO:
                Map<String, Object> m = incompleteDirectDebitService.validateFlowStopping(entity.getContract(), entity);
                if ((boolean) m.get("stopped")) {
                    entity.setStopped(true);
                    flowProcessorEntityRepository.save(entity);
                    return true;
                }

                if ((boolean) m.get("completed")) {
                    entity.setCompleted(true);
                    flowProcessorEntityRepository.save(entity);
                    return true;
                }
                break;
        }

        return false;
    }

    public boolean clientProvidesSignatureAndBankInfo(Contract contract) {

        return clientProvidesSignatureAndBankInfo(contract, null, false);
    }

    public boolean clientProvidesSignatureAndBankInfo(Contract contract, Date date) {

        return clientProvidesSignatureAndBankInfo(contract, date, false);
    }

    public boolean clientProvidesSignatureAndBankInfo(Contract contract, Date date, boolean filterByNewDdf) {
        return clientProvidesSignatureAndBankInfo(contract, date, filterByNewDdf, true);
    }

    public boolean clientProvidesSignatureAndBankInfo(Contract contract, Date date, boolean filterByNewDdf, boolean ignoredStatusesDD) {
        List<DirectDebitStatus> ignoredStatuses = Arrays.asList(
                DirectDebitStatus.CANCELED, DirectDebitStatus.EXPIRED,
                DirectDebitStatus.PENDING_FOR_CANCELLATION);

        SelectQuery<DirectDebitFile> query = new SelectQuery<>(DirectDebitFile.class);
        query.filterBy("directDebit.contractPaymentTerm.contract", "=", contract);
        if (ignoredStatusesDD) {
            query.filterBy("directDebit.status", "not in", ignoredStatuses);
            query.filterBy("directDebit.MStatus", "not in", ignoredStatuses);
        }
        query.filterBy("directDebitSignature", "is not null", null);

        if(date != null) {
            SelectFilter dateFilter = new SelectFilter("directDebitSignature.creationDate", ">=", date);
            // ACC-6804 Fix #8
            if (filterByNewDdf) {
                dateFilter.or("creationDate", ">=", date);
            }
            query.filterBy(dateFilter);
        }

        return query.execute().stream()
                .anyMatch(df -> ((df.getEid() != null || df.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID) != null) &&
                        (df.getIbanNumber() != null || df.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN) != null)) ||
                        df.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR) != null);
    }

    private void closeToDosUponCompletion(FlowProcessorEntity entity) {
        if (entity.getFlowEventConfig().isCloseToDosUponCompletion()) {
            logger.log(Level.INFO, "entity id: {0}; Flow stopping and closeToDosUponCompletion is true", new Object[]{entity.getId()});

            switch (entity.getFlowEventConfig().getName()) {
                case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
                    closeResolverToDos(entity.getContract(), VoiceResolverToDoReason.INCOMPLETE_DD);
                    break;
                case ONLINE_CREDIT_CARD_PAYMENT_REMINDERS:
                    closeResolverToDos(entity.getContract(), VoiceResolverToDoReason.PAYMENT_REMINDER);
                    break;
            }
        }
    }

    private Map<String, Object> handleScheduledContractForTermination(FlowProcessorEntity entity) {
        Map<String, Object> m = new HashMap<>();
        Date scheduledDateOfTermination;

        boolean isSetSFT = true;
        switch (entity.getFlowEventConfig().getName()) {
            case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
                isSetSFT = afterCashFlowService.handleScheduledContractForTermination(entity, m);
                break;
            case CLIENTS_PAYING_VIA_Credit_Card:
                isSetSFT = clientPayingViaCreditCardService.handleScheduledContractForTermination(entity, m);
                break;
            case EXTENSION_FLOW:
                m.put("flowName", "Extension_Flow");
                break;
        }

        if (isSetSFT) {
            entity.setCausedTermination(true);
            scheduledDateOfTermination = scheduledContractForTermination(entity.getContract(), entity.getFlowEventConfig(), m);
            m.put("scheduledDateOfTermination", scheduledDateOfTermination);
        }

        return m;
    }

    private Date scheduledContractForTermination(
            Contract contract,
            FlowEventConfig event,
            Map<String, Object> m){

        if(contract.getIsScheduledForTermination()) return contract.getScheduledDateOfTermination();

        logger.info("reason " + event.getCancellationReason().getCode());

        contract.setScheduledDateOfTermination(
                Setup.getApplicationContext()
                        .getBean(ContractService.class)
                        .setContractForTermination(contract, event.getCancellationReason().getCode(), m));

        // ACC-6085 When to disable CTA?: Once contract is scheduled for termination
        if (event.getName().equals(FlowEventConfig.FlowEventName.ONE_MONTH_AGREEMENT)) {
            List<PushNotification> clientNotifications = disablePushNotificationRepository
                    .findActiveNotificationsByDDMessagingType(
                            contract.getClient().getId().toString(), event.getName().getMessagingType(), contract.getId());
            pushNotificationHelper.stopDisplaying(clientNotifications);
        }

        return contract.getScheduledDateOfTermination();
    }

    private void closeResolverToDos(Contract contract, VoiceResolverToDoReason resolverToDoReason) {
        logger.log(Level.INFO, "resolverToDoReason {0}", resolverToDoReason);
        logger.log(Level.INFO, "contract id: {0}", contract.getId());

        SelectQuery<VoiceResolverToDo> query = new SelectQuery(VoiceResolverToDo.class);
        query.filterBy("contract", "=", contract);
        query.filterBy("reason", "=", resolverToDoReason);
        query.filterBy("completed", "=", false);
        query.filterBy("stopped", "=", false);
        InterModuleConnector moduleConnector = Setup.getApplicationContext().getBean(InterModuleConnector.class);

        query.execute().forEach(t -> moduleConnector.get("clientmgmt/voiceResolverToDo/closetodo/" + t.getId(), Map.class));
    }

    //ACC-4466
    public void disableOldNotification(FlowProcessorEntity entity) {
        List<PushNotification> clientNotifications = disablePushNotificationRepository
            .findActiveNotificationsByDDMessagingType(entity.getContract().getClient().getId().toString(),
                entity.getFlowEventConfig().getName().getMessagingType(), entity.getContract().getId());

        clientNotifications.forEach(n ->
            pushNotificationHelper.stopDisplayingNotification(n));
    }

    public ContractPaymentConfirmationToDo additionalFilter(
            SelectQuery<DDMessaging> query,
            FlowProcessorEntity entity,
            FlowSubEventConfig subEvent) {

        switch (entity.getFlowEventConfig().getName()) {
            case ONLINE_CREDIT_CARD_PAYMENT_REMINDERS:
                unpaidOnlineCreditCardPaymentService.messagingFilter(query, entity);
                break;
            case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
                return afterCashFlowService.messagingFilter(query, entity, subEvent);
            case CLIENTS_PAYING_VIA_Credit_Card:
                return clientPayingViaCreditCardService.messagingFilter(query, entity);
        }

        return null;
    }

    // ACC-5190
    private void sendAnalytics(
            FlowProcessorEntity entity,
            DDMessaging ddMessaging) {

        switch (entity.getFlowEventConfig().getName()) {
            case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
                if(ddMessaging.getSendPayTabMessage()) {
                    googleAnalyticsService.trackEvent(
                            GOOGLE_ANALYTICS_ACTION,
                            GOOGLE_ANALYTICS_CATEGORY,
                            GOOGLE_ANALYTICS_SENDING_IPAM_PAYMENT_REMINDER,
                            entity.getIncrementedTrials());
                }
                break;
        }
    }

    public boolean nextMonthPaymentReceived(Contract contract) {

        LocalDate startDate = contract.isOneMonthAgreement() ?
                oneMonthAgreementFlowService.getCurrentPaymentDate(contract).plusMonths(1).toLocalDate() :
                new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue();

        return paymentRepository.existsByContractAndStatusAndTypeOfPayment_CodeAndDateOfPaymentBetween(
                contract, PaymentStatus.RECEIVED, "monthly_payment",
                startDate.toDate(),
                startDate.plusMonths(1).minusDays(1).toDate());
    }

    public boolean currentMonthPaymentReceived(Contract contract) {

        LocalDate startDate = contract.isOneMonthAgreement() ?
                oneMonthAgreementFlowService.getCurrentPaymentDate(contract).toLocalDate() :
                new LocalDate().dayOfMonth().withMinimumValue();

        return paymentRepository.existsByContractAndStatusAndTypeOfPayment_CodeAndDateOfPaymentBetween(
                contract, PaymentStatus.RECEIVED, "monthly_payment",
                startDate.toDate(),
                startDate.plusMonths(1).minusDays(1).toDate());
    }

    public boolean nextMonthDdConfirmed(Contract contract) {
        return directDebitRepository.existsByContractAndStatusInAndDateBetween(
                contract, Collections.singletonList(DirectDebitStatus.CONFIRMED),
                new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate());
    }

    // ACC-5156
    public void retractContractTermination(ContractPaymentTerm cpt, ContractPaymentConfirmationToDo toDo) {
        Contract contract = cpt.getContract();

        if (!contract.getStatus().equals(ContractStatus.ACTIVE) ||
                contract.getScheduledDateOfTermination() == null ||
                contract.getReasonOfTerminationList() == null ||
                !Setup.getParameter(Setup.getCurrentModule(),
                                PARAMETER_CONTRACT_RETRACT_AFTER_PAYMENT_RECEIVED_REASONS)
                        .contains(contract.getReasonOfTerminationList().getCode())) return;

        logger.log(Level.INFO, "cpt id : {0}; paymentReceived: {1}", new Object[]{cpt.getId(), toDo != null});

        if (toDo == null)  {
            if (contract.isOneMonthAgreement()) {
                logger.info("One month agreement flow: client provided signatures -> reset flag");
                oneMonthAgreementFlowService.flowStoppedResetFlag(contract);

            } else if (flowProcessorEntityRepository.isContractScheduledForTerminationByFlowEventName(
                    contract, FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED)) {

                logger.info("IPAM flow: client provided signatures -> keep flow stopped");
            }
        } else {
            // ACC-6162 ACC-6020
            if (clientPayingViaCreditCardService.reactivateInitialFlowViaConfirmationTodo(cpt, toDo)) {
                logger.info("client paid and has initial flow (Paying via Credit Card) was stopped");
            } else {
                // ACC-5156 ACC-5553
                if (toDo.getContractPaymentList().stream()
                        .noneMatch(p -> {
                            DateTime d = new DateTime(p.getPaymentDate());
                            return p.getPaymentType().getCode().equals("monthly_payment") &&
                                    d.plusHours(1).isAfter(new DateTime().withDayOfMonth(1).withTimeAtStartOfDay());
                        })) {

                    logger.info("Payment is dated before current month -> exiting");
                    return;
                }

                if (!contract.isPayingViaCreditCard() &&
                        !clientProvidesSignatureAndBankInfo(cpt.getContract())) { // ACC-5625

                    logger.info("client paid and not signed -> reactivate IPAM");
                    afterCashFlowService.reactivateFlow(cpt);
                }
            }
        }

        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .retractContractTermination(contract);
    }

    private void resolveDDMessaging(FlowProcessorEntity entity, Map<String, Object> m) {
        logger.info("flow id: " + entity.getId() + "; sub-event id: " + entity.getCurrentSubEvent().getId());

        boolean infiniteReminder = entity.getCurrentSubEvent().isInfiniteReminder() && entity.getIncrementedReminders() > entity.getCurrentSubEvent().getMaxReminders();
        int reminder = infiniteReminder ? entity.getCurrentSubEvent().getMaxReminders() : entity.getIncrementedReminders();

        Date scheduledDateOfTermination = m.containsKey("scheduledDateOfTermination") ?
                (Date) m.get("scheduledDateOfTermination") :
                null;

        DirectDebitMessagingScheduleTermCategory scheduleTermCategory =
                scheduledDateOfTermination == null ?
                        DirectDebitMessagingScheduleTermCategory.None :
                        (new LocalDate().equals(new LocalDate(scheduledDateOfTermination)) ?
                                DirectDebitMessagingScheduleTermCategory.EToday :
                                DirectDebitMessagingScheduleTermCategory.GToday);

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("isActive", "=", true);
        query.filterBy("event", "=", entity.getCurrentSubEvent().getFlowEventConfig().getName().getMessagingType());
        query.filterBy("subType", "=", entity.getCurrentSubEvent().getName().getMessagingSubType());
        query.filterBy("trials", "=", String.valueOf(entity.getIncrementedTrials() == -1 ? 0 : entity.getIncrementedTrials()));
        query.filterBy("reminders", "=", String.valueOf(reminder));
        query.filterBy("contractProspectTypes", "like", "%" + entity.getContract().getContractProspectType().getCode() + "%");
        query.filterBy("deleted", "=", false);

        ContractPaymentConfirmationToDo t = additionalFilter(query, entity, entity.getCurrentSubEvent());

        query.sortBy("lastModificationDate", false, true);
        query.setLimit(1);

        if(t != null) {
            entity.setContractPaymentConfirmationToDo(t);
        }

        DDMessaging ddMessaging = DDMessagingService.getDdMessaging(
                query, entity.getContract(), scheduleTermCategory,
                entity.getFlowEventConfig().getName().isSendPayTabsWithTermination());
        LocalTime ddSendTime = LocalTime.parse(entity.getFlowEventConfig()
                .getTagValue("defaultDDSendTime").getValue());

        if (ddMessaging == null) {
            logger.log(Level.INFO, "dd message not found event: {0}; subType: {1}; trials: {2}; reminder: {3}; contractProspectTypes: {4}; scheduleTermCategory: {5}; infiniteReminder: {6}",
                    new Object[] { entity.getCurrentSubEvent().getFlowEventConfig().getName().getMessagingType(), entity.getCurrentSubEvent().getName().getMessagingSubType(),
                            entity.getIncrementedTrials(), entity.getIncrementedReminders(), entity.getContract().getContractProspectType().getCode(), scheduleTermCategory, infiniteReminder });

            if(new LocalTime().isBefore(ddSendTime)){
                if (t != null) flowProcessorEntityRepository.save(entity);
                return;
            }

            setExecutionDateValidation(entity);
            flowProcessorEntityRepository.save(entity);

            return;
        }

        logger.log(Level.INFO, "dd message id: {0}", ddMessaging.getId());

        // ACC-5045;
        if (ddMessaging.getSendTime() != null) ddSendTime = new LocalTime(ddMessaging.getSendTime());
        if(new LocalTime().isBefore(ddSendTime)) {
            if (t != null) flowProcessorEntityRepository.save(entity);
            return;
        }

        setExecutionDateValidation(entity);
        entity = flowProcessorEntityRepository.save(entity);

        if (!validateDdMessaging(entity)) {
            logger.log(Level.INFO, "validateDdMessaging failed: exiting");
            return;
        }

        if (!infiniteReminder) {
            /*if(ddMessaging.getCreateHumanSms()) {
                DDMessagingService.DdMessagingMethod type =
                        ddMessaging.getEvent().equals(DDMessagingType.BouncedPayment) ||
                                ddMessaging.getEvent().equals(DDMessagingType.OnlineCreditCardPaymentReminders) ||
                                ddMessaging.getEvent().equals(DDMessagingType.ClientPaidCashAndNoSignatureProvided) ?
                                DDMessagingService.DdMessagingMethod.EXPERT_TODO :
                                DDMessagingService.DdMessagingMethod.HUMAN_SMS;

                ddMessagingService.applyDdMessaging(ddMessaging, entity, type, scheduledDateOfTermination);
            }*/

            if(ddMessaging.isCreateClientToDo()) {
                ddMessagingService.applyDdMessaging(ddMessaging, entity, DDMessagingService.DdMessagingMethod.CLIENT_TODO, scheduledDateOfTermination);

            }
        }

        if (ddMessaging.getSendToClient() ||
                ddMessaging.getSendToSpouse() ||
                ddMessaging.getSendToMaid() ||
                ddMessaging.getSendToMaidWhenRetractCancellation()) {

            ddMessagingService.applyDdMessaging(
                    ddMessaging, entity, DDMessagingService.DdMessagingMethod.MESSAGE, scheduledDateOfTermination);
            sendAnalytics(entity, ddMessaging);
        }
    }

    private void applyAfterProcessFlowSubEventConfig(FlowProcessorEntity entity, Map<String, Object> m) {
        switch (entity.getFlowEventConfig().getName()) {
            case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
                afterCashFlowService.applyAfterProcessFlowSubEventConfig(entity, m);
                break;
            case CLIENTS_PAYING_VIA_Credit_Card:
                clientPayingViaCreditCardService.applyAfterProcessFlowSubEventConfig(entity, m);
                break;
        }
    }

    boolean existsRunningFlow(
            FlowEventConfig.FlowEventName eventName,
            List<FlowSubEventConfig.FlowSubEventName> subEventNames,
            ContractPaymentTerm cpt) {

        return flowProcessorEntityRepository.existsRunningFlow(
                cpt.getContract(), eventName, subEventNames);
    }

    public boolean existsRunningFlow(
            Contract contract,
            FlowEventConfig.FlowEventName eventName) {

        return existsRunningFlows(contract, Collections.singletonList(eventName));
    }

    public boolean existsRunningFlows(
            Contract contract,
            List<FlowEventConfig.FlowEventName> eventNames) {

        return flowProcessorEntityRepository.existsRunningFlow(contract, eventNames);
    }

    public boolean existsRunningFlow(
            Contract contract,
            FlowEventConfig.FlowEventName eventName,
            FlowSubEventConfig.FlowSubEventName subEventName) {

        return existsRunningFlow(contract, eventName, Collections.singletonList(subEventName));
    }

    public boolean existsRunningFlow(
            Contract contract,
            FlowEventConfig.FlowEventName eventName,
            List<FlowSubEventConfig.FlowSubEventName> subEventNames) {

        return flowProcessorEntityRepository.existsRunningFlow(contract, eventName, subEventNames);
    }


    FlowProcessorEntity getRunningFlow(
            FlowEventConfig.FlowEventName eventName,
            FlowSubEventConfig.FlowSubEventName subEventName,
            Contract contract,
            ContractPaymentConfirmationToDo toDo) {

        return getRunningFlow(eventName, Collections.singletonList(subEventName), contract, toDo);
    }

    public FlowProcessorEntity getRunningFlow(
            FlowEventConfig.FlowEventName eventName,
            List<FlowSubEventConfig.FlowSubEventName> subEventNames,
            Contract contract,
            ContractPaymentConfirmationToDo todo) {

        List<FlowProcessorEntity> flows =  flowProcessorEntityRepository
                .getRunningFlow(contract, eventName, subEventNames, todo);
        return flows.isEmpty() ? null : flows.get(0);
    }

    public FlowProcessorEntity getFirstRunningFlow(Contract c, FlowEventConfig.FlowEventName eventName) {
        return flowProcessorEntityRepository
                .findFirstByFlowEventConfig_NameAndContractPaymentTerm_ContractAndStoppedFalseAndCompletedFalseOrderByCreationDate(eventName, c);
    }

    public void createFlowProgressPeriods(
            List<Map<String, Integer>> values,
            FlowSubEventConfig flowSubEventConfig) {

        List<FlowProgressPeriod> flowProgressPeriods = flowProgressPeriodRepository
                .findByFlowSubEventConfig(flowSubEventConfig);
        flowProgressPeriodRepository.delete(flowProgressPeriods);

        values.forEach(map -> {
            FlowProgressPeriod flowProgressPeriod = new FlowProgressPeriod();
            flowProgressPeriod.setFlowSubEventConfig(flowSubEventConfig);
            flowProgressPeriod.setReminders(map.get("reminder"));
            flowProgressPeriod.setTrials(map.get("trial"));
            flowProgressPeriod.setPeriodInHours(map.get("period"));
            flowProgressPeriodRepository.save(flowProgressPeriod);
        });
    }

    public FlowEventConfig createOrFetchFlowEventConfig(
            FlowEventConfig.FlowEventName flowEventName,
            Map<String, Object> map) {

        FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(flowEventName);
        if (flowEventConfig != null) return flowEventConfig;

        flowEventConfig = new FlowEventConfig();
        flowEventConfig.setStopMessagesOnContractCancellation((Boolean) map.get("stopMessagesOnContractCancellation"));
        flowEventConfig.setStopTodosOnContractCancellation((Boolean) map.get("stopTodosOnContractCancellation"));
        flowEventConfig.setCloseToDosUponCompletion((Boolean) map.get("closeToDosUponCompletion"));
        flowEventConfig.setName(flowEventName);
        flowEventConfig.setCancellationReason((PicklistItem) map.get("cancellationReason"));
        flowEventConfig.setMaxFlowRuns((int) map.get("maxFlowRuns"));
        flowEventConfigRepository.save(flowEventConfig);
        return flowEventConfig;
    }

    public FlowSubEventConfig createOrFetchFlowSubEventConfig(
            Map<String, Object> subEventMap) {

        FlowEventConfig flowEventConfig = (FlowEventConfig) subEventMap.get("flowEventConfig");
        FlowSubEventConfig.FlowSubEventName flowSubEventName =
                (FlowSubEventConfig.FlowSubEventName) subEventMap.get("flowSubEventName");
        FlowSubEventConfig flowSubEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig(
                flowSubEventName, flowEventConfig);
        if (flowSubEventConfig != null)
            return flowSubEventConfig;
        FlowSubEventConfig.RequiredAction requiredAction = (FlowSubEventConfig.RequiredAction) subEventMap.get("requiredAction");
        int maxTrials = (int) subEventMap.get("maxTrials");
        int maxReminders = (int) subEventMap.get("maxReminders");
        boolean terminateOnMaxTrials = (boolean) subEventMap.get("terminateOnMaxTrials");
        boolean terminateOnMaxReminders = (boolean) subEventMap.get("terminateOnMaxReminders");

        flowSubEventConfig = new FlowSubEventConfig();
        flowSubEventConfig.setName(flowSubEventName);
        flowSubEventConfig.setFlowEventConfig(flowEventConfig);
        flowSubEventConfig.setRequiredAction(requiredAction);
        flowSubEventConfig.setMaxTrials(maxTrials);
        flowSubEventConfig.setMaxReminders(maxReminders);
        flowSubEventConfig.setTerminateContractOnMaxTrials(terminateOnMaxTrials);
        flowSubEventConfig.setTerminateContractOnMaxReminders(terminateOnMaxReminders);
        flowSubEventConfigRepository.save(flowSubEventConfig);
        return flowSubEventConfig;
    }

    public void updateIpamFlowSetupACC6255() {

        FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);

        FlowSubEventConfig flowSubEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig(
                FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE, flowEventConfig);
        flowSubEventConfig.setMaxTrials(5);
        flowSubEventConfigRepository.save(flowSubEventConfig);

        List<Map<String, Integer>> values = new ArrayList<>();
        Map<String, Integer> map = new HashMap<>();
        map.put("trial", 1); map.put("reminder", 1); map.put("period", 0); values.add(map); map = new HashMap<>();
        map.put("trial", 2); map.put("reminder", 1); map.put("period", 0); values.add(map); map = new HashMap<>();
        map.put("trial", 3); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 4); map.put("reminder", 1); map.put("period", 24); values.add(map); map = new HashMap<>();
        map.put("trial", 5); map.put("reminder", 1); map.put("period", 24); values.add(map);
        createFlowProgressPeriods(values, flowSubEventConfig);

        SelectQuery<DDMessaging> q = new SelectQuery<>(DDMessaging.class);
        q.filterBy("event", "=", DDMessagingType.ClientPaidCashAndNoSignatureProvided);
        q.filterBy("subType", "=", DDMessagingSubType.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY);

        DDMessagingRepository ddMessagingRepository = Setup.getRepository(DDMessagingRepository.class);
        q.execute()
                .forEach(d -> {
                    d.setDeleted(true);
                    ddMessagingRepository.save(d);
                });

        TagRepository tagRepository = Setup.getRepository(TagRepository.class);
        List<Tag> tags = tagRepository.findByNameIgnoreCase("ipam_x_days_before_paid_end_date");
        if (tags.isEmpty()) {
            Tag t = new Tag();
            t.setName("ipam_x_days_before_paid_end_date");
            t = tagRepository.save(t);

            flowEventConfig = flowEventConfigRepository.findOne(flowEventConfig.getId());
            flowEventConfig.getTags().add(t);
            flowEventConfigRepository.save(flowEventConfig);
        }

        tags = tagRepository.findByNameIgnoreCase("ipam_x_days_before_adjusted_end_date");
        if (tags.isEmpty()) {
            Tag t = new Tag();
            t.setName("ipam_x_days_before_adjusted_end_date");
            t = tagRepository.save(t);

            flowEventConfig = flowEventConfigRepository.findOne(flowEventConfig.getId());
            flowEventConfig.getTags().add(t);
            flowEventConfigRepository.save(flowEventConfig);
        }
    }
    public boolean showSwitchClientToPayingViaCcButton(Contract contract) {
        return showSwitchClientToPayingViaCcButton(contract, true);
    }

    public boolean showSwitchClientToPayingViaCcButton(Contract contract, boolean checkCurrentMonthPayment) {
        if (contract.isPayingViaCreditCard() || contract.isOneMonthAgreement()) {
            return false;
        }

        if (!directDebitRepository.existsByContractPaymentTerm_Contract(contract)) {
            return false;
        }

        if (!checkCurrentMonthPayment) {
            return true;
        }

        DateTime lastPaymentReceived = Setup.getApplicationContext()
                .getBean(PaymentService.class)
                .getLastReceivedMonthlyPaymentDate(contract);

        return lastPaymentReceived != null &&
                (lastPaymentReceived.isAfter(new DateTime().dayOfMonth().withMinimumValue()) ||
                        lastPaymentReceived.toString("yyyy-MM").equals(new LocalDate().toString("yyyy-MM")));
    }

    public boolean isPayingViaCreditCard(Contract c) {
        return c.isPayingViaCreditCard() || ContractService.isIpam(c) ||
                existsRunningFlow(c, FlowEventConfig.FlowEventName.EXTENSION_FLOW);
    }

    // ACC-8019
    public void stopRecurringFailureFlows(Contract contract, ContractPaymentConfirmationToDo todo, boolean isComplete) {

        FlowProcessorEntity failureFlow = getRunningFlow(
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                ClientPayingViaCreditCardService.recurringFailureFlows,
                contract, todo);

        if (failureFlow == null) {
            return;
        }

        logger.info("flow id: " + failureFlow.getId() + "; sub-event name: " + failureFlow.getCurrentSubEvent().getName());

        failureFlow.setCompleted(isComplete);
        failureFlow.setStopped(!isComplete);
        flowProcessorEntityRepository.save(failureFlow);
    }

    public String dataCorrectionStopPayingViaCCFlowForIPAMFlowsACC8781() {
        ContractService contractService = Setup.getApplicationContext().getBean(ContractService.class);
        Long lastId = -1L;
        List<FlowProcessorEntity> l;
        do {
            SelectQuery<FlowProcessorEntity> q = new SelectQuery<>(FlowProcessorEntity.class);
            q.filterBy("id", ">", lastId);
            q.filterBy("stopped", "=", false);
            q.filterBy("completed", "=", false);
            q.filterBy("flowEventConfig.name", "=", FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);
            q.filterBy("currentSubEvent.name", "=", FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE);
            q.filterBy("contractPaymentTerm.contract.payingViaCreditCard", "=", true);
            q.setLimit(200);
            l = q.execute();

            for (FlowProcessorEntity f : l) {
                try {
                    logger.info("flow id: " + f.getId());
                    contractService.updatePayingViaCreditCardFlag(f.getContractPaymentTerm().getContract(), false);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (!l.isEmpty()) lastId = l.get(l.size() - 1).getId();
        } while (!l.isEmpty());

        return "Done.";
    }

    public void stopRunningFlow(Contract contract, FlowEventConfig.FlowEventName eventName) {
        FlowProcessorEntity entity = getFirstRunningFlow(contract, eventName);
        if (entity != null) {
            logger.info("stop ipam flow : " + entity.getId());
            entity.setStopped(true);
            flowProcessorEntityRepository.save(entity);
        }
    }

    public PicklistItem createOrFetchCancellationReason(String code, String name) {
        PicklistItem cancellationReasonItem = PicklistHelper.getItemNoException(AccountingModule.PICKLIST_TERMINATION_REASON_LIST, code);
        if (cancellationReasonItem == null) {
            cancellationReasonItem = new PicklistItem();
            cancellationReasonItem.setCode(code);
            cancellationReasonItem.setName(name);
            cancellationReasonItem.setList(Setup.getRepository(PicklistRepository.class).findByCode(AccountingModule.PICKLIST_TERMINATION_REASON_LIST));
            Setup.getRepository(PicklistItemRepository.class).save(cancellationReasonItem);
        }
        return cancellationReasonItem;
    }
}
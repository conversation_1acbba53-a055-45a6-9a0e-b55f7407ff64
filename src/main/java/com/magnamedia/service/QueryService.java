package com.magnamedia.service;


import com.google.common.collect.Iterables;
import com.magnamedia.controller.TransactionsController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.template.TemplateChannelParameter;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.*;
import com.magnamedia.entity.*;
import com.magnamedia.entity.dto.ContractPaymentConfirmationToDoDto;
import com.magnamedia.entity.dto.ExpenseRequestTodoSearchApiDto;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.module.type.*;
import com.magnamedia.entity.*;
import com.magnamedia.entity.dto.*;
import com.magnamedia.entity.projection.ExpenseProjection;
import com.magnamedia.extra.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.magnamedia.entity.ContractPaymentConfirmationToDo.Source.*;

@Service
public class QueryService {
    private static final Logger logger = Logger.getLogger(QueryService.class.getName());

    @Autowired
    private AttachmentService attachmentService;

    public String getMainAfterCashQuery() {
        return "SELECT distinct cpt.id from ContractPaymentTerm cpt " +
                "inner join cpt.contract con " +
                "inner join con.client " +
                "WHERE cpt.isActive = true and con.scheduledDateOfTermination is null " +
                    "and con.status in ('ACTIVE', 'PLANNED_RENEWAL', 'POSTPONED') " +
                    "and con.isOneMonthAgreement = false " +
                    "and not exists (select 1 from DirectDebitSignature dds " +
                                    "where dds.contractPaymentTerm.contract = cpt.contract)";
    }

    public Page<ContractPaymentConfirmationToDoDto> contractPaymentConfirmationTodoSearch(
            List<FilterItem> filters, Pageable pageable) {

        String select = "select  new com.magnamedia.entity.dto.ContractPaymentConfirmationToDoDto(" +
                "todo.id, cl.id, cl.name, todo.paymentMethod, h.name, " +
                "c.id, c.contractType," +
                "(select p.name from PicklistItem p where p = h.nationality ), todo.creationDate, " +
                "cpt.isActive, " +
                "(select case when todo.paymentMethod = 'WIRE_TRANSFER' and w.actualReceivedAmount is not null and w.actualReceivedAmount <> 0 "  +
                    "then sum(w.actualReceivedAmount) " +
                    "else sum(w.amount) " +
                "end from ContractPaymentWrapper w where w.contractPaymentConfirmationToDo = todo), " +
                "todo.expectedDate, todo.typesOfPayments, " +
                "todo.paymentType.name, todo.source, todo.description, todo.cleanAuthorizationCode, " +
                getDateChangedToReceivedViaWrapperStrWithAlias("todo") + " todo.transferReference) " +
                "from ContractPaymentConfirmationToDo todo " +
                "join todo.contractPaymentTerm cpt " +
                "join cpt.contract c " +
                "join c.client as cl " +
                "left join c.housemaid h ";

        String countQuery= "select count(todo.id) from ContractPaymentConfirmationToDo todo ";

        StringBuilder conditions = new StringBuilder("");
        Map<String, Object> parameters = new HashMap<>();

        if (filters != null && !filters.isEmpty()) {
            conditions.append("where ");
            for (int i = 0; i < filters.size(); i++) {
                FilterItem f = filters.get(i);
                parameters.put("para" + i, f.getProperty().equals("paymentMethod") ?
                        PaymentMethod.valueOf((String) f.getValue()) :
                        f.getProperty().equals("contractPaymentTerm.contract.id") ?
                                Long.valueOf(f.getValue().toString()) :
                                f.getProperty().equals("dateChangedToReceived") ?
                                    f.getOperation().equals("<=") ?
                                        LocalDateTime.parse(f.getValue().toString()).plusDays(1).minusMillis(1).toDate() :
                                        LocalDateTime.parse(f.getValue().toString()).toDate() :
                                f.getValue());

                conditions.append(!f.getProperty().equals("dateChangedToReceived") ?
                                "todo." : getDateChangedToReceivedViaWrapperStr("todo"))
                        .append(f.getProperty().equals("dateChangedToReceived") ? "" : f.getProperty())
                        .append(" ")
                        .append(f.getProperty().equals("dateChangedToReceived") && f.getOperation().equals("=") ? "between" : f.getOperation())
                        .append(" :")
                        .append("para")
                        .append(i)
                        .append(" ");

                if (f.getProperty().equals("dateChangedToReceived") && f.getOperation().equals("=")) {
                    parameters.put("paraEndDate" + i, LocalDateTime.parse(f.getValue().toString()).plusDays(1).minusMillis(1).toDate());
                    conditions.append(" and :paraEndDate").append(i);
                }

                conditions.append(i != (filters.size() - 1) ? " and " : "");
            }
        }

        String query = sortDirectQueryByPageable(pageable, select + conditions, "todo");
        SelectQuery<ContractPaymentConfirmationToDoDto> q = new SelectQuery<>(
                query, countQuery + conditions, ContractPaymentConfirmationToDoDto.class, parameters);
        Page<ContractPaymentConfirmationToDoDto> p = q.execute(pageable);

        Set<String> paymentTypeIds = new HashSet<>();

        // fill attachments
        List<Map> attachments = getAttachmentsWithBasicInfo(p.getContent().stream()
                .map(c -> c.getId()).collect(Collectors.toList()), "ContractPaymentConfirmationTodo");
        p.getContent().forEach(c -> {
            List<Map> attachmentList = attachments.stream()
                    .filter(a -> c.getId().equals(a.get("ownerId"))).collect(Collectors.toList());
            attachmentList.forEach(a -> a.remove("ownerId"));
            c.setAttachments(attachmentList);

            if (c.getTypesOfPaymentsLabels() != null && !c.getTypesOfPaymentsLabels().isEmpty()) {
                String[] payments = c.getTypesOfPaymentsLabels().split(",");
                for (String id: payments) {
                    if (paymentTypeIds.contains(id)) continue;
                    paymentTypeIds.add(id);
                }
            }
        });

        // TypesOfPaymentsLabels
        Map<Long, Object> paymentNames = getPicklistItemNames(paymentTypeIds.stream()
                .map(Long::valueOf).collect(Collectors.toList()));
        p.getContent().forEach(c -> {

            if (c.getTypesOfPaymentsLabels() != null && !c.getTypesOfPaymentsLabels().isEmpty()) {
                String[] payments = c.getTypesOfPaymentsLabels().split(",");
                List<String> paymentsCodes = new ArrayList<>();

                for (String id: payments) {
                    if (!paymentNames.containsKey(Long.valueOf(id))) continue;
                    paymentsCodes.add((String) paymentNames.get(Long.valueOf(id))) ;
                }
                if (!paymentsCodes.isEmpty())
                    c.setTypesOfPaymentsLabels(String.join(", ", paymentsCodes));

            } else c.setTypesOfPaymentsLabels(c.getPaymentType());
        });

        return p;
    }

    public Map getPaymentsForGPT(Contract contract) {

        String bouncedPaymentsQuery = "select new map(p.status as status, p.typeOfPayment.name as type, " +
                "p.dateOfPayment as date, p.amountOfPayment as amount, p.id as paymentId, " +
                    "case when p.sentToBankByMDD = true and ddf is not null and exists ( " +
                        "select 1 from ManualDDFToSend ms where ms.id = ddf.manualDDFToSendId and ms.status = 'SENT') " +
                    "then true else false end as sentToBankByMDD) " +
                "from Payment p " +
                "left join DirectDebitFile ddf on ddf.id = p.directDebitFileId " +
                "where p.contract.id = :contractId and p.isDeleted = false and p.status = 'BOUNCED' and p.replaced = false";

        String pdpPaymentsQuery = "select new map(p.status as status, p.typeOfPayment.name as type, " +
                "p.dateOfPayment as date, p.amountOfPayment as amount, p.id as paymentId, "  +
                    "case when p.sentToBankByMDD = true and ddf is not null and exists ( " +
                        "select 1 from ManualDDFToSend ms where ms.id = ddf.manualDDFToSendId and ms.status = 'SENT') " +
                    "then true else false end as sentToBankByMDD) " +
                "from Payment p " +
                "left join DirectDebitFile ddf on ddf.id = p.directDebitFileId " +
                "where p.contract.id = :contractId and p.isDeleted = false and p.status in ('PRE_PDP','PDC') and " +
                "p.dateOfPayment between :previousMonthDate and :nextMonthDate";

        Map<String, Object> param = new HashMap<String, Object>() {{
            put("contractId", contract.getId());
            put("nextMonthDate", new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate());
            put("previousMonthDate", new LocalDate().minusMonths(1).dayOfMonth().withMinimumValue().toDate());
        }};

        List<Map> pdpPayments = new SelectQuery<>(pdpPaymentsQuery, "", Map.class, param).execute();
        param.remove("nextMonthDate");
        param.remove("previousMonthDate");
        List<Map> bouncedPayments = new SelectQuery<>(bouncedPaymentsQuery, "", Map.class, param).execute();

        return new HashMap<String, Object>() {{
            put("ExistingBouncedPayments", fillPaymentsInfo(bouncedPayments));
            put("ExistingNotBouncedPayments", fillPaymentsInfo(pdpPayments));
        }};
    }

    private StringBuilder fillPaymentsInfo(List<Map> payments) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < payments.size(); i++) {
            Map row = payments.get(i);
            if (i > 0) {
                stringBuilder.append(System.lineSeparator()).append("###").append(System.lineSeparator());
            }
            PaymentStatus status = (PaymentStatus) row.get("status");
            stringBuilder.append("Payment status: ").append(status.getLabel())
                    .append(", Type: ").append(row.get("type"))
                    .append(", Date: ").append(row.get("date"))
                    .append(", Amount in AED: ").append(((Double) (row.get("amount"))).intValue())
                    .append(", PaymentId: ").append(row.get("paymentId"))
                    .append(", Payment sent to collection: ").append(((boolean) row.get("sentToBankByMDD")));
        }
        return stringBuilder;
    }

    public Page<ExpenseRequestPayInvoiceDto> getPayInvoiceByBeneficiaryIdAndExpenseIdApi(
            Long beneficiaryId, Expense expense,
            PayInvoiceSearchDto payInvoiceSearchDto, Pageable pageable) {

        String select = "select new com.magnamedia.entity.dto.ExpenseRequestPayInvoiceDto( " +
                "e.id, e.relatedToInfo, e.expense.caption, e.amount, " +
                "e.creationDate, h.housemaidType, %s) from ExpenseRequestTodo e " +
                "left join Housemaid h on e.relatedToType = 'MAID' and e.relatedToId = h.id ";

        if (expense.getCode().equals(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_COVID_TEST_EXPENSE_CODE))) {
            select = String.format(select, "(select c.testReason.name from CovidTestRequest c " +
                    "where c.id = (select max(id) from CovidTestRequest c1 where c1.housemaid = h and c1.status = 'DONE')) ");
        } else select= String.format(select, "''");

        String countSelect = "select count (e.id) from ExpenseRequestTodo e ";

        StringBuilder cond = new StringBuilder(
                "where e.beneficiaryId = :beneficiaryId and e.expense = :expense " +
                        "and e.paymentMethod = 'INVOICED' and e.status = 'PENDING_PAYMENT' " +
                        "and e.expensePayment is null and e.beneficiaryType = 'SUPPLIER' ");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("beneficiaryId", beneficiaryId);
        parameters.put("expense", expense);

        if (payInvoiceSearchDto.getFromDate() != null) {
            cond.append(" and e.creationDate >= :startDate ");
            parameters.put("startDate", payInvoiceSearchDto.getFromDate());
        }

        if (payInvoiceSearchDto.getToDate() != null) {
            cond.append(" and e.creationDate < :endDate ");
            parameters.put("endDate", DateUtil.addDays(payInvoiceSearchDto.getToDate(), 1));
        }

        if (payInvoiceSearchDto.getMaidName() != null) {
            cond.append(" and (e.relatedToType = 'MAID' and e.relatedToId in (select id from Housemaid where name like :maidName)) ");
            parameters.put("maidName", payInvoiceSearchDto.getMaidName());
        }

        return new SelectQuery<>(select + cond,countSelect + cond,
                ExpenseRequestPayInvoiceDto.class, parameters)
                .execute(pageable);
    }

    public Page<ExpenseRequestTodoSearchApiDto> getExpenseRequestTodoSearchApi(
            ExpenseRequestSearchDto search, Pageable pageable) {

        String fromStatement = " from ExpenseRequestTodo e " +
                "left join e.expense as ex " +
                "left join e.expensePayment as ep " +
                "left join ep.fromBucket fromBucket " +
                "left join ep.toBucket toBucket ";

        String query = "Select " +
                "new com.magnamedia.entity.dto.ExpenseRequestTodoSearchApiDto(" +
                "e.id, " +
                "ep.transaction.id, " +
                "(select h.name from Housemaid h where e.relatedToType = 'MAID' and h.id = e.relatedToId), " +
                "ex.caption, " +
                "(select pi1.name from PicklistItem pi1 where pi1 = e.purposeAdditionalDescription ), " +
                "e.creationDate, " +
                "e.amount, " +
                "e.loanAmount, " +
                "(select pi1.name from PicklistItem pi1 where pi1 = e.currency ), " +
                "e.paymentMethod, " +
                "e.status,(select u.fullName from User u where u = e.requestedBy ), " +
                "e.approvedBy, " +
                "e.notes, " +
                "ep.id, " +
                "e.description, " +
                "e.relatedToInfo, " +
                "e.benefeciaryInfo, " +
                "ex.name, " +
                "(select p.managerActionBy.fullName from PayrollAccountantTodo p where  p.expensePayment = ep), " +
                "e.amountInLocalCurrency, " +
                "fromBucket.name," +
                "ex.id, " +
                "e.isRefunded, " +
                "e.rejectionNotes, " +
                "ex.isSecure, " +
                "fromBucket.isSecure, " +
                "toBucket.isSecure, " +
                "e.pendingForApproval, " +
                "ex.hideRequestRefundButton ) " + fromStatement;

        String countQuery = "Select count(e.id)  " + fromStatement;
        Map<String, Object> conditionsAndParameters =  getTheConditionsForExpenseRequestTodoQuery(search);
        Map<String, Object> parameters = (Map<String, Object>) conditionsAndParameters.get("parameters");
        if ((parameters != null && !parameters.isEmpty()) || ExpenseRelatedTo.ExpenseRelatedToType.NOT_DETERMINED.equals(search.getRelatedToType())) {
            String conditions = String.join("and", (List<String>) conditionsAndParameters.get("conditionsList"));
            query += " where " + conditions;
            countQuery += " where " + conditions;
        }

        query = sortDirectQueryByPageable(pageable, query, "e");
        SelectQuery<ExpenseRequestTodoSearchApiDto> q = new SelectQuery<>(
                query, countQuery, ExpenseRequestTodoSearchApiDto.class, parameters);
        Page<ExpenseRequestTodoSearchApiDto> p = q.execute(pageable);

        // fill attachments
        List<Map> attachments = getAttachmentsWithBasicInfo(p.getContent().stream()
                .map(ExpenseRequestTodoSearchApiDto::getId).collect(Collectors.toList()), "ExpenseRequestTodo");
        p.getContent().forEach(e -> {
            List<Map> attachmentList = attachments.stream()
                    .filter(a -> e.getId().equals(a.get("ownerId"))).collect(Collectors.toList());
            attachmentList.forEach(a -> a.remove("ownerId"));
            e.setAttachments(attachmentList);
        });

        return p;
    }

    public Page<ClientPaymentSearchApiDto> getPaymentsSearchApi(Contract contract, Boolean includeDeleted,
            PaymentTab paymentTab, Boolean firstLoad, Pageable pageable) {

        Map<String, Object> parameters = new HashMap<>();

        String fromStatement = "from Payment p " +
                               "inner join p.contract c " +
                               "left join p.creator cr " +
                               "left join p.lastModifier lm ";

        String todoSubQuery = "select distinct todo.id as todoId " +
                "from ContractPaymentWrapper cpw " +
                "inner join cpw.contractPaymentConfirmationToDo todo on " +
                    "(cpw.generatedPaymentId = p.id or " +
                    "(cpw.generatedPaymentId is null and cpw.replacedFuturePaymentId = p.id) or " +
                    "(cpw.generatedPaymentId is null and cpw.replacedBouncedPaymentId is not null and " +
                    "p.replacementFor.id = cpw.replacedBouncedPaymentId)) " +
                "where p.methodOfPayment = 'CARD' and p.status not in ('RECEIVED', 'DELETED') and " +
                    "p.replaced = false and todo.paymentMethod = 'CARD' and todo.payingOnline = true and " +
                    "todo.disabled = false and (todo.reactivationPayment = true or " +
                        "(c.status <> 'CANCELLED' and c.status <> 'EXPIRED') or " +
                        "c.dateOfTermination is null or c.dateOfTermination <= todo.creationDate) and " +
                    "todo.showOnERP = false and todo.disabled = false and todo.source <> 'CLIENT_REFUND' " +
                "group by p " +
                "order by max(todo.creationDate) desc";

        String query = "select new com.magnamedia.entity.dto.ClientPaymentSearchApiDto("  +
                "p.id, " +
                "(select pli.name from PicklistItem pli where pli = p.typeOfPayment), " +
                "p.isReplacement, " +
                "c.id as contractId, " +
                "c.client.id as clientId, " +
                "p.replacementFor.id, " +
                "p.methodOfPayment, " +
                "p.dateOfPayment, " +
                "p.replaced, " +
                "p.status, " +
                "(select name from PicklistItem pli where pli = p.reasonOfBouncingCheque), " +
                "p.dateOfBouncing, " +
                "p.amountOfPayment, " +
                "p.discount, " +
                "p.isInitial, " +
                "p.oldApplicationId, " +
                "(select applicationId from DirectDebitFile ddf where ddf.id = p.directDebitFileId), " +
                "p.chequeNumber, " +
                "p.creationDate, " +
                "p.dateChangedToPDP, " +
                "p.dateChangedToReceived, " +
                "p.vat, " +
                "(select count(b.id) from BouncedPaymentLog b where b.payment = p), " +
                "(select min(dateOfBouncing) from BouncedPaymentLog b where b.payment = p), " +
                "p.chequeName, "+
                "p.prepareToRefund, "+
                "(select name from PicklistItem pli where pli = p.bankName), " +
                "p.note, " +
                "p.vatPaidByClient, " +
                "cr.fullName, " +
                "lm.fullName, " +
                "p.statusLastModificationDate, " +
                "(" + todoSubQuery + ")" +
                ") " + fromStatement ;


        StringBuilder conditionsBuilder = new StringBuilder(getConditionsFromSelectFilter(CurrentRequest.getSearchFilter(), parameters, "p"));
        conditionsBuilder.append(CurrentRequest.getSearchFilter() == null || CurrentRequest.getSearchFilter().isEmpty() ? " where " : " and ");

        parameters.put("contract", contract);
        conditionsBuilder.append(" p.contract = :contract ");

        if (includeDeleted != null && !includeDeleted) {
            parameters.put("deletedStatus", PaymentStatus.DELETED);
            conditionsBuilder.append(" and p.status != :deletedStatus ");
        }

        java.util.Date paymentShowDate = Payment.getPaymentShowDate();
        if (!Payment.hasPMasterPosition() && paymentShowDate != null) {
            parameters.put("paymentShowDate", paymentShowDate);
            conditionsBuilder.append(" and p.dateOfPayment >= :paymentShowDate ");
        }

        switch (paymentTab) {
            case CURRENT_MONTH:
                parameters.put("monthStart", new DateTime().dayOfMonth().withMinimumValue().withTimeAtStartOfDay().toDate());
                parameters.put("monthEnd", new DateTime().plusMonths(1)
                        .dayOfMonth().withMinimumValue().withTimeAtStartOfDay().minusMillis(1).toDate());
                conditionsBuilder.append(" and p.dateOfPayment >= :monthStart and p.dateOfPayment <= :monthEnd ");
                break;
            case OTHER_PAYMENTS:
                parameters.put("monthStart", new DateTime().dayOfMonth().withMinimumValue().withTimeAtStartOfDay().toDate());
                parameters.put("monthEnd", new DateTime().plusMonths(1)
                        .dayOfMonth().withMinimumValue().withTimeAtStartOfDay().minusMillis(1).toDate());
                conditionsBuilder.append(" and (p.dateOfPayment < :monthStart or p.dateOfPayment > :monthEnd)");
                break;
            case ALL:
                if (!firstLoad) break;

                SelectQuery<Long> selectQuery = new SelectQuery<>("select count(p.id) from Payment p " + conditionsBuilder,
                        "", Long.class, parameters);
                long total = selectQuery.execute().stream().findFirst().orElse(0L);
                logger.log(Level.SEVERE, "getPayments total is: " + total);

                java.util.Date monthStart = DateUtil.getDayStart(DateUtil.getMonthStart(DateUtil.now()));
                parameters.put("monthStart", monthStart);
                long totalBeforeThisMonth = new SelectQuery<>("select count(p.id) from Payment p " + conditionsBuilder + " and p.dateOfPayment < :monthStart ",
                        "", Long.class, parameters).execute().stream().findFirst().orElse(0L);
                parameters.remove("monthStart");
                logger.log(Level.SEVERE, "getPayments totalBeforeThisMonth is: " + totalBeforeThisMonth);
                logger.log(Level.SEVERE, "getPayments page is: " + (total!=0 && totalBeforeThisMonth!=0 && total/totalBeforeThisMonth!= 1 ?totalBeforeThisMonth/pageable.getPageSize():0));
                logger.log(Level.SEVERE, "getPayments page size is: " + pageable.getPageSize());
                pageable = PageRequest.of(total!=0 && totalBeforeThisMonth!=0 && total/totalBeforeThisMonth!= 1 ? (int) ((total - totalBeforeThisMonth) / pageable.getPageSize()) :0, pageable.getPageSize());

                break;
        }

        query = pageable != null && !pageable.getSort().isEmpty() ?
                sortDirectQueryByPageable(pageable, query + conditionsBuilder , "p") :
                query + conditionsBuilder + " order by p.dateOfPayment desc";

        String countQuery = "select count(p.id) " + fromStatement + conditionsBuilder;

        Page<ClientPaymentSearchApiDto> p = new SelectQuery<>(query , countQuery, ClientPaymentSearchApiDto.class, parameters).execute(pageable);

        List<Map> attachments = getAttachmentsWithBasicInfo(p.getContent().stream()
                .map(ClientPaymentSearchApiDto::getId).collect(Collectors.toList()), "Payment");

        ContractPaymentConfirmationToDoRepository cpcTodoRepository = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class);
        p.getContent().forEach(e -> {
            List<Map> attachmentList = attachments.stream()
                    .filter(a -> e.getId().equals(a.get("ownerId"))).collect(Collectors.toList());
            attachmentList.forEach(a -> a.remove("ownerId"));
            e.setAttachments(attachmentList);
            if (e.getTodoId() != null) {
                e.setTodo(cpcTodoRepository.findOne(e.getTodoId()));
            }
        });
        return p;
    }

    private Map<String, Object> getTheConditionsForExpenseRequestTodoQuery(ExpenseRequestSearchDto search) {
        List<String> conditionsList = new ArrayList<>();
        Map<String, Object> parameters = new HashMap<>();

        // ACC-8985
        if (!CurrentRequest.getUser().hasPosition(AccountingModule.EXPENSE_HISTORY_FULL_ACCESS)) {
            Set<Long> ids = new HashSet<>();
            ids.add(CurrentRequest.getUser().getId());
            // Apply employee access restriction - only show expenses requested by the current user or employees under them
            if (CurrentRequest.getUser().getEmail() != null && !CurrentRequest.getUser().getEmail().isEmpty()) {
                List<OfficeStaff> currentStaff = Setup.getRepository(OfficeStaffRepository.class)
                        .findOfficeStaffByLinkedUserEmail(CurrentRequest.getUser().getEmail());

                if (!currentStaff.isEmpty()) {
                    ids.addAll(OfficeStaffHelper.getEmployeeHierarchyTree(currentStaff.get(0)));
                }
            }

            conditionsList.add(" e.requestedBy.id in :allowedUsers ");
            parameters.put("allowedUsers", ids);
        }

        if (search.getId() != null) {
            conditionsList.add(" e.id = :id ");
            parameters.put("id", search.getId());
        }
        if (search.getExpenseId() != null) {
            conditionsList.add(" ex.id = :exId ");
            parameters.put("exId", search.getExpenseId());
        }
        if (search.getExpenseCaption() != null) {
            conditionsList.add(" ex.caption like :caption ");
            parameters.put("caption", "%" + search.getExpenseCaption() + "%");
        }
        if (search.getStatus() != null) {
            conditionsList.add(" e.status = :status ");
            parameters.put("status", search.getStatus());
        }
        if (search.getMultipleStatus() != null && !search.getMultipleStatus().isEmpty()) {
            conditionsList.add(" e.status in :statusIn ");
            parameters.put("statusIn", search.getMultipleStatus());
        }
        if (search.getAmount() != null && search.getAmountOperator() != null) {
            conditionsList.add(" e.amount " + search.getAmountOperator() + " :amount ");
            parameters.put("amount", search.getAmount());

            if (CurrentRequest.getUser() == null || !CurrentRequest.getUser().hasPosition(AccountingModule.POSITION_READ_SECURE_EXPENSE)) {
                conditionsList.add(
                        " ( " +
                        "(ex.isSecure is null or ex.isSecure <> true) and " +
                        "(fromBucket.isSecure is null or fromBucket.isSecure <> true) and " +
                        "(toBucket.isSecure is null or toBucket.isSecure <> true) " +
                        ") ");
            }
        }
        if (search.getPaymentMethod() != null) {
            conditionsList.add(" e.paymentMethod = :paymentMethod ");
            parameters.put("paymentMethod", search.getPaymentMethod());
        }
        if (search.getBeneficiaryType() != null) {
            conditionsList.add(" e.beneficiaryType = :beneficiaryType ");
            parameters.put("beneficiaryType", search.getBeneficiaryType());
        }
        if (search.getBeneficiaryId() != null) {
            conditionsList.add(" e.beneficiaryId = :beneficiaryId ");
            parameters.put("beneficiaryId", search.getBeneficiaryId());
        }

        if (search.getBucketName() != null ) {
            conditionsList.add(" fromBucket.name = :bucketName ");
            parameters.put("bucketName", search.getBucketName());
        }

        if (search.getPendingForApproval() != null ) {
            if (search.getPendingForApproval().equals("Pending CEO")) {
                conditionsList.add(" (e.taskName = 'WAITING_COO_APPROVAL') ");
            } else {
                String s = search.getPendingForApproval().replace("Pending ", "");
                if (s.contains("@")) {
                    conditionsList.add(" (e.taskName = 'WAITING_MANAGER_APPROVAL' and " +
                            "e.approveHolderEmail = :approveHolderEmail) ");
                } else {
                    conditionsList.add(" (e.taskName = 'WAITING_MANAGER_APPROVAL' and " +
                            "e.approveHolder.fullName = :approveHolderEmail) ");
                }
                parameters.put("approveHolderEmail", s);
            }
        }

        if (search.getRelatedToType() != null) {
            if (ExpenseRelatedTo.ExpenseRelatedToType.NOT_DETERMINED.equals(search.getRelatedToType())) {
                conditionsList.add(" e.relatedToType is null ");
            } else {
                conditionsList.add(" e.relatedToType = :relatedToType ");
                parameters.put("relatedToType", search.getRelatedToType());
            }
        }
        if (search.getRelatedToId() != null) {
            conditionsList.add(" e.relatedToId = :relatedToId ");
            parameters.put("relatedToId", search.getRelatedToId());
        }
        if (search.getRelatedToIds() != null && !search.getRelatedToIds().isEmpty()) {
            conditionsList.add(" e.relatedToId in :relatedToIdIn ");
            parameters.put("relatedToIdIn", search.getRelatedToIds());
        }
        if (!StringUtils.isEmpty(search.getRelatedToName()) && search.getRelatedToType() != null) {
            List<Long> relatedToIds = new ArrayList();
            relatedToIds.add(-1L);
            if (search.getRelatedToType().equals(ExpenseRelatedTo.ExpenseRelatedToType.MAID)) {
                conditionsList.add(" e.relatedToId in (select h.id from Housemaid h where h.name like :hName ) ");
                parameters.put("hName", "%" + search.getRelatedToName() + "%");
            } else {
                conditionsList.add(" e.relatedToId in :hRelatedToIds ");
                parameters.put("hRelatedToIds", relatedToIds);
            }
        }

        if (search.getRequesterId() != null) {
            conditionsList.add(" e.requestedBy.id = :requestedById ");
            parameters.put("requestedById", search.getRequesterId());
        }
        if (search.getApprovedBy() != null) {
            conditionsList.add(" e.approvedBy like :approvedBy ");
            parameters.put("approvedBy", "%" + search.getApprovedBy() + "%");
        }
        if (search.getDate1() != null && search.getDateOperator() != null && search.getDate2() == null) {
            if (search.getDateOperator().equals("=")) {
                conditionsList.add(" e.creationDate >= :startDate ");
                parameters.put("startDate", new DateTime(search.getDate1()).withTimeAtStartOfDay().toDate());
                conditionsList.add(" e.creationDate < :endDate ");
                parameters.put("endDate", new DateTime(search.getDate1()).plusDays(1).withTimeAtStartOfDay().toDate());
            } else {
                conditionsList.add(" e.creationDate " + search.getDateOperator() + " :startDate ");
                parameters.put("startDate", search.getDate1());
            }
        } else if (search.getDate1() != null && search.getDate2() != null) {
            conditionsList.add(" e.creationDate >= :startDate ");
            parameters.put("startDate", search.getDate1());
            conditionsList.add(" e.creationDate <= :endDate ");
            parameters.put("endDate", search.getDate2());

        }
        if (search.getConfirmed() != null) {
            conditionsList.add(" (e.confirmed = :confirmed " +
                    (BooleanUtils.toBoolean(search.getConfirmed()) ? ") " : " or e.confirmed is null ) "));
            parameters.put("confirmed", search.getConfirmed());
        }

        if (search.getRefunded() != null) {
            conditionsList.add(" (e.isRefunded = :isRefunded " +
                    (BooleanUtils.toBoolean(search.getRefunded()) ? ") " : " or e.isRefunded is null ) "));
            parameters.put("isRefunded", search.getRefunded());
        }
        if (search.getRefundConfirmed() != null) {
            conditionsList.add(" (e.refundConfirmed = :refundConfirmed " +
                    (BooleanUtils.toBoolean(search.getRefundConfirmed()) ? ") " : " or e.refundConfirmed is null ) "));
            parameters.put("refundConfirmed", search.getRefundConfirmed());
        }

        //this filter is only being used when matching credit card statement record
        if (search.getOnlyUnconfirmed() != null && search.getOnlyUnconfirmed()) {
            if (search.getRefunded() != null && search.getRefunded()) {
                conditionsList.add(" (e.refundConfirmed = :unRefundConfirmed or e.refundConfirmed is null ) ");
                parameters.put("unRefundConfirmed", false);
            } else {
                conditionsList.add(" (e.confirmed = :unConfirmed or e.confirmed is null ) ");
                parameters.put("unConfirmed", false);
            }
            conditionsList.add(" fromBucket is not null  ");
        }

        // ACC-4505
        if(CurrentRequest.getSource() != null &&
                CurrentRequest.getSource().getCode() != null &&
                CurrentRequest.getSource().getCode()
                .equalsIgnoreCase("ACCOUNTING__ReconciliatorCreditCardStatementDetails")) {

            conditionsList.add(" ep.fromBucket is not null ");
        }

        return new HashMap<String, Object>(){{
            put("parameters", parameters);
            put("conditionsList", conditionsList);
        }};
    }

    public Double calculateTheAmountOfExpenseRequestTodoDependingOnItsStatus(
            ExpenseRequestSearchDto search, ExpenseRequestStatus status) {

        if (search.getStatus() != null && !search.getStatus().equals(status)) {
            return 0D;
        }

        String query = "select sum(abs(e.amountInLocalCurrency)) " +
                "from ExpenseRequestTodo e " +
                "left join e.expense as ex " +
                "left join e.expensePayment as ep " +
                "left join ep.fromBucket fromBucket " +
                "left join ep.toBucket toBucket " +
                "where e.status = :statusToCalc ";
        Map<String, Object> conditionsAndParameters =  getTheConditionsForExpenseRequestTodoQuery(search);
        Map<String, Object> parameters = new HashMap<>((Map<String, Object>) conditionsAndParameters.get("parameters"));

        if (!parameters.isEmpty()) {
            String conditions = String.join("and", (List<String>) conditionsAndParameters.get("conditionsList"));
            query += " and " + conditions;
        }

        parameters.put("statusToCalc", status);
        SelectQuery<Double> q = new SelectQuery<>(query, "", Double.class, parameters);
        List<Double> result = q.execute();

        return !result.isEmpty() && result.get(0) != null ?
                Double.parseDouble(PaymentHelper.df_two_decimal.format(result.get(0)).replace(",", "")) :
                0D;
    }

    public Map<Long, Object> getPicklistItemNames(List<Long> ids) {
        String query = "select new Map(p.id as id, p.name as name) from PicklistItem p " +
                "where p.id in :ids";
        Map<Long, Object> m = new HashMap<>();
        new SelectQuery<>(query, "", java.util.Map.class,
                new HashMap<String, Object>() {{
                    put("ids", ids);
                }})
                .execute()
                .forEach(o ->
                        m.put((Long) o.get("id"), o.get("name")));

        return m;
    }

    public String sortDirectQueryByPageable(
            Pageable pageable, String query, String alias) {

        if (pageable != null && !pageable.getSort().isEmpty()) {
            Iterator it = pageable.getSort().iterator();
            query += " ORDER BY ";
            StringBuilder queryBuilder = new StringBuilder(query);
            while(it.hasNext()) {
                Sort.Order order = (Sort.Order)it.next();
                if(order.getProperty().equals("fromBucket.name")){
                    queryBuilder.append(order.getProperty()).append(" ")
                            .append(order.getDirection().isAscending() ? "" : " DESC")
                            .append(order.getNullHandling().equals(Sort.NullHandling.NULLS_LAST) ? " NULLS LAST" : "");
                } else if(order.getProperty().equals("amountInLocalCurrency")) {
                    queryBuilder.append("abs(e.amountInLocalCurrency)").append(" ")
                            .append(order.getDirection().isAscending() ? "" : " DESC")
                            .append(order.getNullHandling().equals(Sort.NullHandling.NULLS_LAST) ? " NULLS LAST" : "");
                } else {
                    queryBuilder.append(alias).append(".").append(order.getProperty()).append(" ")
                            .append(order.getDirection().isAscending() ? "" : " DESC")
                            .append(order.getNullHandling().equals(Sort.NullHandling.NULLS_LAST) ? " NULLS LAST" : "");
                }
            }
            query = queryBuilder.toString();
        }

        return query;
    }

    public List<Map> getContractPayments(List<Long> ddIds) {
        String query = "select new map(cp.directDebit.id as ddId, cp.paymentType.id as paymentTypeId, " +
                "cp.paymentType.name as paymentTypeName, count(cp.id) as contractPaymentsCount) " +
                "from ContractPayment cp " +
                "where cp.directDebit.id in :ddIds " +
                "group by cp.directDebit.id";

        return new SelectQuery<>(query, "", java.util.Map.class,
                new HashMap<String, Object>() {{ put("ddIds", ddIds); }})
                .execute();
    }

    public List<Map> getAttachmentsWithBasicInfo(List<Long> ids, String owner) {
        String query = "select new Map(a.id as id, a.name as name, a.tag as tag, a.uuid as uuid, " +
                "ownerId as ownerId, a.creationDate as creationDate) " +
                "from Attachment a " +
                "where a.ownerId in (:ownerId) and a.ownerType = :ownerType";

        return new SelectQuery<>(query, "", java.util.Map.class,
                new HashMap<String, Object>() {{
                    put("ownerId", ids);
                    put("ownerType", owner);}})
                .execute();
    }

    public List<TemplateChannelParameter> getDdMessagingParametersBasedOnEventAndSubEvent(
            DDMessagingType event, DDMessagingSubType subType, String ddMessagingJoinColumn) {

        String query = "select para from TemplateChannelParameter para " +
                "join para.setting.template t " +
                "inner join DDMessaging d on d." + ddMessagingJoinColumn + " = t " +
                "where d.event = :p0 and " + (subType == null ? "" : " d.subType = :p1 and ") +
                "para.name is not null and para.name <> '' " +
                "group by para.name";

        Map<String, Object> m = new HashMap<>();
        m.put("p0", event);
        if (subType != null ) m.put("p1", subType);

        return new SelectQuery<>(query, "", TemplateChannelParameter.class, m).execute();
    }

    public Double getTheSumBalanceForAllBuckets(List<FilterItem> filters) {

        String select = "select sum(b.balance) from Bucket b " ;
        StringBuilder conditions = new StringBuilder();
        Map<String, Object> parameters = new HashMap<>();

        if (filters != null && !filters.isEmpty()) {
            conditions.append("where ");
            for (int i = 0; i < filters.size(); i++) {
                FilterItem f = filters.get(i);
                parameters.put("para" + i, f.getValue());

                if (f.getProperty().equals("isActive")) {
                    conditions.append(" b." + f.getProperty() + " " + f.getOperation() + " " + ":para" + i );
                }

                if (f.getProperty().equals("name")) {
                    parameters.put("para" + i, f.getOperation().equals("like") || f.getOperation().equals("not like") ?
                            "%"+ f.getValue().toString() + "%" : f.getValue().toString() );
                    conditions.append(" ((b.name" + " " + f.getOperation() + " :para" + i + ") :code) "
                            .replace(":code", f.getAlternatives() != null && f.getAlternatives().contains("code") ?
                            " or " +"(b.code" + " " + f.getOperation() + " :para" + i + ") " : ""));
                    }

                if (f.getProperty().equals("bucketType")) {
                    parameters.put("para" + i, BucketType.valueOf (f.getValue().toString()));
                    conditions.append(" b." + f.getProperty() + " " + f.getOperation() + " " + ":para" + i );
                }

                if (f.getProperty().equals("autoReplenishment")) {
                    conditions.append(" b." + f.getProperty() + " " + f.getOperation() + " " + ":para" + i );
                }

                if (f.getProperty().equals("balance")) {
                    conditions.append(" b." + f.getProperty() + " " + f.getOperation() + " " + ":para" + i );
                }

                if (i != (filters.size() - 1)) conditions.append(" and ");
            }
        }

        SelectQuery<Double> q = new SelectQuery<>(select + conditions, "", Double.class, parameters);
        List<Double> result = q.execute();

        return result.get(0);
    }
    public Page<DDFSendDDToBankDto> advanceSearchForSendDDToBankAcc8544(
            String clientName, Long contractId, String bankName,
            Long paymentTypeId, java.util.Date startDate, Pageable pageable) {

        return advanceSearchForSendDDToBankAcc8544(clientName, contractId, bankName, paymentTypeId, startDate, pageable, -1L);
    }

    public Page<DDFSendDDToBankDto> advanceSearchForSendDDToBankAcc8544(
            String clientName, Long contractId, String bankName,
            Long paymentTypeId, java.util.Date startDate, Pageable pageable, Long lastId) {

        Page<Map> page = Setup.getRepository(DirectDebitFileRepository.class)
                .getPendingDDFsGroupByApplicationIdFilteredByContractAcc8544(
                        lastId, clientName, contractId, bankName, paymentTypeId,
                        new LocalDate().toDate(), startDate, pageable);

        List<Map> attachments = getAttachmentsWithBasicInfo(page.getContent().stream()
                .map(ddf -> (Long) ddf.get("ddfId")).collect(Collectors.toList()), "DirectDebitFile");

        // fetch all contract payments related to filtered directDebits
        List<Map> contractPayments = getContractPayments(page.getContent().stream()
                .map(ddf -> (Long) ddf.get("ddId")).collect(Collectors.toList()));

        List<DDFSendDDToBankDto> l = page.getContent().stream().map(ddf -> {
            DDFSendDDToBankDto dto = new DDFSendDDToBankDto(ddf);

            dto.setAttachments(attachmentService.getDdfSecuredAttachments(attachments));
            Map result = contractPayments.stream()
                    .filter(m -> m.containsKey("ddId") && m.get("ddId").equals(dto.getDirectDebit().get("id")))
                    .findFirst()
                    .orElse(new HashMap());
            dto.setPaymentsCount((Long) result.getOrDefault("contractPaymentsCount",0L));
            dto.getDirectDebit().put("paymentType", dto.getPaymentsCount() > 0 ?
                new HashMap<String, Object>() {{
                    put("id", result.get("paymentTypeId"));
                    put("name", result.get("paymentTypeName"));}} : null);
            return dto;
        }).collect(Collectors.toList());

        return new PageImpl<>(l, pageable, page.getTotalElements());
    }

    public Page<DirectDebitFileSearchDto> directDebitFileSearch(Pageable pageable) {
        Map<String, Object> parameters = new HashMap<>();

        String conditions = getConditionsFromSelectFilter(CurrentRequest.getSearchFilter(), parameters, "ddf");

        String fromStatement = "from DirectDebitFile ddf " +
                "join ddf.directDebit dd " +
                "join dd.contractPaymentTerm cpt " +
                "join cpt.contract c " +
                "join c.client cl ";

        String selectQuery = "select  new com.magnamedia.entity.dto.DirectDebitFileSearchDto(" +
                "cl.id as clientId, cl.name as clientName, c.id as contractId, cpt.isActive as isActive, " +
                "cpt.bankName as bankName, dd.additionalDiscount as additionalDiscount, dd.additionalDiscountNotes as additionalDiscountNotesDD, " +
                "cpt.additionalDiscountNotes as additionalDiscountNotesCPT, " +
                "ddf.id as id, ddf.applicationId as applicationId, ddf.amount as amount, ddf.ddFrequency as ddFrequency, " +
                "ddf.creationDate as creationDate, ddf.startDate as startDate, ddf.expiryDate as expiryDate, " +
                "ddf.ddStatus as ddStatus, ddf.rejectionReason as rejectionReason, " +

                "(select max(r.creationDate) from BankDirectDebitActivationRecord r  where r.directDebitFileId = ddf.id) as resultDate, " +

                "ddf.notes as notes, dd.id as ddId, cpt.id as cptId, ddf.accountName as accountName, " +

                "(select cp.paymentType.name from ContractPayment cp where cp.id = " +
                    "(select min(cp1.id) from ContractPayment cp1 where cp1.directDebit.id = dd.id)) as paymentTypeName) " +
                fromStatement + conditions;

        StringBuilder fromStatementCountQuery = new StringBuilder()
                .append("from DirectDebitFile ddf ")
                .append("join ddf.directDebit dd ");

        if(conditions != null && !conditions.isEmpty()) {
            fromStatementCountQuery.append("join dd.contractPaymentTerm cpt ")
                    .append("join cpt.contract c ")
                    .append("join c.client cl ");
        }

        String countQuery= "select count(ddf.id) " + fromStatementCountQuery + conditions;
        String query = sortDirectQueryByPageable(pageable, selectQuery, "ddf");
        SelectQuery<DirectDebitFileSearchDto> q = new SelectQuery<>(
                query, countQuery, DirectDebitFileSearchDto.class, parameters);
        Page<DirectDebitFileSearchDto> page = q.execute(pageable);

        // fill attachments
        List<Map> ddAttachments = getAttachmentsWithBasicInfo(page.getContent().stream()
                .map(ddf -> ddf.getDdId()).collect(Collectors.toList()), "DirectDebit");
        List<Map> cptAttachments = getAttachmentsWithBasicInfo(page.getContent().stream()
                .map(cpt -> cpt.getCptId()).collect(Collectors.toList()), "ContractPaymentTerm");

        page.getContent().forEach(ddf -> {

            Map ddAdditionalAttachments = ddAttachments.stream().filter(a -> (a.get("tag").equals("ADDITIONAL_DISCOUNT_ATTACHMENT"))).findFirst().orElse(null);
            Map cptAdditionalAttachments = cptAttachments.stream().filter(a -> (a.get("tag").equals("ADDITIONAL_DISCOUNT_ATTACHMENT"))).findFirst().orElse(null);

            ddf.getDirectDebit().put("additionalDiscountAttachment", ddAdditionalAttachments != null &&
                    !ddAdditionalAttachments.isEmpty() ? ddAdditionalAttachments :
                     (ddf.getDirectDebit().get("additionalDiscount") != null && (Double) ddf.getDirectDebit().get("additionalDiscount") != 0
                     && ddf.getDirectDebit().get("contractPaymentTerm") != null ? cptAdditionalAttachments : null));

            ((Map<String, Object>) ddf.getDirectDebit().get("contractPaymentTerm")).put("attachments", cptAttachments);
        });

        return page;
    }

    public String getConditionsFromSelectFilter(
            SelectFilter filters, Map<String, Object> parametrs, String alias) {

        StringBuilder sb = new StringBuilder();
        if (filters != null && !filters.isEmpty()) {
            sb.append(" where ");
            renderFilter(filters, sb, parametrs, 1, alias);
        }
        return sb.toString();
    }

    private int renderFilter(
            SelectFilter fb,
            StringBuilder sb,
            Map<String, Object> parametersMap,
            int parameterIndex,
            String alias) {

        if (fb.getField() != null && fb.getOperation() != null) {
            parameterIndex = renderSqlFilter(
                    fb.getField(), fb.getOperation(), fb.getValue(), fb.getFieldType(),
                    sb, parametersMap, parameterIndex, alias);
            return parameterIndex;
        } else {
            sb.append(" ( ");
            parameterIndex = renderFilter(
                    fb.getLeft(), sb, parametersMap, parameterIndex, alias);
            if (fb.isAnd())
                sb.append(" and ");
            else
                sb.append(" or ");
            parameterIndex = renderFilter(
                    fb.getRight(), sb, parametersMap, parameterIndex, alias);
            sb.append(" ) ");
            return parameterIndex;
        }
    }

    private int renderSqlFilter(
            String field,
            String operation,
            Object objectValue,
            String fieldType,
            StringBuilder sb,
            Map<String, Object> parametersMap,
            int parameterIndex,
            String alias) {

        switch (field) {
            case "fromBucket.name":
            case "toBucket.name":
            case "revenue.name":
            case "expense.name":
            case "description":
                if(alias.equals("tr")){
                    sb.append(field);
                    sb.append(" ").append(operation);
                    if (objectValue != null) addParamOfValue(sb, parameterIndex, objectValue, parametersMap);
                }
                parameterIndex++;
                break;
            case "fromBucket":
            case "toBucket":
            case "expense":
            case "revenue":
            case "license":
                if(alias.equals("tr")){
                    if(field.equals("license"))sb.append("tr.");
                    sb.append(field + ".id");
                    sb.append(" ").append(operation);
                    if (objectValue != null) addParamOfValue(sb, parameterIndex, ((BaseEntity) objectValue).getId() , parametersMap);
                }
                parameterIndex++;
                break;
            case "missingTaxInvoice":
            case "chequesNotClearedAmount":
                if(alias.equals("tr")){
                    sb.append("tr." + field);
                    sb.append(" ").append(operation);
                    if (objectValue != null){
                        addParamOfValue(sb, parameterIndex, objectValue, parametersMap);
                    }
                }
                parameterIndex++;
                break;
            case "vatType":
                if(alias.equals("tr")){
                    if (operation.contains("<>")) {
                        sb.append(" ( ");
                    }
                    sb.append("tr.vatType");
                    sb.append(" ").append(operation);
                    if (objectValue != null) {
                        sb.append(" :")
                                .append("para")
                                .append(parameterIndex);
                        parametersMap.put("para" + parameterIndex, VatType.valueOf(objectValue.toString()));
                    }

                    if (operation.contains("<>")) {
                        sb.append(" or ")
                            .append("tr.vatType ")
                                .append("is null")
                                .append(" ) ");
                    }
                }
                parameterIndex++;
                break;
            case "date":
                if(alias.equals("tr")){
                    sb.append("tr." + field);
                    sb.append(" ").append(operation);
                    if (objectValue != null) {
                        try {
                            addParamOfValue(sb, parameterIndex, new java.sql.Date(new SimpleDateFormat("E MMM dd HH:mm:ss Z yyyy")
                                    .parse(objectValue.toString()).getTime()) , parametersMap);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                parameterIndex++;
                break;
            case "directDebit.contractPaymentTerm.contract.client.name":
                if(alias.equals("ddf")){
                    sb.append("cl.name");
                    sb.append(" ").append(operation);
                    if (objectValue != null) {
                        sb.append(" :")
                                .append("para")
                                .append(parameterIndex);
                        parametersMap.put("para" + parameterIndex, objectValue);
                    }
                }
                parameterIndex++;
                break;
            case "directDebit.contractPaymentTerm.contract.client.mobileNumber":
                if(alias.equals("ddf")){
                    sb.append("cl.mobileNumber");
                    sb.append(" ").append(operation);
                    if (objectValue != null) addParamOfValue(sb, parameterIndex, objectValue, parametersMap);
                }
                parameterIndex++;
                break;
            case "directDebit.contractPaymentTerm.contract.id":
                if(alias.equals("ddf")){
                    sb.append("c.id");
                    sb.append(" ").append(operation);
                    if (objectValue != null) {
                        addParamOfValue(sb, parameterIndex, objectValue, parametersMap);
                    }
                }
                parameterIndex++;
                break;
            case "directDebit.contractPaymentTerm.bankName":
                if(alias.equals("ddf")){
                    sb.append("cpt.bankName");
                    sb.append(" ").append(operation);
                    if (objectValue != null) addParamOfValue(sb, parameterIndex, objectValue, parametersMap);
                }
                parameterIndex++;
                break;
            case "applicationId":
                if(alias.equals("ddf")){
                    sb.append("ddf.applicationId");
                    sb.append(" ").append(operation);
                    if (objectValue != null) addParamOfValue(sb, parameterIndex, objectValue, parametersMap);
                }
                parameterIndex++;
                break;
            case "rejectionReason":
                if(alias.equals("ddf")){
                    sb.append("ddf.rejectionReason");
                    sb.append(" ").append(operation);
                    if (objectValue != null) addParamOfValue(sb, parameterIndex, objectValue, parametersMap);
                }
                parameterIndex++;
                break;
            case "ddStatus":
                if(alias.equals("ddf")){
                    sb.append("ddf.ddStatus");
                    sb.append(" ").append(operation);
                    if (objectValue != null) {
                        sb.append(" :")
                                .append("para")
                                .append(parameterIndex);
                        parametersMap.put("para" + parameterIndex, DirectDebitStatus.valueOf(objectValue.toString()));
                    }
                }
                parameterIndex++;
                break;
            case "startDate":
                if(alias.equals("ddf")){
                    sb.append("ddf.startDate");
                    sb.append(" ").append(operation);
                    if (objectValue != null) {
                        try {
                            addParamOfValue(sb, parameterIndex, new java.sql.Date(new SimpleDateFormat("E MMM dd HH:mm:ss Z yyyy")
                                    .parse(objectValue.toString()).getTime()) , parametersMap);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                parameterIndex++;
                break;
            case "pnlValueDate":
            case "creationDate":
                if(alias.equals("tr")){
                    sb.append("tr." + field);
                    sb.append(" ").append(operation.equals("=") ? "between" : operation);
                    if (objectValue != null) {
                        try {
                            addParamOfValue(sb, parameterIndex, new SimpleDateFormat("E MMM dd HH:mm:ss Z yyyy")
                                    .parse(objectValue.toString()), parametersMap);

                            if(operation.equals("=")){
                                sb.append(" and ");
                                sb.append(" :")
                                        .append("paraEnd")
                                        .append(parameterIndex);

                                parametersMap.put("paraEnd" + parameterIndex, new LocalDateTime(new SimpleDateFormat("E MMM dd HH:mm:ss Z yyyy")
                                        .parse(objectValue.toString())).plusMinutes(1).toDate());
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                if(alias.equals("ddf")){
                    sb.append("ddf.creationDate");
                    sb.append(" ").append(operation);
                    if (objectValue != null) {
                        try {
                            addParamOfValue(sb, parameterIndex, new SimpleDateFormat("E MMM dd HH:mm:ss Z yyyy")
                                    .parse(objectValue.toString()), parametersMap);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                parameterIndex++;
                break;
            case "expiryDate":
                if(alias.equals("ddf")){
                    sb.append("ddf.expiryDate");
                    sb.append(" ").append(operation);
                    if (objectValue != null) {
                        try {
                            addParamOfValue(sb, parameterIndex, new Date(new SimpleDateFormat("E MMM dd HH:mm:ss Z yyyy")
                                    .parse(objectValue.toString()).getTime()) , parametersMap);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                parameterIndex++;
                break;
            case "notes":
                if(alias.equals("ddf")){
                    sb.append("ddf.notes");
                    sb.append(" ").append(operation);
                    if (objectValue != null) addParamOfValue(sb, parameterIndex, objectValue, parametersMap);
                }
                parameterIndex++;
                break;
            case "vatAmount":
            case "amount":
                if(alias.equals("tr")){
                    sb.append("tr." + field);
                    sb.append(" ").append(operation);
                    if (objectValue != null) addParamOfValue(sb, parameterIndex, objectValue, parametersMap);
                }
                if(alias.equals("ddf")){
                    sb.append("ddf.amount");
                    sb.append(" ").append(operation);
                    if (objectValue != null) addParamOfValue(sb, parameterIndex, objectValue, parametersMap);
                }
                parameterIndex++;
                break;
            case "directDebit.additionalDiscount":
                if(alias.equals("ddf")){
                    sb.append("dd.additionalDiscount");
                    sb.append(" ").append(operation);
                    if (objectValue != null) addParamOfValue(sb, parameterIndex, objectValue, parametersMap);
                }
                parameterIndex++;
                break;
            default:
                boolean isPicklist = fieldType.equals("com.magnamedia.core.entity.PicklistItem");
                sb.append(alias).append(".").append(field).append(isPicklist ?".id " : " ").append(operation);

                if (objectValue != null) addParamOfValue(sb, parameterIndex,
                        isPicklist ? ((PicklistItem)objectValue).getId() : objectValue, parametersMap);
                parameterIndex++;
                break;
        }
        return parameterIndex;
    }

    private void addParamOfValue(StringBuilder sb, int parameterIndex,
                                 Object objectValue, Map<String, Object> parametersMap){
        sb.append(" :")
                .append("para")
                .append(parameterIndex);
        parametersMap.put("para" + parameterIndex, objectValue);
    }

    public Map<String, Object> getQueryTransaction(){
        StringBuilder select = new StringBuilder("select new com.magnamedia.entity.dto.TransactionsSearchDto(" +
                "tr.id as id, tr.pnlValueDate as pnlValueDate, " +
                "fromBucket.id as fromBucketId, fromBucket.name as fromBucketName, fromBucket.code as fromBucketCode, fromBucket.cardNumber as fromBucketcCardNumber, " +
                "revenue.id as revenueId, revenue.name as revenueName, revenue.code as revenueCode, " +
                "expense.id as expenseId, expense.name as expenseName, expense.code as expenseCode, " +
                "toBucket.id as toBucketId, toBucket.name as toBucketName, toBucket.code as toBucketCode, toBucket.cardNumber as toBucketCardNumber, " +
                "tr.description as description, tr.paymentType as paymentType, tr.amount as amount, tr.date as date, tr.creationDate as creationDate, " +
                "tr.previouslyUnknown as previouslyUnknown, tr.transactionType as transactionType, expense.isSecure as isSecure, tr.vatType as vatType, " +
                "tr.vatAmount as vatAmount, license.id as licenseId, license.name as licenseName, license.code as licenseCode, tr.paymentId as paymentId, " +
                "(select max(p.contract.id) from Payment p where p.id = tr.paymentId) as contractId, " +
                "(select max(p.contract.client.id) from Payment p where p.id = tr.paymentId) as clientId, " +
                "fromBucket.isSecure as fromBucketIsSecure, toBucket.isSecure as toBucketIsSecure) ");
        String fromStatement = "from Transaction tr " +
                "left join tr.fromBucket fromBucket " +
                "left join tr.toBucket toBucket " +
                "left join tr.expense expense " +
                "left join tr.revenue revenue " +
                "left join tr.license license ";
        return new HashMap<String, Object>(){{
            put("select", select);
            put("fromStatement", fromStatement);
        }};
    }

    public Map<String, Object> getCountQueryTransaction(Boolean withFiltering){
        StringBuilder countQuery= new StringBuilder("select count(tr.id) ");
        StringBuilder fromStatementCountQuery = new StringBuilder("from Transaction tr ");
        if (withFiltering) fromStatementCountQuery.append("left join tr.fromBucket fromBucket ")
                .append("left join tr.toBucket toBucket ")
                .append("left join tr.expense expense ")
                .append("left join tr.revenue revenue ");

        return new HashMap<String, Object>(){{
            put("countQuery", countQuery);
            put("fromStatementCountQuery", fromStatementCountQuery);
        }};
    }

    public Map<String, Object> getTransactionConditions(Map<String, Object> mfilters, boolean withFiltering, boolean includeMinDate, String source, Integer indexParameter, Map<String, Object> parameters){
        if (!withFiltering && !includeMinDate) return null;

        StringBuilder conditions = new StringBuilder(" where ");
        switch (source){
            case "MainSearch":
                String valueSearch = (String) mfilters.get("valueSearch");
                if (withFiltering) {
                    conditions.append("( ");
                    if (org.apache.commons.lang3.StringUtils.isNumeric(valueSearch)) {
                        conditions.append("tr.id ")
                                .append("=")
                                .append(" :")
                                .append("para")
                                .append(indexParameter)
                                .append(" ");
                        parameters.put("para" + indexParameter++, Long.valueOf(valueSearch));

                        conditions.append(" or ");
                    }
                    conditions.append("fromBucket.name ")
                            .append("like")
                            .append(" :")
                            .append("para")
                            .append(indexParameter)
                            .append(" ");
                    parameters.put("para" + indexParameter++, "%" + valueSearch + "%");

                    conditions.append(" or ");
                    conditions.append("fromBucket.code ")
                            .append("like")
                            .append(" :")
                            .append("para")
                            .append(indexParameter)
                            .append(" ");
                    parameters.put("para" + indexParameter++, "%" + valueSearch + "%");

                    conditions.append(" or ");
                    conditions.append("toBucket.name ")
                            .append("like")
                            .append(" :")
                            .append("para")
                            .append(indexParameter)
                            .append(" ");
                    parameters.put("para" + indexParameter++, "%" + valueSearch + "%");

                    conditions.append(" or ");
                    conditions.append("toBucket.code ")
                            .append("like")
                            .append(" :")
                            .append("para")
                            .append(indexParameter)
                            .append(" ");
                    parameters.put("para" + indexParameter++, "%" + valueSearch + "%");

                    conditions.append(" or ");
                    conditions.append("expense.name ")
                            .append("like")
                            .append(" :")
                            .append("para")
                            .append(indexParameter)
                            .append(" ");
                    parameters.put("para" + indexParameter++, "%" + valueSearch + "%");

                    conditions.append(" or ");
                    conditions.append("expense.code ")
                            .append("like")
                            .append(" :")
                            .append("para")
                            .append(indexParameter)
                            .append(" ");
                    parameters.put("para" + indexParameter++, "%" + valueSearch + "%");

                    conditions.append(" or ");
                    conditions.append("revenue.name ")
                            .append("like")
                            .append(" :")
                            .append("para")
                            .append(indexParameter)
                            .append(" ");
                    parameters.put("para" + indexParameter++, "%" + valueSearch + "%");

                    conditions.append(" or ");
                    conditions.append("revenue.code ")
                            .append("like")
                            .append(" :")
                            .append("para")
                            .append(indexParameter)
                            .append(" ");
                    parameters.put("para" + indexParameter++, "%" + valueSearch + "%");

                    conditions.append(") ");

                }
                break;
            case "AdvanceSearch":
                List<FilterItem> filters = (List<FilterItem>) mfilters.get("valueSearch");
                List<String> conditionWithoutValue = Arrays.asList("is null", "is not null");
                for (int i = 0; i < filters.size(); i++, indexParameter++) {
                    FilterItem f = filters.get(i);
                    boolean withValue = !conditionWithoutValue.contains(f.getOperation().toLowerCase());
                    if((f.getProperty().equals("amount") || f.getProperty().equals("vatAmount")) && f.getValue() == null) continue;
                    if(withValue) {
                        parameters.put("para" + indexParameter,  f.getProperty().equals("vatType") ?
                                VatType.valueOf((String) f.getValue()) : f.getProperty().equals("amount") || f.getProperty().equals("vatAmount") ?
                                Double.valueOf(f.getValue().toString()) : f.getProperty().equals("creationDate") || f.getProperty().equals("pnlValueDate") ?
                                LocalDateTime.parse(f.getValue().toString()).toDate() : f.getProperty().equals("date") ?
                                java.sql.Date.valueOf(f.getValue().toString()) :
                                f.getProperty().equals("license.id") || f.getProperty().equals("fromBucket.id") || f.getProperty().equals("toBucket.id") ||
                                        f.getProperty().equals("expense.id") || f.getProperty().equals("revenue.id") ? Long.valueOf(f.getValue().toString())
                                        : f.getProperty().equals("paymentType") ? PaymentMethod.valueOf((String) f.getValue()): f.getValue());
                        if(f.getOperation().contains("like")) parameters.put("para" + indexParameter, "%" + parameters.get("para" + indexParameter) + "%");
                    }

                    boolean fromJoinTable = f.getProperty().contains("fromBucket.") || f.getProperty().contains("toBucket.") ||
                            f.getProperty().contains("expense.") || f.getProperty().contains("revenue.") ;

                    conditions.append(!fromJoinTable ? "tr." : f.getAlternatives() == null ? "" : "(")
                            .append(f.getProperty())
                            .append(" ")
                            .append(f.getProperty().equals("creationDate") && f.getOperation().equals("=") ?
                                    "between" : f.getOperation());
                    if(withValue) conditions.append(" :")
                            .append("para")
                            .append(indexParameter)
                            .append(" ");

                    if(fromJoinTable && f.getAlternatives() != null){
                        for(String altProperty: f.getAlternatives()) {
                            conditions.append( " or ")
                                    .append(altProperty)
                                    .append(" ")
                                    .append(f.getOperation());
                            if(withValue) conditions.append(" :")
                                    .append("para")
                                    .append(indexParameter);
                        }
                        conditions.append(") ");
                    }

                    if(f.getOperation().equals("between") && (f.getProperty().equals("date") ||
                            f.getProperty().equals("pnlValueDate") || f.getProperty().equals("creationDate"))){
                        conditions.append( " and ")
                                .append(" :")
                                .append("paraEndDate")
                                .append(indexParameter);
                        parameters.put("paraEndDate" + indexParameter, f.getProperty().equals("date") ?
                                java.sql.Date.valueOf(f.getSecondValue().toString()) :
                                f.getProperty().equals("creationDate") || f.getProperty().equals("pnlValueDate") ?
                                    LocalDateTime.parse(f.getSecondValue().toString()).plusDays(1).minusMillis(1).toDate() :  "");
                    }

                    if (f.getProperty().equals("creationDate") && f.getOperation().equals("=")) {
                        parameters.put("paraEndDate" + indexParameter, LocalDateTime.parse(f.getValue().toString()).plusDays(1).minusMillis(1).toDate());
                        conditions.append(" and :paraEndDate").append(indexParameter);
                    }
                    conditions.append(i != filters.size()-1 ? " and " : "");
                }
                break;
        }
        //Jira ACC-2545
        if(includeMinDate){
            if(!conditions.toString().equals(" where ")) conditions.append(" and ");
            conditions.append("tr.date ")
                    .append(">=")
                    .append(" :")
                    .append("para")
                    .append(indexParameter)
                    .append(" ");
            parameters.put("para" + indexParameter++, java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));
        }

        Integer finalIndexParameter = indexParameter;
        return new HashMap<String, Object>(){{
            put("conditions", conditions);
            put("indexParameter", finalIndexParameter);
        }};
    }

    public AccountingPage manageTransactionsMainSearch(String valueSearch, Pageable pageable) {
        boolean withFiltering = valueSearch != null && !valueSearch.isEmpty();
        Integer indexParameter = 1;
        Map<String, Object> parameters = new HashMap<>();
        Map<String, Object> mQuery = getQueryTransaction();
        Map<String, Object> mCountQuery = getCountQueryTransaction(withFiltering);
        Map<String, Object> mConditions = getTransactionConditions(new HashMap<String, Object>(){{put("valueSearch", valueSearch);}}, withFiltering, true, "MainSearch", indexParameter, parameters);

        //Query part
        StringBuilder select = (StringBuilder) mQuery.get("select");
        String fromStatement = (String) mQuery.get("fromStatement");

        // CountQuery part
        StringBuilder countQuery = (StringBuilder) mCountQuery.get("countQuery");
        StringBuilder fromStatementCountQuery = (StringBuilder) mCountQuery.get("fromStatementCountQuery");

        // conditions part
        StringBuilder conditions = mConditions != null ? (StringBuilder) mConditions.get("conditions") : null;
        indexParameter = mConditions.containsKey("indexParameter") ? (Integer) mConditions.get("indexParameter") : indexParameter;

        //Sorting part
        String query = (pageable.getSort() == null || Iterables.isEmpty(pageable.getSort())) ?
                select + fromStatement + conditions + " order by case when tr.creationDate is null then 1 else 0 end, tr.creationDate DESC" :
                sortDirectQueryByPageable(pageable, select + fromStatement + conditions, "tr");

        SelectQuery<TransactionsSearchDto> q = new SelectQuery<>(
                query, String.valueOf(countQuery) + fromStatementCountQuery + conditions, TransactionsSearchDto.class, parameters);
        Page<TransactionsSearchDto> p = q.execute(pageable);

        // fill isDescriptionSecured
        p = fillIsDescriptionSecured(p);

        // fill attachments
        p = fillAttachments(p, "Transaction");

        //  Sum Query part
        Map<String,Double> mSum = getBalanceSumAndInBalanceSumAndOutBalanceSum(conditions, countQuery, fromStatementCountQuery, parameters);

        PageImpl s = (PageImpl)p;
        AccountingPage accountingPageResult =
                new TransactionPage(
                        s.getContent(), pageable, s.getTotalElements(),
                        mSum.get("balanceSum"), mSum.get("inBalanceSum"), mSum.get("outBalanceSum"));
        return accountingPageResult;
    }

    public AccountingPage manageTransactionsAdvanceSearch(List<FilterItem> filters, Pageable pageable) {
        boolean withFiltering =  filters != null && !filters.isEmpty();
        Integer indexParameter = 1;
        Map<String, Object> parameters = new HashMap<>();
        Map<String, Object> mQuery = getQueryTransaction();
        Map<String, Object> mCountQuery = getCountQueryTransaction(withFiltering);
        Map<String, Object> mConditions = getTransactionConditions(new HashMap<String, Object>(){{put("valueSearch", filters);}}, withFiltering, true, "AdvanceSearch", indexParameter, parameters);

        //Query part
        StringBuilder select = (StringBuilder) mQuery.get("select");
        String fromStatement = (String) mQuery.get("fromStatement");

        // CountQuery part
        StringBuilder countQuery = (StringBuilder) mCountQuery.get("countQuery");
        StringBuilder fromStatementCountQuery = (StringBuilder) mCountQuery.get("fromStatementCountQuery");

        // conditions part
        StringBuilder conditions = mConditions.containsKey("conditions") ? (StringBuilder) mConditions.get("conditions") : null;
        indexParameter = mConditions.containsKey("indexParameter") ? (Integer) mConditions.get("indexParameter") : indexParameter;

        //Sorting part
        String query = (pageable.getSort() == null || Iterables.isEmpty(pageable.getSort())) ?
                select + fromStatement + conditions + " order by case when tr.creationDate is null then 1 else 0 end, tr.creationDate DESC" :
                sortDirectQueryByPageable(pageable, select + fromStatement + conditions, "tr");

        SelectQuery<TransactionsSearchDto> q = new SelectQuery<>(
                query, String.valueOf(countQuery) + fromStatementCountQuery + conditions, TransactionsSearchDto.class, parameters);
        Page<TransactionsSearchDto> p = q.execute(pageable);

        // fill isDescriptionSecured
        p = fillIsDescriptionSecured(p);

        // fill attachments
        p = fillAttachments(p, "Transaction");

        //  Sum Query part
        Map<String,Double> mSum = getBalanceSumAndInBalanceSumAndOutBalanceSum(conditions, countQuery, fromStatementCountQuery, parameters);

        PageImpl s = (PageImpl)p;
        AccountingPage accountingPageResult =
                new TransactionPage(
                        s.getContent(), pageable, s.getTotalElements(),
                        mSum.get("balanceSum"), mSum.get("inBalanceSum"), mSum.get("outBalanceSum"));
        return accountingPageResult;
    }

    public AccountingPage manageTransactionsAdvanceSearch2(Pageable pageable) {
        boolean withFiltering =  CurrentRequest.getSearchFilter() != null && !CurrentRequest.getSearchFilter().isEmpty();
        Integer indexParameter = 1;
        Map<String, Object> parameters = new HashMap<>();
        Map<String, Object> mQuery = getQueryTransaction();
        Map<String, Object> mCountQuery = getCountQueryTransaction(withFiltering);

        //Query part
        StringBuilder select = (StringBuilder) mQuery.get("select");
        String fromStatement = (String) mQuery.get("fromStatement");

        // CountQuery part
        StringBuilder countQuery = (StringBuilder) mCountQuery.get("countQuery");
        StringBuilder fromStatementCountQuery = (StringBuilder) mCountQuery.get("fromStatementCountQuery");

        // conditions part
        StringBuilder conditions = new StringBuilder(getConditionsFromSelectFilter(CurrentRequest.getSearchFilter(), parameters, "tr"));
        conditions.append(!withFiltering ? " where " : " and ");
        conditions .append("tr.date ")
                .append(">=")
                .append(" :")
                .append("paraDate")
                .append(indexParameter)
                .append(" ");
        parameters.put("paraDate" + indexParameter++, java.sql.Date.valueOf(TransactionsController.TRANSACTION_MINIMUM_DATE));

        //Sorting part
        String query = (pageable.getSort() == null || Iterables.isEmpty(pageable.getSort())) ?
                select + fromStatement + conditions + " order by case when tr.creationDate is null then 1 else 0 end, tr.creationDate DESC" :
                sortDirectQueryByPageable(pageable, select + fromStatement + conditions, "tr");

        SelectQuery<TransactionsSearchDto> q = new SelectQuery<>(
                query, String.valueOf(countQuery) + fromStatementCountQuery + conditions, TransactionsSearchDto.class, parameters);
        Page<TransactionsSearchDto> p = q.execute(pageable);

        // fill isDescriptionSecured
        p = fillIsDescriptionSecured(p);

        // fill attachments
        p = fillAttachments(p, "Transaction");

        //  Sum Query part
        Map<String,Double> mSum = getBalanceSumAndInBalanceSumAndOutBalanceSum(conditions, countQuery, fromStatementCountQuery, parameters);

        PageImpl s = (PageImpl)p;
        AccountingPage accountingPageResult =
                new TransactionPage(
                        s.getContent(), pageable, s.getTotalElements(),
                        mSum.get("balanceSum"), mSum.get("inBalanceSum"), mSum.get("outBalanceSum"));
        return accountingPageResult;
    }

    public Page<TransactionsSearchDto> fillAttachments(Page<TransactionsSearchDto> p, String owner){
        List<Map> attachments = getAttachmentsWithBasicInfo(p.getContent().stream()
                .map(recored -> recored.getId()).collect(Collectors.toList()), owner);
        p.getContent().forEach(recored -> {
            List<Map> attachmentList = attachments.stream()
                    .filter(a -> recored.getId().equals(a.get("ownerId"))).collect(Collectors.toList());
            attachmentList.forEach(a -> a.remove("ownerId"));
            recored.setAttachments(attachmentList);
        });
        return p;
    }

    public Page<TransactionsSearchDto> fillIsDescriptionSecured(Page<TransactionsSearchDto> p){
        p.forEach(tr -> {
            if(tr.getIsDescriptionSecured()) tr.setDescription("***DESCRIPTION SECURED***");
        });
        return p;
    }

    public Map<String, Double> getBalanceSumAndInBalanceSumAndOutBalanceSum(StringBuilder conditions, StringBuilder countQuery, StringBuilder fromStatementCountQuery, Map<String, Object > parameters){
        String query = "select new map (sum(tr.amount) as balanceSum, " +
                "sum(case when tr.vatType = 'IN' then tr.vatAmount else 0 end) as inBalanceSum, " +
                "sum(case when tr.vatType = 'OUT' then tr.vatAmount else 0 end) as outBalanceSum) " +
                fromStatementCountQuery + conditions;

        Map result = new SelectQuery<>(
                query, String.valueOf(countQuery) + fromStatementCountQuery + conditions, Map.class, parameters)
                .execute().get(0);

        if(result!= null && result.get("balanceSum") == null) result.put("balanceSum", 0D);
        if(result!= null && result.get("inBalanceSum") == null) result.put("inBalanceSum", 0D);
        if(result!= null && result.get("outBalanceSum") == null) result.put("outBalanceSum", 0D);

        //Jirra ACC-1147
        //Jira ACC-7479
        result.put("balanceSum", (double) Math.round((Double) result.get("balanceSum") * 100) / 100);
        result.put("inBalanceSum", Math.floor((Double) result.get("inBalanceSum") * 100) / 100);
        result.put("outBalanceSum", Math.floor((Double) result.get("outBalanceSum") * 100) / 100);

        return result;
    }

    public static String getDateChangedToReceivedViaWrapperStrWithAlias(String toDoAlias) {
        return getDateChangedToReceivedViaWrapperStr(toDoAlias) + " as dateChangedToReceived, ";
    }

    public static String getDateChangedToReceivedViaWrapperStr(String toDoAlias) {

        return " (case " +
                    "when " + toDoAlias + ".cardPaymentReceivedDate is not null " +
                            "then " + toDoAlias + ".cardPaymentReceivedDate " +
                        "else " + toDoAlias + ".creationDate " +
                    "end) ";
    }

    public static Page<Map> getDateChangedToReceivedViaResult(Page<Map> result) {
        result.getContent().forEach(m -> {
            if (!m.containsKey("dateChangedToReceived") || m.get("dateChangedToReceived") == null) return;
            m.put("paymentDate", m.get("dateChangedToReceived"));
            m.remove("dateChangedToReceived");
        });

        return result;
    }

    public Page<Map> getTodosForMatchOnlineStatement(Double amount, Pageable pageable) {
        String fromStatement = " from ContractPaymentWrapper cpw " +
                "join cpw.contractPaymentConfirmationToDo todo " +
                "where todo.paymentMethod = 'Card' and todo.confirmed = false and todo.showOnERP = true " +
                "group by todo.id " +
                "having sum(cpw.amount) = :amount";

        String query = "select new map(" +
                "todo.id as todoId, todo.creationDate as paymentDate, " +
                "todo.cleanAuthorizationCode as authorizationCode, todo.transferReference as transferReference, " +
                getDateChangedToReceivedViaWrapperStrWithAlias("todo") +
                "sum(cpw.amount) as amount, " +
                "(select max(cl.name) from ContractPaymentConfirmationToDo cpc join cpc.contractPaymentTerm cpt join cpt.contract c join c.client cl where cpc.id = todo.id) as clientName )" + fromStatement;

        String fromStatementCountQuery = " from ContractPaymentConfirmationToDo  t " +
                "where t.id in ( select todo.id from ContractPaymentWrapper cpw join cpw.contractPaymentConfirmationToDo todo " +
                "where todo.paymentMethod = 'Card' and todo.confirmed = false and todo.showOnERP = true group by todo.id having sum(cpw.amount) = :amount)";
        String countQuery = "Select count(t.id) " + fromStatementCountQuery;

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("amount", amount);

        SelectQuery<Map> q = new SelectQuery<>(query, countQuery, Map.class, parameters);

        return getDateChangedToReceivedViaResult(q.execute(pageable));
    }

    public List<Map> creditCardStatementGetErpPayments(List<String> l) {

        String condition = " c.transferReference in :ids ";;

        String query = "select new map( " +
                getDateChangedToReceivedViaWrapperStrWithAlias("c") + " c as todo )" +
                "from ContractPaymentConfirmationToDo c " +
                "where " + condition + " and c.payingOnline = true " +
                    "and c.paymentMethod = 'Card' and c.confirmed = false and c.showOnERP = true and c.source <> 'CLIENT_REFUND'";

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("ids", l);

        SelectQuery<Map> q = new SelectQuery<>(query, "", Map.class, parameters);

        return q.execute();
    }

    public AccountingPage expenseSearch(
            Pageable pageable, String search,
            boolean withDeleted, String activeFilter,
            Boolean onlyParent, List<FilterItem> filters) {

        String select = "select e ";
        StringBuilder from = new StringBuilder("from Expense e ");
        StringBuilder join = new StringBuilder();

        String countQuery = "select count(e.id) " + from;

        StringBuilder conditions = new StringBuilder();
        Map<String, Object> parameters = new HashMap<>();

        // add search
        int index = 1;
        if ((filters != null && !filters.isEmpty()) || (search != null && !search.isEmpty()) ||
                !withDeleted || onlyParent || activeFilter != null) {
            conditions.append("where ");
        }

        if (search != null && !search.isEmpty()){
            String value = "like :para" + index + " ";
            parameters.put("para" + index++, "%" + search + "%");

            // conditions query
            conditions.append("(");
            conditions.append("e.name ")
                    .append(value);
            conditions.append(" or ")
                    .append("e.code ")
                    .append(value);
            conditions.append(" or ")
                    .append("e.caption ")
                    .append(value);
            conditions.append(")");
        }

        if (!withDeleted) {
            if(!conditions.toString().equals("where ")) conditions.append(" and ");
            conditions.append("e.deleted ")
                    .append("=")
                    .append(" :")
                    .append("para")
                    .append(index);

            parameters.put("para" + index++, false);
        }

        if (activeFilter != null) { // else filter on activeFilter field
            Boolean value = null;
            if (activeFilter.equals("enabled"))
                value = false;
            else if (activeFilter.equals("disabled"))
                value = true;

            if(value != null){
                if(!conditions.toString().equals("where ")) conditions.append(" and ");
                conditions.append("e.disabled ")
                        .append("=")
                        .append(" :")
                        .append("para")
                        .append(index);

                parameters.put("para" + index++, value);
            }
        }

        if (filters != null && !filters.isEmpty()) {
            if(!conditions.toString().equals("where ")) conditions.append(" and ");
            for (int i = 0; i < filters.size(); i++) {
                FilterItem f = filters.get(i);

                if(f.getProperty().equals("approveHolder.fullName")) {
                    join.append(" left join User ue on ue = e.approveHolder ");
                    conditions.append("ue.fullName ");
                } else if(f.getProperty().equals("requestedFrom.code")) {
                    join.append(" left join PicklistItem pe on pe = e.requestedFrom ");
                    conditions.append("pe.code ");
                } else {
                    conditions.append("e.")
                            .append(f.getProperty())
                            .append(" ");
                }

                conditions.append(f.getOperation())
                        .append(" :")
                        .append("para")
                        .append(index)
                        .append(" ");

                parameters.put("para" + index++, f.getProperty().equals("approveHolderType") ?
                        ExpenseApproveHolderType.valueOf(f.getValue().toString()) : f.getValue());
                conditions.append(i != (filters.size() - 1) ? " and " : "");
            }
        }

        // sub query for children
        if(!conditions.toString().isEmpty()){
            String subQuerySelect = "select 1 from Expense children " +
                    (join.toString().isEmpty() ? "" :
                            join.toString()
                            .replace("e.", "children.")
                            .replace(" pe ", " pchildren ")
                            .replace(" ue ", " uchildren "));

            String subQueryConditions = conditions.toString()
                    .replaceFirst("where ", "where children.parent = e " +
                            (conditions.toString().equals("where ") ? "" : " and "))
                    .replace("e.", "children.");
            String subQuery = subQuerySelect + subQueryConditions + " and children.parent = e ";

            conditions.append(" or ")
                    .append("exists ( ")
                    .append(subQuery)
                    .append(") ");

            if (onlyParent) {
                conditions.append(") "); // for close '(' after  'e.parent is null'
            }
        }

        if (onlyParent) {
            conditions = new StringBuilder(conditions.toString()
                    .replaceFirst("where ", "where e.parent is null " +
                            (conditions.toString().equals("where ") ? "" : " and ( ")));
        }

        from.append(join);
        //Sorting
        String query;
        if (pageable.getSort() != null && !pageable.getSort().isEmpty()) {
            query = sortDirectQueryByPageable(pageable, select + from + conditions, "e");
        } else {
            StringBuilder sortBy = new StringBuilder();
            sortBy.append(" order by ");
            sortBy.append("e.code ");
            query = select + from + conditions + sortBy.toString();
        }

        PageImpl page = (PageImpl) new SelectQuery<>(
                query, countQuery + join + conditions, Expense.class, parameters)
                .execute(pageable);

        ProjectionFactory projectionFactory = Setup.getApplicationContext().getBean(ProjectionFactory.class);
        List<Long> parentNotMatched = new ArrayList<>();
        Page projectedResult = page.map(e -> {

            List<Expense> processedSearchFilterChildren = new ArrayList<>();

            // remove childes Not Matched with

            for (Expense ex : ((Expense)e).getChildren()) {
                if (!withDeleted && ex.getDeleted()) continue;
                ex.setMatchedSearchFilter(checkExpenseIfValid(ex, search, filters, activeFilter, withDeleted));
                processedSearchFilterChildren.add(ex);
            }

            ((Expense)e).setChildren(processedSearchFilterChildren);

            // find Parent Not Matched to hidden before response
            if (!checkExpenseIfValid((Expense)e, search, filters, activeFilter, withDeleted)) {
                parentNotMatched.add(((Expense)e).getId());
            }

            return projectionFactory.createProjection(ExpenseProjection.class, e);
        });

        return new AccountingPage((List) projectedResult.getContent().stream()
                .map(e -> {
                    if(!parentNotMatched.contains(((ExpenseProjection)e).getId())) return e;
                    return projectionFactory.createProjection(ExpenseProjection.class, e);
                }).collect(Collectors.toList()),
                pageable, page.getTotalElements(), 0.0);
    }

    public boolean checkExpenseIfValid(Expense ex, String search, List<FilterItem> filters, String activeFilter, boolean withDeleted) {
        if (!withDeleted && ex.getDeleted()) return false;

        if (!activeFilter.equals("all") && (activeFilter.equals("disabled") == !ex.getDisabled() ||
                activeFilter.equals("enabled") == ex.getDisabled())) return false;

        if (search != null && !search.isEmpty() &&
                ((ex.getCode() == null || !org.apache.commons.lang3.StringUtils.containsIgnoreCase(ex.getCode(), search)) &&
                        (ex.getName() == null || !org.apache.commons.lang3.StringUtils.containsIgnoreCase(ex.getName(), search)) &&
                        (ex.getCaption() == null || !org.apache.commons.lang3.StringUtils.containsIgnoreCase(ex.getCaption(), search)))) return false;

        if (filters != null && !filters.isEmpty()) {
            for (FilterItem f : filters) {
                switch (f.getProperty()) {
                    case "requestedFrom.code":
                        if (ex.getRequestedFrom() == null || !f.getValue().toString().equalsIgnoreCase(ex.getRequestedFrom().getCode())) return false;
                        break;
                    case "approveHolder.fullName":
                        if ((ex.getApproveHolder() == null && !f.getValue().toString().isEmpty()) ||
                                !f.getValue().toString().equalsIgnoreCase(ex.getApproveHolder().getFullName() == null ?
                                null : "%" + ex.getApproveHolder().getFullName() + "%")) return false;
                        break;
                    case "approveHolderEmail":
                        if ((ex.getApproveHolderEmail() == null && !f.getValue().toString().isEmpty()) ||
                                !f.getValue().toString().equalsIgnoreCase(ex.getApproveHolderEmail() == null ?
                                null : "%" + ex.getApproveHolderEmail() + "%")) return false;
                        break;
                    case "approveHolderType":
                        if ((ex.getApproveHolderType() == null && !f.getValue().toString().isEmpty()) ||
                                !ex.getApproveHolderType().equals(ExpenseApproveHolderType.valueOf(f.getValue().toString()))) return false;
                        break;
                    case "isLimitedCOO":
                        if (!ex.getIsLimitedCOO().equals(f.getValue())) return false;
                        break;
                }
            }
        }
        return true;
    }

    public static boolean existsEntity(
            Class<?> t,
            String conditions,
            Object[] array) {

        String q = "select count(e.id) > 0 from :entity e where ".replace(":entity", t.getName()) + conditions;

        Map<String, Object> m = new HashMap<>();
        for (int i = 0; i < array.length; i++) {
            m.put("p" + i, array[i]);
        }

        return new SelectQuery<>(q, "", Boolean.class, m)
                .execute().get(0);
    }

    public static <T> List<T> selectFields(Class<?> t, String conditions, Object[] parameters, List<String> fields, Class<?> returnType) {

        String selectStatement = "select ";

        // fields should be passed with alias like : e.id, e.name
        if (Map.class.isAssignableFrom(returnType)) {
            String fieldMappings = fields.stream()
                    .map(field -> field + " as " + field.split("\\.")[1])
                    .collect(Collectors.joining(", "));
            selectStatement += "new map(" + fieldMappings + ")";
        } else if (fields.size() == 1) {
            selectStatement += fields.get(0);
        }

        String q = selectStatement + " from :entity e where ".replace(":entity", t.getName()) + conditions;

        Map<String, Object> m = new HashMap<>();
        for (int i = 0; i < parameters.length; i++) {
            m.put("p" + i, parameters[i]);
        }

        return (List<T>) new SelectQuery<>(q, "", returnType, m).execute();
    }

    // ACC-8212
    public List<ContractPayment> getAllDdPaymentsRelatedFlows(ContractPaymentConfirmationToDo.Source source, Boolean isRejectionDueBouncing, ContractPaymentTerm cpt) {

        String select = "select cp from ContractPayment cp ";
        String joins = " join cp.directDebit directDebit ";

        Map<String, Object> parameters = new HashMap<>();
        StringBuilder conditions = new StringBuilder(" where ");

        conditions.append(" directDebit.contractPaymentTerm.id ")
                .append("= ")
                .append(":cptId");
        parameters.put("cptId", cpt.getId());

        // Filter by Status
        String filterDdb = " directDebit.category = :categoryDdb ";
        parameters.put("categoryDdb", DirectDebitCategory.B);

        String filterDda = " directDebit.category = :categoryDda ";
        parameters.put("categoryDda", DirectDebitCategory.A);

        String filterStatus = source.equals(DD_REJECTED) && isRejectionDueBouncing ?
                "(directDebit.status in (:statuses) or directDebit.MStatus in (:statuses))" :
                "directDebit.status in (:statuses)";

        filterDdb = " (" + filterDdb + " and " + filterStatus + ") ";  // DDB status
        filterDda = " (" + filterDda + " and directDebit.MStatus in (:statuses)) ";  // DDA M_Status

        conditions.append(" and ")
                .append("(")
                    .append(filterDdb)
                .append(" or ")
                    .append(filterDda)
                .append(")");

        switch (source) {
            case INCOMPLETE_FLOW_MISSING_BANK_INFO:
            case INCOMPLETE_FLOW_DATA_ENTRY_REJECTION:
                parameters.put("statuses", DirectDebitStatus.IN_COMPLETE);
                break;
            case DD_REJECTED:
                parameters.put("statuses", DirectDebitService.activeWithoutConfirmedStatuses);

                // Add Joins
                joins += " join directDebit." +
                            (isRejectionDueBouncing ?
                                "directDebitBouncingRejectionToDo" :
                                "directDebitRejectionToDo") +
                        " rejectionToDo ";

                // Check flow not completed and not stopped
                conditions.append(" and ");
                conditions.append("rejectionToDo.completed")
                        .append(" = ")
                        .append(":completed");
                parameters.put("completed", false);

                conditions.append(" and ");
                conditions.append("rejectionToDo.stopped")
                        .append(" = ")
                        .append(":stopped");
                parameters.put("stopped", false);
                break;
            default:
                return new ArrayList<>();
        }

        // Filter by Date
        String toDate = " cp.date <= :date1 ";
        parameters.put("date1", Stream.of(
                        new LocalDate().dayOfMonth().withMaximumValue().toDate(),
                        new LocalDate().plusDays(CreditCardOfferProperties.getX_DaysBeforeDdStartDate()).toDate())
                .max(java.util.Date::compareTo).get());

        // ProRatedPeriod
        if (cpt.getContract().getIsProRated() && new LocalDate().toString("yyyy-MM")
                .equals(new LocalDate(cpt.getContract().getStartOfContract()).toString("yyyy-MM"))) {

            toDate = " (" + toDate;
            toDate += " or (cp.date <= :date2 and cp.paymentType.code = :paymentType)";
            toDate += ") " ;
            parameters.put("date2", new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate());
            parameters.put("paymentType", "monthly_payment");
        }

        // Date
        conditions.append(" and ")
                .append(toDate);

        // paymentMethod
        conditions.append(" and ")
                .append("cp.paymentMethod")
                .append(" = ")
                .append(":paymentMethod");
        parameters.put("paymentMethod", PaymentMethod.DIRECT_DEBIT);

        String sort = " order by cp.creationDate desc ";
        return new SelectQuery<>(select + joins + conditions + sort, "", ContractPayment.class, parameters)
                .execute();
    }

    // ACC-8212
    public List<Object[]> getAllUpcomingContractPaymentsByRelatedFlow(
            ContractPaymentConfirmationToDo.Source relatedFlow, Boolean isRejectionDueBouncing,
            Long cptId, java.util.Date date, List<DirectDebitStatus> statuses) {

        String joinRejection = "", conditionRejection = "";
        String filterStatus = "dd.status in (:statuses)";
        if (relatedFlow.equals(DD_REJECTED)) {
            joinRejection = " join " + (isRejectionDueBouncing ? " dd.directDebitBouncingRejectionToDo" : " dd.directDebitRejectionToDo") + " rejectionToDo ";
            conditionRejection = "rejectionToDo.completed = false and rejectionToDo.stopped = false and " ;
            if (isRejectionDueBouncing) {
                filterStatus = "(dd.status in (:statuses) or dd.MStatus in (:statuses))";
            }
        }

        String query = "select new Map (cp.id as id, cp.date as date) " +
                "from ContractPayment cp " +
                "join cp.directDebit dd " +
                "join dd.contractPaymentTerm cpt " +
                joinRejection +
                "where cpt.id = :cptId and cp.date >= :date and " +
                    "((dd.category = 'A' and dd.MStatus in (:statuses)) or (dd.category = 'B' and " + filterStatus + ")) and " +
                     conditionRejection +
                    "not exists( select 1 from Payment p " +
                    "where p.contract = cpt.contract and p.amountOfPayment = cp.amount and " +
                    "p.typeOfPayment = cp.paymentType and p.status = 'RECEIVED' and p.dateOfPayment = cp.date) " +
                "order by cp.date asc";

        Map<String, Object > parameters = new HashMap<>();
        parameters.put("cptId", cptId);
        parameters.put("date", date);
        parameters.put("statuses", statuses);

        return new SelectQuery<>(query, "", Map.class, parameters)
                .execute()
                .stream()
                .map(m -> new Object[] {m.get("id"), m.get("date")})
                .collect(Collectors.toList());
    }

    // ACC-8212
    public List<DirectDebit> getAllDdByRelatedFlow(
            ContractPaymentConfirmationToDo.Source relatedFlow, Boolean isRejectionDueBouncing,
            Long cptId, List<DirectDebitStatus> statuses) {

        String joinRejection = "", conditionRejection = "";
        String filterStatus = "dd.status in (:statuses)";
        if (relatedFlow.equals(DD_REJECTED)) {
            joinRejection = " join " + (isRejectionDueBouncing ? " dd.directDebitBouncingRejectionToDo " : " dd.directDebitRejectionToDo ") + " rejectionToDo ";
            conditionRejection = "rejectionToDo.completed = false and rejectionToDo.stopped = false and " ;
            if (isRejectionDueBouncing) {
                filterStatus = "(dd.status in (:statuses) or dd.MStatus in (:statuses))";
            }
        }

        String query = "select dd.id from DirectDebit dd " +
                        joinRejection +
                        "where dd.contractPaymentTerm.id = :cptId and " + conditionRejection +
                            "((dd.category = 'A' and dd.MStatus in (:statuses)) or (dd.category = 'B' and " + filterStatus + "))";


        Map<String, Object > parameters = new HashMap<>();
        parameters.put("cptId", cptId);
        parameters.put("statuses", statuses);

        List<Long> ddIds = new SelectQuery<>(query, "", Long.class, parameters).execute();
        return !ddIds.isEmpty() ?
                Setup.getRepository(DirectDebitRepository.class).findAll(ddIds) : new ArrayList<>();
    }
}
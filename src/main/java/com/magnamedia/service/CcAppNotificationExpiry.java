package com.magnamedia.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.PushNotificationRepository;
import com.magnamedia.core.type.AppActionVisibility;
import com.magnamedia.entity.*;
import com.magnamedia.module.type.TaxiWorkOrderStatus;
import com.magnamedia.repository.ClientRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.HousemaidAttendanceLogRepository;
import com.magnamedia.repository.HousemaidRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class CcAppNotificationExpiry {
    protected static final Logger logger = Logger.getLogger(CcAppNotificationExpiry.class.getName());

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;

    @Autowired
    private HousemaidRepository housemaidRepository;

    @Autowired
    private HousemaidAttendanceLogRepository housemaidAttendanceLogRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private PushNotificationRepository pushNotificationRepository;

    public AppActionVisibility getClientDirectDebitRejected33Link2VisibilityApi(Long entityId) {
        logger.log(Level.INFO, "Start");

        if (entityId != null) {
            PushNotification pushNotification = pushNotificationRepository.findOne(entityId);
            if (pushNotification != null) {
                logger.log(Level.INFO, "start pushNotification id " + pushNotification.getId());
                if (pushNotification.getRecepientType().equals("Client") && pushNotification.getRecepientId() != null) {
                    Client client = clientRepository.findOne(Long.valueOf(pushNotification.getRecepientId()));
                    if (client != null) {
                        logger.log(Level.INFO, "client id " + client.getId());
                        Map<String, Object> signatureType = directDebitSignatureService.getLastSignatureType(
                                client, client.getEid(), false, false);
                        if (((Boolean) signatureType.get("useApprovedSignature")
                                || (Boolean) signatureType.get("useNonRejectedSignature")))
                            return AppActionVisibility.Disabled;
                    }
                }
            }
        }
        logger.log(Level.INFO, "End");
        return AppActionVisibility.Visible;
    }

    public AppActionVisibility getClientDirectDebitRejected33Action1VisibilityApi(Long entityId) {
        logger.log(Level.INFO, "Start");
        if (entityId != null) {
            PushNotification pushNotification = pushNotificationRepository.findOne(entityId);

            if (pushNotification != null) {
                logger.log(Level.INFO, "pushNotification id " + pushNotification.getId());
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    TypeReference<Map<String, Map<String, Object>>> typeRef =
                            new TypeReference<Map<String, Map<String, Object>>>() {
                            };
                    TypeReference<Map<String, String>> typeRef2 =
                            new TypeReference<Map<String, String>>() {
                            };
                    Map<String, Object> m = mapper.readValue(pushNotification.getContext(), typeRef).get("@Action1@");

                    if (m != null) {
                        Map<String, String> appRouteArguments = (Map<String, String>) m.get("appRouteArguments");
                        Map<String, String> argsMap = mapper.readValue(appRouteArguments.get("notificationArguments"), typeRef2);

                        if (argsMap.get("housemaidId") != null) {
                            Long housemaidId = Long.valueOf(argsMap.get("housemaidId"));
                            logger.log(Level.INFO, "housemaid id " + housemaidId);

                            Housemaid housemaid = housemaidRepository.findOne(housemaidId);
                            Boolean maidPresent = housemaidAttendanceLogRepository
                                    .existsHousemaidAttendanceLogByHousemaidAndAttendanceStatusAndCreationDateGreaterThan(
                                            housemaid, HousemaidAttendanceLog.AttendanceStatus.PRESENT, pushNotification.getCreationDate());
                            logger.log(Level.INFO, "maidPresent " + maidPresent);
                            if (maidPresent)
                                return AppActionVisibility.Disabled;
                        }

                        if (argsMap.get("contractId") != null) {
                            Long contractId = Long.valueOf(argsMap.get("contractId"));
                            logger.log(Level.INFO, "contract id " + contractId);
                            Contract contract = contractRepository.findOne(contractId);
                            Long clientId = contract.getClient().getId();
                            logger.log(Level.INFO, "client id " + clientId);

                            SelectQuery<LogisticsWorkOrder> query = new SelectQuery(LogisticsWorkOrder.class);
                            query.filterBy("client.id", "=", clientId);
                            query.filterBy("taxiWorkOrderStatus", "=", TaxiWorkOrderStatus.PENDING);
                            query.filterBy("creationDate", ">", pushNotification.getCreationDate());
                            logger.log(Level.INFO, "End found an uber");
                            return query.execute().isEmpty() ? AppActionVisibility.Visible : AppActionVisibility.Disabled;
                        }
                        logger.log(Level.INFO, "No housemaid or contract found");
                    }
                } catch (Exception ex) {
                    logger.log(Level.INFO, "Error while disabling notification: " + pushNotification.getId(), ex);
                }
            }
        }
        logger.log(Level.INFO, "End");
        return AppActionVisibility.Visible;
    }
}

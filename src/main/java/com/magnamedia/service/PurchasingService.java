package com.magnamedia.service;

import com.magnamedia.entity.Item;
import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.entity.PurchaseOrder;
import com.magnamedia.repository.ItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <PERSON> (Feb 15, 2021)
 */
@Service
public class PurchasingService {
    @Autowired
    ItemRepository itemRepository;

    public void updateLastAndCheapestSupplierInItems(PurchaseOrder order) {
        for (PurchaseItem purchaseItem : order.getPurchaseItems()) {
            Item item = purchaseItem.getItem();
            if (item.getBestPrice() == null || item.getBestPrice() >= purchaseItem.getUnitPrice().doubleValue()) {
                item.setBestPrice(purchaseItem.getUnitPrice().doubleValue());
                item.setBestSupplier(purchaseItem.getCurrentSupplier());
            }

            item.setLastPrice(purchaseItem.getUnitPrice().doubleValue());
            item.setLastSupplier(purchaseItem.getCurrentSupplier());

            itemRepository.save(item);
        }
    }
}

package com.magnamedia.service;

import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.Template;
import com.magnamedia.entity.*;
import com.magnamedia.entity.ccapp.CcAppTracking;
import com.magnamedia.entity.ccapp.CcAppTracking.CcAppAction;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.DDUtils;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

//CMA-2728
@Service
public class CCAppService {
    protected static final Logger logger = Logger.getLogger(CCAppService.class.getName());

    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private DirectDebitService directDebitService;
    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private CCAppContentService ccAppContentService;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;

    public enum CCAppPaymentStatusSection {
        BOUNCED_PAYMENTS,
        REJECTED_DD,
        PENDING_DD,
        INCOMPLETE_DD,
        APPROVED_PAYMENTS,
        PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS,
        CLIENT_PAYING_VIA_CREDIT_CARD,
        PENDING_REFUND_PROOF_TRANSFER_UPLOADED,
        PENDING_REFUND_CONDITIONAL,
        PENDING_REFUND_NON_CONDITIONAL,
        ONE_MONTH_AGREEMENT,
        INITIAL_PAYMENT_BY_ALTERNATIVE_METHOD,
        ONLINE_CREDIT_CARD_PAYMENT_REMINDERS,

        // Deprecated
        BOUNCED_PAYMENTS_PAID,
        CREDIT_CARD_PAYMENT,
        PENDING_REFUND,
    }

    @Deprecated
    public Map<String, Object> ccPaymentsSectionData(Contract contract, boolean isNewVersion) {
        Map<String, Object> body = new HashMap<>();
        if (contract != null) {
            logger.log(Level.INFO, "Contract id " + contract.getId());
            body.put("bankDetails", directDebitService.getDDBankDetails(contract));
            body.put("contractId", contract.getId());


            ContractPaymentTerm activeCPT = contract.getActiveContractPaymentTerm();

            if (contract.isPayingViaCreditCard()) {
                 // CMA-3318 ACC-5687
                DateTime d = Setup.getApplicationContext().getBean(PaymentService.class)
                        .getLastReceivedMonthlyPaymentDate(contract);

                // ACC-5509
                Setup.getApplicationContext().getBean(ClientPayingViaCreditCardService.class)
                        .createTodoIfNotExists(activeCPT, d == null ?
                                new LocalDate(contract.getStartOfContract()) :
                                new LocalDate(d).plusMonths(1));
                body.put("sectionKey", CCAppPaymentStatusSection.CLIENT_PAYING_VIA_CREDIT_CARD.toString());

                return body;
            }

            List<Payment> payments = paymentRepository.findByContractOrderByDateOfPaymentDesc(contract);
            logger.log(Level.INFO, "payments: " + payments.size());

            List<DirectDebit> directDebits = directDebitRepository.findByContractPaymentTerm(activeCPT);

            if (!payments.isEmpty()) {
                //BOUNCED_PAYMENTS Section
                Payment bouncedPayment = payments.stream().filter(payment -> payment.getStatus().equals(PaymentStatus.BOUNCED)
                        && !payment.isReplaced()).findFirst().orElse(null);

                logger.log(Level.INFO, "bouncedPayment: " + bouncedPayment);
                Template bouncedTemplate = ccAppContentService.getFirstMessageByFlowType(
                    DDMessagingType.BouncedPayment, contract);

                if (bouncedPayment != null && bouncedTemplate != null) {
                    logger.log(Level.INFO, "bouncedPayment id : {0}", bouncedPayment.getId());
                    if (isNewVersion) {
                        body.put("bouncedPaymentId", bouncedPayment.getId());
                        body.put("amount", bouncedPayment.getAmountOfPayment());
                    }

                    Payment bouncedPaymentReplacement = payments.stream().filter(p ->
                            p.getDateOfPayment().equals(bouncedPayment.getDateOfPayment())
                            && p.getAmountOfPayment().equals(bouncedPayment.getAmountOfPayment())
                            && p.getTypeOfPayment().equals(bouncedPayment.getTypeOfPayment())
                                    && p.getCreationDate().after(bouncedPayment.getCreationDate())
                                    && p.getStatus().equals(PaymentStatus.RECEIVED)).findFirst().orElse(null);
                    logger.log(Level.INFO, "bouncedPaymentReplacement id : {0}", bouncedPaymentReplacement == null ? "Null" : bouncedPaymentReplacement.getId());

                    boolean newDdCoverBouncedPayment = directDebits.stream().anyMatch(directDebit ->
                            directDebit.getPaymentType() != null
                            && directDebit.getPaymentType().getCode().equals("monthly_payment")
                            && directDebit.getCreationDate().after(bouncedPayment.getDateOfBouncing())
                            && DDUtils.doesDDCoverDate(directDebit, bouncedPayment.getDateOfPayment()));

                    if (bouncedPaymentReplacement != null || newDdCoverBouncedPayment
                            || !bouncedTemplate.getName().toLowerCase().contains("bouncedpayment")) {

                        body.put("sectionKey", CCAppPaymentStatusSection.BOUNCED_PAYMENTS_PAID.toString());
                        return body;
                    }

                    body.put("sectionKey", CCAppPaymentStatusSection.BOUNCED_PAYMENTS.toString());
                    return body;
                }
            }

            //REJECTED_DD Section
            boolean isThereRejectedDD = directDebits.stream()
                    .anyMatch(dd -> {
                        return (dd.getStatus().equals(DirectDebitStatus.REJECTED) ||
                                dd.getMStatus().equals(DirectDebitStatus.REJECTED)) &&
                                ((dd.getDirectDebitRejectionToDo() != null
                                 && !dd.getDirectDebitRejectionToDo().isStopped()
                                 && !dd.getDirectDebitRejectionToDo().isCompleted()) ||
                                (dd.getDirectDebitBouncingRejectionToDo()!= null
                                 && !dd.getDirectDebitBouncingRejectionToDo().isStopped()
                                 && !dd.getDirectDebitBouncingRejectionToDo().isCompleted()));
                    });

            logger.log(Level.INFO, "isThereRejectedDD: " + isThereRejectedDD);
            if (isThereRejectedDD && ccAppContentService.getFirstMessageByFlowType(
                    DDMessagingType.DirectDebitRejected, contract) != null) {

                try {
                    body.put("rejectionInfo", getBankAccountInfo(activeCPT));
                    body.put("sectionKey", CCAppPaymentStatusSection.REJECTED_DD.toString());
                    return body;
                } catch (Exception e){
                    e.printStackTrace();
                    body.put("error", "An error happened in rejected dd");
                }
            }

            //PENDING_DD Section
            boolean existPendingDdForFollowingPayment = directDebitRepository
                    .existPendingDdForFollowingPayment(contract, Arrays.asList(
                            DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY));
            if (existPendingDdForFollowingPayment) {
                body.put("sectionKey", CCAppPaymentStatusSection.PENDING_DD.toString());
                return body;
            }

            //CREDIT_CARD_PAYMENT Section
            logger.log(Level.INFO, "Checking CREDIT_CARD_PAYMENT Section");
            if (Setup.getApplicationContext().getBean(FlowProcessorService.class)
                    .existsRunningFlow(contract, FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED) &&
                    ccAppContentService.getFirstMessageByFlowType(
                            DDMessagingType.ClientPaidCashAndNoSignatureProvided, contract) != null) {

                ContractPaymentConfirmationToDo toDo = contractPaymentConfirmationToDoRepository
                            .findFirstByContractPaymentTerm_ContractAndSourceAndShowOnERPFalseAndDisabledFalseOrderByCreationDateDesc(
                                activeCPT.getContract(), ContractPaymentConfirmationToDo.Source.AFTER_CASH_FLOW);

                if (toDo != null) {
                    logger.log(Level.INFO, "toDo id " + toDo.getId());
                    body.put("amount", toDo.getTotalAmount());
                    body.put("todoUuid", toDo.getUuid());
                }
                body.put("sectionKey", CCAppPaymentStatusSection.CREDIT_CARD_PAYMENT.toString());
                return body;
            }

            //INCOMPLETE_DD Section
            boolean isThereIncompleteDD = directDebits.stream()
                    .anyMatch(dd -> dd.getStatus().equals(DirectDebitStatus.IN_COMPLETE) ||
                            dd.getMStatus().equals(DirectDebitStatus.IN_COMPLETE));

            logger.log(Level.INFO, "isThereIncompleteDD: " + isThereIncompleteDD);
            if (isThereIncompleteDD && ccAppContentService.getFirstMessageByFlowType(
                    DDMessagingType.IncompleteDDRejectedByDataEntry, contract) != null) {

                body.put("sectionKey", CCAppPaymentStatusSection.INCOMPLETE_DD.toString());
                return body;
            }

            if (!payments.isEmpty()) {
                //CREDIT_CARD_PAYMENT Section
                boolean existsDDF = directDebitFileRepository.existsByDirectDebit_ContractPaymentTerm(activeCPT);
                if(!existsDDF) {
                    Payment creditCardPayment = payments.stream()
                            .filter(payment -> payment.getMethodOfPayment().equals(PaymentMethod.CARD)
                                    && new LocalDate(payment.getDateOfPayment()).getMonthOfYear() == new LocalDate(contract.getStartOfContract()).getMonthOfYear()
                                    && new LocalDate(payment.getDateOfPayment()).getYear() == new LocalDate(contract.getStartOfContract()).getYear()).findFirst().orElse(null);

                    if (creditCardPayment != null && ccAppContentService.getFirstMessageByFlowType(
                            DDMessagingType.ClientPaidCashAndNoSignatureProvided, contract) != null) {

                        body.put("sectionKey", CCAppPaymentStatusSection.CREDIT_CARD_PAYMENT.toString());
                        return body;
                    }
                }

                boolean hasApprovedDD = directDebits.stream()
                        .anyMatch(dd -> (dd.getStatus().equals(DirectDebitStatus.CONFIRMED) ||
                                dd.getMStatus().equals(DirectDebitStatus.CONFIRMED)));

                if (!hasApprovedDD) return body;

                if (isNewVersion) {
                    return getApprovedDdSection(contract, body);
                } else {
                    //PENDING_REFUND Section
                    boolean isTherePendingRefund = clientRefundTodoRepository
                            .existsByContractAndStatus(contract, ClientRefundStatus.PENDING);
                    logger.log(Level.INFO, "isTherePendingRefundPayment: " + isTherePendingRefund);
                    if (isTherePendingRefund) {
                        body.put("sectionKey", CCAppPaymentStatusSection.PENDING_REFUND.toString());
                        return body;
                    }

                    //APPROVED_PAYMENTS Section
                    logger.log(Level.INFO, "hasApprovedDD: " + hasApprovedDD);
                    body.put("sectionKey", CCAppPaymentStatusSection.APPROVED_PAYMENTS.toString());
                    return body;

                }

            }
        } else
            body.put("error", "An error happened contract id is wrong");

        // Default no rules apply
        return body;
    }

    public Map<String, Object> ccPaymentsSectionDataAcc7142(Contract contract) {
        Map<String, Object> body = new HashMap<>();
        if (contract == null) {
            body.put("error", "An error happened contract id is wrong");
            return body;
        }

        logger.info("Contract id: " + contract.getId());
        body.put("bankDetails", directDebitService.getDDBankDetails(contract));
        body.put("contractId", contract.getId());

        Payment p = paymentRepository.findFirstByContractAndStatusAndReplacedFalseOrderByDateOfPaymentAsc(contract, PaymentStatus.BOUNCED);
        //BOUNCED_PAYMENTS Section
        if (p != null) {
            logger.info("bouncedPayment id: " + p.getId());

            body.put("sectionKey", CCAppPaymentStatusSection.BOUNCED_PAYMENTS.toString());
            body.put("bouncedPayment", p);
            return body;
        }

        ContractPaymentTerm activeCPT = contract.getActiveContractPaymentTerm();

        List<DirectDebit> directDebits = directDebitRepository.findByContractPaymentTerm(activeCPT);

        //REJECTED_DD Section
        List<DirectDebit> rejectedDds = directDebits.stream()
                .filter(dd -> (dd.getStatus().equals(DirectDebitStatus.REJECTED) ||
                        dd.getMStatus().equals(DirectDebitStatus.REJECTED)) &&
                        ((dd.getDirectDebitRejectionToDo() != null &&
                                !dd.getDirectDebitRejectionToDo().isStopped() &&
                                !dd.getDirectDebitRejectionToDo().isCompleted()) ||
                                (dd.getDirectDebitBouncingRejectionToDo() !=  null &&
                                        !dd.getDirectDebitBouncingRejectionToDo().isStopped() &&
                                        !dd.getDirectDebitBouncingRejectionToDo().isCompleted())))
                .collect(Collectors.toList());

        Map<String, Object> m = getRejectionFLowSectionInfo(rejectedDds, contract);
        if (m != null) {
            body.putAll(m);
            return m;
        }

        //INCOMPLETE_DD Section
        DirectDebit incompleteDd = directDebits.stream()
                .filter(dd -> dd.getStatus().equals(DirectDebitStatus.IN_COMPLETE) ||
                        dd.getMStatus().equals(DirectDebitStatus.IN_COMPLETE))
                .findFirst()
                .orElse(null);

        if (incompleteDd != null) {
            logger.log(Level.INFO, "incompleteDd id: " + incompleteDd);
            body.put("incompleteDd", incompleteDd);
            body.put("sectionKey", CCAppPaymentStatusSection.INCOMPLETE_DD.toString());
            return body;
        }

        // Reminder Required flow
        FlowProcessorEntity reminderFlowRequired = flowProcessorService.getRunningFlow(
                FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS,
                FlowSubEventConfig.FlowSubEventName.PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS,
                contract, null);
        if (reminderFlowRequired != null && reminderFlowRequired.getCurrentSubEvent().getName()
                .equals(FlowSubEventConfig.FlowSubEventName.PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS)) {

            logger.info("requiredReminderFlow id " + reminderFlowRequired.getId());

            body.put("reminderFlow", reminderFlowRequired);
            body.put("sectionKey", CCAppPaymentStatusSection.PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS.toString());
            return body;
        }

        //CLIENT_PAYING_VIA_CREDIT_CARD CMA-3318
        if (activeCPT.getContract().isPayingViaCreditCard() && !contract.isOneMonthAgreement()) {
            logger.info("Paying via credit card");

            body.put("sectionKey", CCAppPaymentStatusSection.CLIENT_PAYING_VIA_CREDIT_CARD.toString());
            return body;
        }

        //IPAM Section
        FlowProcessorEntity ipamFlow = flowProcessorService.getFirstRunningFlow(contract, FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);
        if (ipamFlow != null && !ccAppContentService.clientProvidedNewDetailsAfterDate(contract, ipamFlow.getCreationDate())) {
            logger.info("IPAM id: " + ipamFlow.getId());

            ContractPaymentConfirmationToDo toDo = contractPaymentConfirmationToDoRepository
                    .findFirstByContractPaymentTerm_ContractAndSourceAndShowOnERPFalseAndDisabledFalseOrderByCreationDateDesc(
                            activeCPT.getContract(), ContractPaymentConfirmationToDo.Source.AFTER_CASH_FLOW);

            if (toDo == null) {
                try {
                    toDo = Setup.getApplicationContext()
                            .getBean(AfterCashFlowService.class)
                            .addConfirmationTodoForPayment(contract);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (toDo != null) {
                logger.info("toDo id " + toDo.getId());
                if (ipamFlow.getContractPaymentConfirmationToDo() == null ||
                        !toDo.getId().equals(ipamFlow.getContractPaymentConfirmationToDo().getId())) {
                    ipamFlow.setContractPaymentConfirmationToDo(toDo);
                    Setup.getRepository(FlowProcessorEntityRepository.class).save(ipamFlow);
                }
                body.put("amount", toDo.getTotalAmount());
                body.put("todoUuid", toDo.getUuid());
            }

            body.put("ipamFlow", ipamFlow);
            body.put("sectionKey", CCAppPaymentStatusSection.INITIAL_PAYMENT_BY_ALTERNATIVE_METHOD.toString());
            return body;
        }

        //OMA ACC-5687
        if (contract.isOneMonthAgreement() && contract.isPayingViaCreditCard()) {
            Date flowStartDate = clientPayingViaCreditCardService.getChangeToPayingViaCcDate(contract);
            if (flowStartDate != null && !ccAppContentService.clientProvidedNewDetailsAfterDate(contract, flowStartDate)) {
                logger.info("one month agreement");
                body.put("flowStartDate", flowStartDate);
                body.put("sectionKey", CCAppPaymentStatusSection.ONE_MONTH_AGREEMENT.toString());
                return body;
            }
        }

        // Reminder flow
        FlowProcessorEntity reminderFlow = flowProcessorService.getRunningFlow(
                FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS,
                FlowSubEventConfig.FlowSubEventName.PENDING_PAYMENT,
                contract, null);
        if (reminderFlow != null) {
            logger.info("reminderFlow id " + reminderFlow.getId());

            body.put("reminderFlow", reminderFlow);
            body.put("sectionKey", CCAppPaymentStatusSection.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS.toString());
            return body;
        }

        //APPROVED_PAYMENTS Section
        boolean hasApprovedDD = directDebits.stream()
                .anyMatch(dd -> ((dd.getCategory().equals(DirectDebitCategory.B) && dd.getStatus().equals(DirectDebitStatus.CONFIRMED)) ||
                        (dd.getCategory().equals(DirectDebitCategory.A) && dd.getMStatus().equals(DirectDebitStatus.CONFIRMED))));
        if (hasApprovedDD) {
            logger.info("hasApprovedDD");
            body.put("sectionKey", CCAppPaymentStatusSection.APPROVED_PAYMENTS.toString());
            return body;
        }


        //PENDING_DD Section
        boolean hasPendingDD = directDebits.stream()
                .anyMatch(dd -> ((dd.getCategory().equals(DirectDebitCategory.B) &&
                        Arrays.asList(DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY).contains(dd.getStatus())) ||
                        (dd.getCategory().equals(DirectDebitCategory.A) &&
                                Arrays.asList(DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY).contains(dd.getMStatus()))));

        boolean contractHasOpenDdc = Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .contractHasOpenMainDdcToDo(contract.getId());
        if (hasPendingDD || contractHasOpenDdc) {
            logger.info("hasPendingDD: " + hasPendingDD + "; contractHasOpenDdc:" + contractHasOpenDdc);
            body.put("sectionKey", CCAppPaymentStatusSection.PENDING_DD.toString());
            return body;
        }

        // Default no rules apply
        return body;
    }

    public Map<String, Object> getRejectionFLowSectionInfo(List<DirectDebit> rejectedDds, Contract contract) {
        if (rejectedDds.isEmpty()) {
            rejectedDds = directDebitRepository.findDirectDebitByRejectionFlowAndCptInactive(contract.getId());
        }
        DirectDebit rejectedDd = rejectedDds.stream()
                .sorted(Comparator.comparingInt(this::sortRejectedDds))
                .findFirst()
                .orElse(null);

        if (rejectedDd == null) return null;
        logger.log(Level.INFO, "rejectedDd id: " + rejectedDd);
        Map<String, Object> m = new HashMap<>();
        m.put("sectionKey", CCAppPaymentStatusSection.REJECTED_DD.toString());
        m.put("rejectedDd", rejectedDd);
        return m;
    }

    private int sortRejectedDds(DirectDebit d) {

        return (d.getDirectDebitRejectionToDo() != null && d.getDirectDebitRejectionToDo().getLeadingRejectionFlow()) ||
                (d.getDirectDebitBouncingRejectionToDo() != null && d.getDirectDebitBouncingRejectionToDo().getLeadingRejectionFlow()) ?
                1 : 2;
    }

    public Map<String, Object> getPayingViaCcSectionInfo(ContractPaymentTerm cpt) {
        Map<String, Object> m = new HashMap<>();
        DateTime d = Setup.getApplicationContext().getBean(PaymentService.class)
                .getLastReceivedMonthlyPaymentDate(cpt.getContract());
        logger.info("Paying via credit card");
        // ACC-5509
        ContractPaymentConfirmationToDo todo = Setup.getApplicationContext().getBean(ClientPayingViaCreditCardService.class)
                .createTodoIfNotExists(cpt, d == null ?
                        new LocalDate(cpt.getContract().getStartOfContract()) :
                        new LocalDate(d).plusMonths(1));
        m.put("sectionKey", CCAppPaymentStatusSection.CLIENT_PAYING_VIA_CREDIT_CARD.toString());
        m.put("todoUuid", todo.getUuid());

        return m;
    }

    public Map<String, Object> getOneMonthAgreementSectionInfo(ContractPaymentTerm cpt) {
        Date flowStartDate = clientPayingViaCreditCardService.getChangeToPayingViaCcDate(cpt.getContract());
        if (flowStartDate == null || ccAppContentService.clientProvidedNewDetailsAfterDate(cpt.getContract(), flowStartDate)) {
            return null;
        }
        Map<String, Object> m = new HashMap<>();
        logger.info("one month agreement");
        ContractPaymentConfirmationToDo toDo = Setup.getApplicationContext()
                .getBean(OneMonthAgreementFlowService.class)
                .createTodoIfNotExists(cpt);
        if (toDo != null) {
            logger.info("toDo id " + toDo.getId());
            m.put("amount", toDo.getTotalAmount());
            m.put("todoUuid", toDo.getUuid());
        }
        m.put("flowStartDate", flowStartDate);
        m.put("sectionKey", CCAppPaymentStatusSection.ONE_MONTH_AGREEMENT.toString());

        return m;
    }

    @Deprecated
    private Map<String, Object> getApprovedDdSection(
            Contract contract,
            Map<String, Object> body) {

        logger.log(Level.INFO, "hasApprovedDD: true");
        ClientRefundToDo todo = clientRefundTodoRepository
                .findFirstByContractAndStatus(contract, ClientRefundStatus.PENDING);

        //APPROVED_PAYMENTS Section
        if (todo == null) {
            // screen #1
            body.put("sectionKey", CCAppPaymentStatusSection.APPROVED_PAYMENTS.toString());
            return body;
        }

        logger.log(Level.INFO, "todo id: {0}", todo.getId());

        if (new DateTime().isBefore(new DateTime(todo.getCreationDate()).plusDays(3))) // screen #4
            body.put("sectionKey", CCAppPaymentStatusSection.PENDING_REFUND_PROOF_TRANSFER_UPLOADED.toString());
        else if (todo.isConditionalRefund()) // screen #3
            body.put("sectionKey", CCAppPaymentStatusSection.PENDING_REFUND_CONDITIONAL.toString());
        else // screen #2
            body.put("sectionKey", CCAppPaymentStatusSection.PENDING_REFUND_NON_CONDITIONAL.toString());

        return body;
    }

    public List<?> ccPaymentsHistory(Contract contract) {
        if (contract != null) {
            logger.log(Level.INFO, "Contract id " + contract.getId());
            List<String> allowedPaymentCodes = Arrays.asList("PCR_without_Vat", "accommodation_fee", "bounced_check_fee",
                "CC_to_MV_contract", "deposit", "discount", "payment_for_vacation_not_taken",
                "vat_only", "matching_fee");

            Set<String> s = new HashSet<>();

            return paymentRepository.findByContractAndStatusCCAppPaymentsHistory(contract.getId())
                    .stream()
                    .filter(v -> {
                        String k = v[0] + "_" + v[3] + "_" + v[1];
                        return !s.contains(k);
                    }).map(v -> {
                        Map<String, Object> m = new HashMap<>();
                        m.put("dateOfPayment", v[0]);
                        m.put("amount", v[1]);
                        m.put("typeOfPayment", v[2]);
                        m.put("typeCode", v[3]);
                        m.put("cptId", v[4]);
                        m.put("taxInvoice", v[5]);
                        m.put("taxInvoiceDate", v[6]);
                        m.put("hasTaxInvoice", v[5] != null ? true : false);
                        String k = v[0] + "_" + v[3] + "_" + v[1];
                        s.add(k);

                        if (!allowedPaymentCodes.contains((String) v[3]))
                            m.put("typeOfPayment", "");

                        if(v[3] != null && v[4] != null &&
                                ((String) v[3]).equals("upgrading_nationality")) {
                            
                            List<Map<String, Object>> nationalities = (List<Map<String, Object>>)
                                Setup.getRepository(ContractPaymentRepository.class)
                                    .getSwitchingOldNewMaids(Long.parseLong(v[4].toString()));
                            if(!nationalities.isEmpty()) {
                                m.put("typeOfPayment", "Difference between " + nationalities.get(0).get("oldMaid") + 
                                        " and " + nationalities.get(0).get("newMaid") + " rates");
                            }
                        }

                        return m;
                    })
                    .collect(Collectors.toList());
        }
    
        return new ArrayList<>();
    }

    @Deprecated
    private Map getBankAccountInfo(ContractPaymentTerm activeCPT) {
        Map<String, Object> bankInfo = new HashMap();
        
        Attachment eidAttachment = activeCPT.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID);
        Attachment ibanAttachment = activeCPT.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN);
        Attachment accountNameAttachment = activeCPT.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME);
        
        if (eidAttachment != null) {
            bankInfo.put("eidAttachment", eidAttachment.getUuid());
        }
        
        if (ibanAttachment != null) {
            bankInfo.put("ibanAttachment", ibanAttachment.getUuid());
        }
        
        if (accountNameAttachment != null) {
            bankInfo.put("accountNameAttachment", accountNameAttachment.getUuid());
        }
        
        return bankInfo;
    }
    
    public void insertTrackingLog(String setContractUuid, CcAppAction action) {
        CcAppTracking tracking = new CcAppTracking();
        tracking.setContractUuid(setContractUuid);
        tracking.setCcAction(action);
        
        Setup.getRepository(CcAppTrackingRepository.class).save(tracking);
    }
}

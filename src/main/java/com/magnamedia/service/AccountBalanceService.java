package com.magnamedia.service;

import com.magnamedia.controller.TransactionsController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Parameter;
import com.magnamedia.core.helper.Aggregate;
import com.magnamedia.core.helper.AggregateQuery;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.ParameterRepository;
import com.magnamedia.entity.*;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.AccountBalanceRepository;
import com.magnamedia.repository.BucketRepository;
import com.magnamedia.repository.ExpenseRepository;
import com.magnamedia.repository.RevenueRepository;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Oct 14, 2020
 *         Jirra ACC-2522
 */

@Service
public class AccountBalanceService {

    private static final Logger logger = Logger.getLogger(AccountBalanceService.class.getName());
    private static final String prefix = "MMM ";

    @Autowired
    private TransactionsController transactionController;
    @Autowired
    private AccountBalanceRepository accountBalanceRepository;
    @Autowired
    private BucketRepository bucketRepository;
    @Autowired
    private RevenueRepository revenueRepository;
    @Autowired
    private ExpenseRepository expenseRepository;
    @Autowired
    private ExpensePaymentService expensePaymentService;

    public Map getBalance(AccountBalance accountBalance) {
        Bucket bucket = null;
        Expense expense = null;
        Revenue revenue = null;

        if (accountBalance.getBucket() != null && accountBalance.getBucket().getCode() != null &&
                !accountBalance.getBucket().getCode().isEmpty()) {

            bucket = bucketRepository.findByCode(accountBalance.getBucket().getCode());
            accountBalance.setBucket(bucket);
        }

        if (accountBalance.getRevenue() != null && accountBalance.getRevenue().getCode() != null &&
                !accountBalance.getRevenue().getCode().isEmpty()) {

            revenue = revenueRepository.findByCode(accountBalance.getRevenue().getCode());
            accountBalance.setRevenue(revenue);
        }

        if (accountBalance.getExpense() != null && accountBalance.getExpense().getCode() != null &&
                !accountBalance.getExpense().getCode().isEmpty()) {

            expense = expenseRepository.findByCodeAndDeletedFalse(accountBalance.getExpense().getCode());
            accountBalance.setExpense(expense);
        }

        Date fromDate = accountBalance.getFromDate();
        Date toDate = accountBalance.getToDate();
        List<FilterItem> selectFilters = accountBalance.getSelectFilters();

        Map result = new HashMap();
        Date beforeOneDay = new Date(new DateTime(fromDate).minusDays(1).toDate().getTime());
        Double initBalance = bucket != null ?
                getBucketBalanceAmount(bucket, beforeOneDay, selectFilters) :
                getRevenueOrExpenseBalanceAmount(revenue, expense, beforeOneDay, selectFilters);

        List<FilterItem> mainRelationFilters = new ArrayList();
        List<FilterItem> dateFilters = new ArrayList();

        if (bucket != null) {
            mainRelationFilters.add(new FilterItem("fromBucket.code",
                    Arrays.asList("toBucket.code"), "=", bucket.getCode()));
        }
        if (revenue != null) {
            mainRelationFilters.add(new FilterItem(
                    "revenue.code", "=", revenue.getCode()));
        }
        if (expense != null) {
            mainRelationFilters.add(new FilterItem(
                    "expense.code", "=", expense.getCode()));
        }

        Double totalBalance = initBalance;

        if (fromDate != null) dateFilters.add(new FilterItem("date", ">=", fromDate));
        if (toDate != null) dateFilters.add(new FilterItem("date", "<=", toDate));

        List<FilterItem> totalFilters = Stream.concat(
                        selectFilters.stream(),
                        Stream.concat(mainRelationFilters.stream(), dateFilters.stream())
                                .collect(Collectors.toList()).stream())
                .collect(Collectors.toList());

        Map transactionSearchResult = transactionController.advanceSearch(null,
                Sort.by(Sort.Direction.ASC, "date", "id"),
                bucket, false, totalFilters, false);

        List<Transaction> transactions = ((List<Transaction>) transactionSearchResult.get("transactions"));
        Double transactionSum = (Double) transactionSearchResult.get("transactionsSum");

        if (bucket != null) {
            Double changeInBalance = (Double) transactionSearchResult.get("balanceSum");
            if(changeInBalance != 0) totalBalance += changeInBalance;

            initBalance -= bucket.getAuthorizedBalance();
            totalBalance -= bucket.getAuthorizedBalance();
        }

        result.put("initBalance", initBalance);
        result.put("totalBalance", totalBalance);
        result.put("transactions", transactions);
        result.put("transactionSum", transactionSum);
        return result;
    }

    public Double getBucketBalanceAmount(
            Bucket bucket,
            java.util.Date toDate,
            List<FilterItem> selectFilters) {

        return getBucketBalanceAmount(bucket, new Date(toDate.getTime()), selectFilters) -
                bucket.getAuthorizedBalance();
    }

    public Double getBucketBalanceAmount_old(Bucket bucket, Date toDate, List<FilterItem> selectFilters) {
        if (bucket == null) return 0.0;

        if (selectFilters == null) {
            selectFilters = new ArrayList();
        }

        Double balance = 0.0; //bucket.getInitialBalance() != null ? bucket.getInitialBalance() : 0.0;
        AccountBalance accountBalance = getRelevantBalance(bucket, toDate, selectFilters);

        Map transactionSearchResult;

        List<FilterItem> dateFilters = new ArrayList();
        dateFilters.add(new FilterItem("date", "<=", toDate));

        boolean calculationCompleted = false;
        if (accountBalance != null) {
            balance = accountBalance.getActualValue() != null ? accountBalance.getActualValue() : 0.0;

            Date from = accountBalance.getToDate();

            if (from.equals(toDate) || from.after(toDate)) {
                calculationCompleted = true;
            } else {
                dateFilters.add(new FilterItem("date", ">", from));
            }
        }


        if (!calculationCompleted) {
            List<FilterItem> filterItems = Stream.concat(selectFilters.stream(), dateFilters.stream()).collect(Collectors.toList());

            filterItems.add(new FilterItem("fromBucket.code", Arrays.asList("toBucket.code"), "=", bucket.getCode()));

            transactionSearchResult = transactionController.advanceSearch(null, bucket, false, filterItems, false);

            if (transactionSearchResult != null) {
                balance += (Double) transactionSearchResult.get("balanceSum");
            }

            persistNewAccountBalance(bucket, toDate, balance, selectFilters);
        }

        return balance;
    }

    public Double getBucketBalanceAmount(Bucket bucket, Date toDate, List<FilterItem> selectFilters) {
        if (bucket == null) return 0.0;

        if (selectFilters == null) {
            selectFilters = new ArrayList<>();
        }

        Double balance = 0.0;

        List<FilterItem> dateFilters = new ArrayList<>();
        dateFilters.add(new FilterItem("date", "<=", toDate));

        List<FilterItem> filterItems = Stream.concat(selectFilters.stream(), dateFilters.stream()).collect(Collectors.toList());

        filterItems.add(new FilterItem("fromBucket.code", Arrays.asList("toBucket.code"), "=", bucket.getCode()));

        Map transactionSearchResult = transactionController.advanceSearch(null, bucket, false, filterItems, false);

        if (transactionSearchResult != null && transactionSearchResult.containsKey("balanceSum")) {
            balance += (Double) transactionSearchResult.get("balanceSum");
        }

        return balance;
    }

    public Double addAccountBalance(Bucket bucket) {
        List<FilterItem> selectFilters = new ArrayList();
        java.sql.Date toDate = new java.sql.Date(new java.util.Date().getTime());

        AccountBalance accountBalance = getRelevantBalance(bucket, toDate, selectFilters);
        Double prevBalance = accountBalance != null ? accountBalance.getActualValue() : 0.0;

        logger.log(Level.INFO, "toDate: {0}; last log: {1}", new Object[] {
                new LocalDate(toDate).toString("yyyy-MM-dd"), accountBalance == null ?
                "null" : accountBalance.getId()});

        List<FilterItem> dateFilters = new ArrayList();
        dateFilters.add(new FilterItem("date", "<=", toDate));

        if (accountBalance != null) {
            dateFilters.add(new FilterItem("lastModificationDate", ">",
                    accountBalance.getCreationDate()));
        }

        Double changeInBalance = getBucketTransactionsSum(bucket, Stream.concat(
                selectFilters.stream(), dateFilters.stream()).collect(Collectors.toList()));
        logger.log(Level.INFO, "Bucket balance update; ID: {0}, old: {1}, change: {2}",
                new Object[] { bucket.getId(), prevBalance, changeInBalance });

        if(changeInBalance != 0) {
            persistNewAccountBalance(bucket, toDate, prevBalance + changeInBalance, selectFilters);
        }

        if(bucket.getBalance() != (prevBalance + changeInBalance)) {
            bucket.setBalance(prevBalance + changeInBalance);
            bucketRepository.save(bucket);
        }

        return bucket.getBalance();
    }

    private double getBucketTransactionsSum(Bucket bucket, List<FilterItem> filters) {
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            logger.log(Level.INFO, "field: {0}; operation: {1}; value: {2}",
                    new Object[] { filter.getProperty(), filter.getOperation(),
                            filter.getValue() });
            selectFilter = selectFilter.and(filter.getSelectFilter(Transaction.class));
        }

        SelectQuery<Transaction> inQuery = new SelectQuery<>(Transaction.class);
        inQuery.filterBy(new SelectFilter(selectFilter));
        inQuery.filterBy("toBucket", "=", bucket);

        SelectQuery<Transaction> outQuery = new SelectQuery<>(Transaction.class);
        outQuery.filterBy(new SelectFilter(selectFilter));
        outQuery.filterBy("fromBucket", "=", bucket);

        AggregateQuery inAggQuery = new AggregateQuery(inQuery, Aggregate.Sum, "amount");
        AggregateQuery outAggQuery = new AggregateQuery(outQuery, Aggregate.Sum, "amount");

        Double credit = inAggQuery.execute().doubleValue();
        Double debit = outAggQuery.execute().doubleValue();

        logger.log(Level.INFO, "credit: {0}; debit: {1}", new Object[] { credit, debit });
        return credit - debit;
    }

    public Double getRevenueOrExpenseBalanceAmount(Revenue revenue, Expense expense, Date toDate, List<FilterItem> filters) {
        Double balance = 0.0;
        if (filters == null) filters = new ArrayList();
        List<FilterItem> mainRelationFilters = new ArrayList();

        if (revenue != null) {
            mainRelationFilters.add(new FilterItem(
                    "revenue.code", "=", revenue.getCode()));
        }

        if (expense != null) {
            mainRelationFilters.add(new FilterItem(
                    "expense.code", "=", expense.getCode()));
        }

        Map transactionSearchResult;
        List<FilterItem> dateFilters = new ArrayList();
        if (toDate != null) {
            dateFilters.add(new FilterItem("date", "<=", toDate));
        }

        List<FilterItem> totalFilters = Stream.concat(filters.stream(),
                        Stream.concat(mainRelationFilters.stream(), dateFilters.stream()).collect(Collectors.toList()).stream())
                .collect(Collectors.toList());

        transactionSearchResult = transactionController.advanceSearch(null, null, false, totalFilters, false);

        if (transactionSearchResult != null && transactionSearchResult.containsKey("balanceSum")) {
            balance += (Double) transactionSearchResult.get("balanceSum");
        }

        return balance;
    }

    private String getFilterItemsAsString(List<FilterItem> filterItems) {
        if (filterItems == null) return "";

        String filterItemsAsString = "";
        for (FilterItem filterItem : filterItems.stream().sorted(Comparator.comparing(FilterItem::getProperty)).collect(Collectors.toList())) {
            filterItemsAsString += filterItem.toString() + ",";
        }

        if (!filterItemsAsString.isEmpty()) {
            filterItemsAsString = filterItemsAsString.substring(0, filterItemsAsString.length() - 1);
        }

        return filterItemsAsString;
    }

    private AccountBalance getRelevantBalance(
            Bucket bucket,
            Date toDate,
            List<FilterItem> filterItems) {

        SelectQuery selectQuery = new SelectQuery(AccountBalance.class);
        selectQuery.filterBy("bucket", "=", bucket);
        if (toDate != null) selectQuery.filterBy("toDate", "<=", toDate);
        selectQuery.filterBy("filterItemsAsString", "=", getFilterItemsAsString(filterItems));
        selectQuery.sortBy("toDate", false, true);
        selectQuery.sortBy("id", false, true);
        selectQuery.setLimit(1);

        List<AccountBalance> accountBalances = selectQuery.execute();
        return accountBalances.isEmpty() ? null : accountBalances.get(0);
    }

    private AccountBalance persistNewAccountBalance(
            Bucket bucket,
            Date toDate,
            Double value,
            List<FilterItem> filterItems) {

        AccountBalance accountBalance = new AccountBalance();
        accountBalance.setBucket(bucket);
        accountBalance.setToDate(toDate);
        accountBalance.setActualValue(value);
        accountBalance.setFilterItemsAsString(getFilterItemsAsString(filterItems));

        return accountBalanceRepository.save(accountBalance);
    }

    public void deleteBucketAccountBalanceAfter(
            Bucket bucket,
            java.sql.Date date) {

        if (bucket == null || date == null) return;

        List<AccountBalance> accountBalances = accountBalanceRepository.
                findByBucketAndToDateGreaterThanEqual(bucket, date);
        accountBalanceRepository.delete(accountBalances);
    }

    public Bucket setBucketBalanceBasedOnTransaction(Bucket b) {
        if (b == null) return null;

        Double balance = bucketRepository.findAllBucketsWithCalculatesCorrectBalance(b.getId());
        logger.info("bucket id: " + b.getId() +
                "; old balance: " + b.getBalance() +
                "; new balance: " + balance);

        b.setBalance(balance);
        bucketRepository.save(b);

        return bucketRepository.findOne(b.getId());
    }

    public void calculateCorrectBalanceBasedTransaction() {
        Page<Map<String, Object>> allBucketWithBalance;
        Long lastId = -1L;

        do {
            allBucketWithBalance = bucketRepository.findAllBucketsWithCalculatesCorrectBalance(
                    lastId, PageRequest.of(0, 100));

            allBucketWithBalance.getContent()
                    .forEach(bucketWithBalance -> {
                        Bucket bucket = (Bucket) bucketWithBalance.get("bucket");
                        if (bucket != null &&
                                bucketWithBalance.get("correctBalance") != null &&
                                bucket.getBalance() != bucketWithBalance.get("correctBalance")) {
                            logger.info("Bucket id: " + bucket.getId() +
                                    "; current balance: " + bucket.getBalance() +
                                    "; correctBalance: " + bucketWithBalance.get("correctBalance"));

                            expensePaymentService.sendEmailToCashierForScheduledCollection(
                                    bucket, bucket.getBalance(), (Double) bucketWithBalance.get("correctBalance"));

                            bucket.setBalance((Double) bucketWithBalance.get("correctBalance"));
                            bucketRepository.save(bucket);
                        }
                    });

            if (!allBucketWithBalance.getContent().isEmpty()) {
                lastId = ((Bucket) allBucketWithBalance.getContent().get(allBucketWithBalance.getContent().size() - 1).get("bucket")).getId();
            }
        } while (!allBucketWithBalance.getContent().isEmpty());

        ParameterRepository parameterRepository = Setup.getRepository(ParameterRepository.class);
        Parameter p = parameterRepository.findByModuleAndCode(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_LAST_CALCULATE_BUCKETS_BALANCE);
        p.setValue(new DateTime().toString("yyyy-MM-dd HH:mm:ss"));
        parameterRepository.save(p);
    }
}
package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.TechnicalException;
import com.magnamedia.entity.Payment;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.magnamedia.controller.DirectDebitFileController.DD_FORM_DOWNLOAD_POSITION;
import static com.magnamedia.entity.DirectDebitFile.FILE_TAG_DD_ACTIVATION;
import static com.magnamedia.entity.DirectDebitFile.FILE_TAG_DD_SIGNATURE;

/**
 * <AUTHOR> <PERSON><PERSON>
 * Created on : 2025-01-30
 */
@Service
public class AttachmentService {

    public <T> List<T> getDdfSecuredAttachments(List<T> attachments) {
        User user = CurrentRequest.getUser();

        if (user == null || !user.hasPosition(DD_FORM_DOWNLOAD_POSITION)) {
            return attachments != null ?
                    attachments.stream()
                            .filter(a -> !((String) getAttributeValue(a, "tag")).equalsIgnoreCase(FILE_TAG_DD_ACTIVATION) &&
                                    !((String) getAttributeValue(a, "tag")).equalsIgnoreCase(FILE_TAG_DD_SIGNATURE))
                            .collect(Collectors.toList()) :
                    new ArrayList<>();
        }

        return attachments;
    }

    private Object getAttributeValue(Object value, String attName) {
        switch (attName) {
            case "tag":
                return value instanceof Map ? (String) ((Map) value).get("tag") :
                        value instanceof Attachment ? ((Attachment) value).getTag() : "";

            case "creationDate":
                return value instanceof Map ? (Date) ((Map) value).get("creationDate") :
                        value instanceof Attachment ? ((Attachment) value).getCreationDate() : "";
            default:
                return "";
        }
    }

    public <T> List<T> getFilteredAttachments(List<T> attachments) {
        if (attachments == null || attachments.isEmpty()) return attachments;

        if (CurrentRequest.getUser() != null &&
                CurrentRequest.getUser().hasPosition("payment_details_signed_version_access")) {
            return attachments;
        }

        List<T> paymentDetailsAttachments = attachments.stream()
                .filter(att -> ((String) getAttributeValue(att, "tag")).startsWith(Payment.FILE_TAG_PAYMENTS_RECEIPT)).collect(Collectors.toList());

        List<T> result = attachments.stream().filter(att ->
                !((String) getAttributeValue(att, "tag")).startsWith(Payment.FILE_TAG_PAYMENTS_RECEIPT)).collect(Collectors.toList());


        try {
            java.util.Date signedVersionDate = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss")
                    .parse(Setup.getParameter(Setup.getModule("clientmgmt"), "PAYMENT_DETAILS_SIGNED_VERSION_DATE"));
            result.addAll(paymentDetailsAttachments.stream()
                    .filter(at -> ((Date) getAttributeValue(at, "creationDate")).getTime() > signedVersionDate.getTime())
                    .collect(Collectors.toList()));
            return result;
        } catch (ParseException e) {
            e.printStackTrace();
            throw new TechnicalException("Error in parsing parameter: " +
                    Setup.getParameter(Setup.getModule("clientmgmt"), "PAYMENT_DETAILS_SIGNED_VERSION_DATE"));
        }
    }
}
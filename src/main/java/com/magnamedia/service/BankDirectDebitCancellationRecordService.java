package com.magnamedia.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.BankDirectDebitCancelationFile;
import com.magnamedia.entity.BankDirectDebitCancelationRecord;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.extra.DDUtils;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.*;
import com.opencsv.CSVParserBuilder;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;


@Service
public class BankDirectDebitCancellationRecordService {

    protected static final Logger logger = Logger.getLogger(BankDirectDebitCancellationRecordService.class.getName());

    @Autowired
    private BankDirectDebitCancelationRecordRepository bankDirectDebitCancelationRecordRepository;
    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;
    @Autowired
    private DirectDebitCancelationToDoRepository directDebitCancelationToDoRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private BankDirectDebitCancelationFileRepository bankDirectDebitCancelationFileRepository;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private BackgroundTaskHelper backgroundTaskHelper;

    @Transactional
    public void processFile(Map<String, Object> payload) throws IOException {
        BankDirectDebitCancelationFile cancellationFile = bankDirectDebitCancelationFileRepository.findOne(Utils.parseValue(payload.get("entityId"), Long.class));
        parseRecords(cancellationFile);
        autoConfirmRecords(cancellationFile, false);
    }

    // ACC-9315
    public void processFileByRPA(Map<String, Object> payload) throws IOException {
        BankDirectDebitCancelationFile cancellationFile = bankDirectDebitCancelationFileRepository.findOne(Utils.parseValue(payload.get("entityId"), Long.class));
        logger.info("file ID : " + cancellationFile.getId());
        parseRecords(cancellationFile);
        autoConfirmRecords(cancellationFile, true);
    }

    // ACC-330
    // ACC-9315 - Optimized for performance
    public void parseRecords(BankDirectDebitCancelationFile cancellationFile) throws IOException {
        Date smallestCheckerDate = null;

        Storage.updateAttachements(cancellationFile);
        Attachment att = cancellationFile.getAttachments().get(0);
        List<BankDirectDebitCancelationRecord> records = new ArrayList<>();

        // Define batch size for database operations
        final int BATCH_SIZE = 500;
        int recordCount = 0;


        // Configure CSVReader with optimal settings for performance
        BufferedReader br = new BufferedReader(new InputStreamReader(Storage.getStream(att)));
        CSVReader csvReader = new CSVReaderBuilder(br)
                .withCSVParser(new CSVParserBuilder().withSeparator(',').build()) // Use comma as separator
                .withSkipLines(5) // Skip first 5 lines
                .withKeepCarriageReturn(false) // Improve performance by not keeping carriage returns
                .withVerifyReader(false) // Disable reader verification for better performance
                .build();

        String[] cellsValues;
        // Read and process records in batches
        while ((cellsValues = csvReader.readNext()) != null) {
            // Skip empty rows
            if (cellsValues.length == 0) continue;

                BankDirectDebitCancelationRecord cancellationRecord = new BankDirectDebitCancelationRecord();
                try {
                    // Parse the CSV record into a cancellation record
                    parseRecordFields(cellsValues, cancellationRecord);

                    // Process checker date and update smallest date if needed
                    Date checkerDate = cancellationRecord.getCheckerDate() != null ?
                            new Date(cancellationRecord.getCheckerDate().getTime()) : null;

                    if (checkerDate != null && cancellationRecord.getCbStatus() == null) {
                        if (smallestCheckerDate == null || checkerDate.before(smallestCheckerDate)) {
                            smallestCheckerDate = checkerDate;
                        }
                    }
                } catch (Exception ex) {
                    handleRecordException(ex, cancellationRecord);
                } finally {
                    if (cancellationRecord.getCbStatus() != null) {
                        cancellationRecord.setBankDirectDebitCancelationFile(cancellationFile);
                        records.add(cancellationRecord);
                        recordCount++;

                    // Save in batches to avoid large transactions
                    if (recordCount % BATCH_SIZE == 0) {
                        // Validate records in batch before saving
                        validateRecords(records);
                        bankDirectDebitCancelationRecordRepository.save(records);
                        records.clear();
                    }
                }
            }
        }

        // Save any remaining records
        if (!records.isEmpty()) {
            // Validate remaining records in batch before saving
            validateRecords(records);
            bankDirectDebitCancelationRecordRepository.save(records);
        }

        br.close();

        // Update file with the smallest checker date
        cancellationFile.setOldestPendingForCancellationDD(smallestCheckerDate);
        bankDirectDebitCancelationFileRepository.save(cancellationFile);
    }

    /**
     * Parses CSV record fields into a BankDirectDebitCancellationRecord object
     */
    private void parseRecordFields(String[] cellsValues, BankDirectDebitCancelationRecord cancellationRecord) throws ParseException {
        // ACC-404
        cancellationRecord.setRowIndex(Integer.parseInt(getCellValue(cellsValues[0])));
        cancellationRecord.setDdaRefNo(cellsValues.length > 1 && cellsValues[1] != null && !cellsValues[1].isEmpty() ?
                        getCellValue(cellsValues[1]) : null);
        cancellationRecord.setCancelReason(cellsValues.length > 5 && cellsValues[5] != null && !cellsValues[5].isEmpty() ?
                        getCellValue(cellsValues[5]) : null);

        String cbStatus = cellsValues.length > 6 && cellsValues[6] != null && !cellsValues[6].isEmpty() ?
                getCellValue(cellsValues[6]) : null;
        cancellationRecord.setCbStatus(cbStatus);

        if (cbStatus != null) {
            cancellationRecord.setStatus(BankDirectDebitCancelationRecord.CB_STATUS_NAK.equals(cbStatus) ?
                    BankDirectDebitCancelationRecord.status.REJECTED :
                    BankDirectDebitCancelationRecord.CB_STATUS_ACK.equals(cbStatus) ?
                            BankDirectDebitCancelationRecord.status.CONFIRMED : null);
        }

        // Parse checker date
        String checkerDateStr = cellsValues.length > 8 && cellsValues[8] != null && !cellsValues[8].isEmpty() ?
                getCellValue(cellsValues[8]) : null;
        if (checkerDateStr != null) {
            String[] checkerDateArray = checkerDateStr.split(" ");
            if (checkerDateArray.length == 2) {
                Date checkerDate = DateUtil.parseDateSlashed(checkerDateArray[1]);
                cancellationRecord.setCheckerDate(new java.sql.Date(checkerDate.getTime()));
            }
        }
    }

    private String getCellValue(String value) {
        return value.startsWith("\'") ? value.substring(1) : value;
    }

    /**
     * Handles exceptions that occur during record processing
     * Logs the exception and sets an appropriate error message on the record
     */
    private void handleRecordException(Exception ex, BankDirectDebitCancelationRecord cancellationRecord) {
        ex.printStackTrace();
        if (ex instanceof ParseException) {
            cancellationRecord.setErrorMessage("Date Parsing Exception: " + ex.getMessage());
        } else if (ex instanceof NumberFormatException) {
            cancellationRecord.setErrorMessage("Number Parsing Exception: " + ex.getMessage());
        } else {
            cancellationRecord.setErrorMessage("Exception: " + ex.getMessage());
        }
    }

    /**
     * Validates a batch of BankDirectDebitCancelationRecord entries collectively
     * This optimizes performance by executing a single query for all records instead of individual queries
     *
     * @param records List of records to validate
     */
    private void validateRecords(List<BankDirectDebitCancelationRecord> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        // Extract all DDA reference numbers that need validation
        List<String> ddaRefNos = records.stream()
                .filter(record -> record.getDdaRefNo() != null && !record.getDdaRefNo().isEmpty())
                .map(BankDirectDebitCancelationRecord::getDdaRefNo)
                .distinct()
                .collect(Collectors.toList());

        if (ddaRefNos.isEmpty()) {
            return;
        }

        Map<String, List<Map<String, Object>>> ddfMap = directDebitFileRepository.findByDdaRefNoIn(ddaRefNos)
                .stream()
                .collect(Collectors.groupingBy(m -> (String) m.get("ddaRefNo")));

        // Process each record using the pre-fetched data
        for (BankDirectDebitCancelationRecord record : records) {
            if (record.getDdaRefNo() == null || record.getDdaRefNo().isEmpty() || !ddfMap.containsKey(record.getDdaRefNo())) {
                continue;
            }

            List<Map<String, Object>> ddfs = ddfMap.get(record.getDdaRefNo());
            if (ddfs.isEmpty()) {
                continue;
            }
            Long ddfId = getMatchedDDfWithRecord(ddfs);
            if (ddfId != null) {
                DirectDebitFile ddf = new DirectDebitFile();
                ddf.setId(ddfId);
                record.setDirectDebitFile(ddf);
            }
        }
    }

    private Long getMatchedDDfWithRecord(List<Map<String, Object>> ddfs) {

        // First check if the application ID indicates an old DDF format
        // For old DDFs, we only use PENDING_FOR_CANCELLATION status, otherwise leave it null
        boolean isOldDdf = DDUtils.isOldDdfByApplicationId((String) ddfs.get(0).get("applicationId"));

        // Always prioritize finding a DirectDebitFile with PENDING_FOR_CANCELLATION status
        Map<String, Object> pendingForCancellationDdf = ddfs
                .stream()
                .filter(ddf -> DirectDebitStatus.PENDING_FOR_CANCELLATION.equals(ddf.get("ddStatus")))
                .findFirst()
                .orElse(null);

        Long ddfId = null;
        if (pendingForCancellationDdf != null) {
            // Found a PENDING_FOR_CANCELLATION DDF - use it regardless of whether it's old or new format
            ddfId = (Long) pendingForCancellationDdf.get("id");
        } else if (!isOldDdf) {
            // For non-old DDFs, try additional fallback options if no PENDING_FOR_CANCELLATION was found

            // Try to find a CANCELED status DDF
            Map<String, Object> canceledDdf = ddfs
                    .stream()
                    .filter(ddf -> DirectDebitStatus.CANCELED.equals(ddf.get("ddStatus")))
                    .findFirst()
                    .orElse(null);

            ddfId = canceledDdf != null ?
                    (Long) canceledDdf.get("id") :          // Found a CANCELED DDF - use it
                    (Long) ddfs.get(0).get("id");           // If no appropriate status was found, use the first DDF

        }
        return ddfId;
    }

    public void autoConfirmRecords(BankDirectDebitCancelationFile cancellationFile, boolean fromRPA) {
        String autoConfirmParam = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_AUTO_CONFIRM_DD_RPA_RECORDS);
        logger.info("AutoConfirmParam" + autoConfirmParam + " for file ID : " + cancellationFile.getId());
        if (!Boolean.parseBoolean(autoConfirmParam)) return;

        addNewPropertyToSendEmail(cancellationFile);
        confirmRecords(cancellationFile, fromRPA);
    }

    public void confirmRecords(BankDirectDebitCancelationFile cancellationFile, boolean fromRPA) {
        List<Map<String, Object>> records = bankDirectDebitCancelationRecordRepository.findRecordsToBeConfirmed(cancellationFile);
        logger.info("records size : " + records.size());

        Long userId = CurrentRequest.getUser().getId();

        for (Map<String, Object> record : records) {
            switch ((BankDirectDebitCancelationRecord.status) record.get("status")) {
                case CONFIRMED:
                    backgroundTaskHelper.createBGTForConfirmOneDD(record, fromRPA, userId);
                    break;
                case REJECTED:
                    backgroundTaskHelper.createBGTForApprovalOnRejectionBank(record, fromRPA, userId);
                    break;
            }
        }
    }

    @Transactional
    public void approveRejectionByBankForOneDD(Long recordId, Boolean fromRPA) {
        logger.info("record id: " + recordId);
        BankDirectDebitCancelationRecord record = bankDirectDebitCancelationRecordRepository.findOne(recordId);
        record.setConfirmed(true);
        record.setConfirmedByRPA(fromRPA);
        bankDirectDebitCancelationRecordRepository.save(record);

        // ACC-9383
        if (record.getDirectDebitFile() == null) return;
        if (QueryService.existsEntity(DirectDebitCancelationToDo.class,
                "e.directDebitFile = :p0 and e.completed = false and e.stopped = false",
                new Object[]{record.getDirectDebitFile()})) {
            return;
        }

        DirectDebitCancelationToDo toDo = directDebitCancelationToDoRepository.findFirstByDirectDebitFileAndCompletedTrueAndStoppedFalseOrderByCreationDateDesc(record.getDirectDebitFile());
        if (toDo == null) {
            toDo = directDebitCancelationToDoRepository.findFirstByDirectDebitFileOrderByCreationDateDesc(record.getDirectDebitFile());
        }
        DirectDebitCancelationToDo directDebitCancelationToDo = new DirectDebitCancelationToDo();
        directDebitCancelationToDo.setDirectDebitFile(record.getDirectDebitFile());
        directDebitCancelationToDo.setDirectDebit(record.getDirectDebitFile().getDirectDebit());
        directDebitCancelationToDo.setReason(toDo != null ? toDo.getReason() : null);
        directDebitCancelationToDo.setIgnoreDDRejectionFlow(true);

        directDebitCancelationToDoRepository.save(directDebitCancelationToDo);
    }

    @Transactional
    public boolean confirmOneDD(Long recordId, Boolean fromRPA) {
        logger.info("record id: " + recordId);
        BankDirectDebitCancelationRecord bankDirectDebitCancelationRecord =
                bankDirectDebitCancelationRecordRepository.findOne(recordId);

        if (!bankDirectDebitCancelationRecord.isConfirmed() &&
                bankDirectDebitCancelationRecord.getDirectDebitFile() != null &&
                bankDirectDebitCancelationRecord.getDirectDebitFile().getDdStatus()
                        .equals(DirectDebitStatus.PENDING_FOR_CANCELLATION)) {

            DirectDebitFile ddf = bankDirectDebitCancelationRecord.getDirectDebitFile();
            ddf.setDdStatus(DirectDebitStatus.CANCELED);
            ddf.setCancellationDate(new Date());
            directDebitFileRepository.save(ddf);
        }

        bankDirectDebitCancelationRecord.setConfirmed(true);
        bankDirectDebitCancelationRecord.setConfirmedByRPA(fromRPA);
        bankDirectDebitCancelationRecordRepository.save(bankDirectDebitCancelationRecord);
        return true;
    }

    //ACC-9005
    public void sendReportInEmail(Long fileId, Long matchedAndAcceptedCount, Long matchedAndRejectedCount, Long unMatchedCount) {
        BankDirectDebitCancelationFile file = bankDirectDebitCancelationFileRepository.findOne(fileId);
        Map<String, String> parameters = new HashMap<>();

        parameters.put("report_name", file.getAttachments().get(0).getName());
        parameters.put("upload_date", new DateTime(file.getDate()).toString("yyyy-MM-dd HH:mm:ss"));
        parameters.put("matched_accepted_count", Long.toString(matchedAndAcceptedCount));
        parameters.put("matched_rejected_count", Long.toString(matchedAndRejectedCount));
        parameters.put("unmatched_count", Long.toString(unMatchedCount));
        parameters.put("details_link", Setup.getApplicationContext()
                .getBean(Utils.class)
                .shorteningUrl(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) +
                        "#!/accounting/importing-bank-dd-cancellation-file/" + file.getId()));

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("dd_400_cancellation_report", parameters,
                        Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DDS_CANCELLATION_REPORT_RPA_MAIL),
                        "DD 400 Cancellation Report");
    }

    // ACC-9005
    public void addNewPropertyToSendEmail(BankDirectDebitCancelationFile file) {
        try {
            AccountingEntityProperty a = new AccountingEntityProperty();
            a.setOrigin(file);
            a.setKey(AccountingModule.SEND_REPORT_EMAIL_AFTER_BACKGROUND_TASKS_FINISHED);
            a.setPurpose("BankDirectDebitCancellationFile_Confirm_DD_" + file.getId());

            // Add Email parameters for the property
            Long matchedAndAcceptedCount = bankDirectDebitCancelationRecordRepository.countMatchedRecordsByStatus(
                    file.getId(), BankDirectDebitCancelationRecord.status.CONFIRMED);
            Long matchedAndRejectedCount = bankDirectDebitCancelationRecordRepository.countMatchedRecordsByStatus(
                    file.getId(), BankDirectDebitCancelationRecord.status.REJECTED);
            Long unMatchedCount = bankDirectDebitCancelationRecordRepository.countUnMatchedRecords(file.getId());

            a.setValue(objectMapper.writeValueAsString(new HashMap<String, String>() {{
                put("fileId", file.getId().toString());
                put("templateName", "dd_400_cancellation_report");
                put("matchedAndAcceptedCount", Long.toString(matchedAndAcceptedCount));
                put("matchedAndRejectedCount", Long.toString(matchedAndRejectedCount));
                put("unMatchedCount", Long.toString(unMatchedCount));
            }}));
            Setup.getRepository(AccountingEntityPropertyRepository.class).save(a);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
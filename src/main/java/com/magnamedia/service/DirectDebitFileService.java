package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.module.type.DirectDebitFileStatus;
import com.magnamedia.module.type.DirectDebitFileSubStatus;
import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.repository.DirectDebitFileRepository;
import org.joda.time.DateTime;

import java.util.List;

/**
 * <AUTHOR>
 * ACC-8771
 * */
public class DirectDebitFileService {

    public static DirectDebitFileSubStatus getSubStatus(DirectDebitFile directDebitFile) {
        if (!directDebitFile.getStatus().equals(DirectDebitFileStatus.APPROVED) ||
                directDebitFile.getDdMethod().equals(DirectDebitMethod.AUTOMATIC)) return null;

        List<String> subStatus = Setup.getRepository(DirectDebitFileRepository.class)
                .getSubStatus(directDebitFile.getId(),
                        new DateTime().dayOfMonth().withMinimumValue().withTimeAtStartOfDay().toDate(),
                        new DateTime().plusMonths(1).dayOfMonth().withMinimumValue().withTimeAtStartOfDay().minusMillis(1).toDate());

        return subStatus.isEmpty() ? DirectDebitFileSubStatus.NOT_COLLECTED :
                DirectDebitFileSubStatus.valueOf(subStatus.get(0));
    }
}
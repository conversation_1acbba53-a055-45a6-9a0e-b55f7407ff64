package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.PurchaseOrder;
import com.magnamedia.entity.StockKeeperToDo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.repository.PurchaseOrderRepository;
import com.magnamedia.repository.StockKeeperToDoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <PERSON> (Feb 09, 2021)
 */
@Service
public class StockKeeperToDoService {
    @Autowired
    StockKeeperToDoRepository stockKeeperToDoRepository;
//    @Autowired
//    StockKeeperReceiveMaintenanceRequestStep stockKeeperReceiveMaintenanceRequestStep;
//
//    public void businessAfterPayMaintenancePayment(ExpensePayment expensePayment) {
//        ExpenseRequestTodo expenseRequestTodo = expensePayment.getExpenseRequestTodos().get(0);
//        StockKeeperToDo stockKeeperToDo = stockKeeperToDoRepository.findTop1ByExpenseRequestTodoOrderByCreationDateDesc(expenseRequestTodo);
//        stockKeeperReceiveMaintenanceRequestStep.supperOnDone(stockKeeperToDo.getMaintenanceRequest());
//    }

    @Autowired
    PurchaseOrderRepository purchaseOrderRepository;

    public void businessAfterPurchaseOrderGoToPayment(PurchaseOrder purchaseOrder) {
        StockKeeperToDo stockKeeperToDo = new StockKeeperToDo();
        stockKeeperToDo.setToDoType(StockKeeperToDo.StockKeeperToDoType.PURCHASING);
        stockKeeperToDo.setStatus(StockKeeperToDo.StockKeeperToDoStatus.PENDING);
        stockKeeperToDo.setPurchaseOrder(purchaseOrder);
        stockKeeperToDo.setExpenseRequestTodo(purchaseOrder.getExpenseRequestTodo());
        stockKeeperToDo.setName("Receive Order " +
                " from " + purchaseOrder.getSupplier().getName() +
                " for " + purchaseOrder.getPurchasingToDo().getCategory().getName() + " " + purchaseOrder.getPurchasingToDo().getOrderCycleName());
        stockKeeperToDoRepository.save(stockKeeperToDo);
    }

    public void businessAfterPayPurchaseOrderPayment(ExpenseRequestTodo expenseRequestTodo) {
        StockKeeperToDo stockKeeperToDo = Setup.getRepository(StockKeeperToDoRepository.class).findTop1ByExpenseRequestTodoOrderByCreationDateDesc(expenseRequestTodo);
        stockKeeperToDo.setStatus(StockKeeperToDo.StockKeeperToDoStatus.PAID);
        stockKeeperToDoRepository.save(stockKeeperToDo);
    }
}

package com.magnamedia.service;

import com.magnamedia.core.entity.Template;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.extra.CcAppCmsTemplate;
import com.magnamedia.extra.MvSmsTemplateCode;
import com.magnamedia.helper.PicklistHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

@Service
public class MvAppSmsTemplate {


    public ArrayList<Template> getMessageTemplates() {
        return new ArrayList() {
            {
                add(new Template(MvSmsTemplateCode.MV_PAYMENT_EXPIRY_4_1_1_SMS.toString(), false,
                    "ACTION REQUIRED: The Monthly Bank Payment Form that you’ve previously signed expires soon. To " +
                        "avoid interruption in your service, we'll amend and resubmit your Bank Payment Forms to the " +
                        "bank. Please remember, you can stop the service and cancel your contract anytime you want. " +
                        "If you have any questions, please reach us at: @chat_with_us_link@",
                    "In case of an unused signature, the message will be sent to the client"));
                add(new Template(MvSmsTemplateCode.MV_PAYMENT_EXPIRY_4_1_2_SMS.toString(), false,
                    "@greetings@ The Monthly Bank Payment Form that you’ve previously signed expires on @ExpiryDate@. " +
                        "To avoid interruption in your service, please click here to sign the new payment form: " +
                        "@link_send_dd_details@ .",
                    "The message that will be sent to the client if there are no unused signatures"));

                add(new Template(MvSmsTemplateCode.MV_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_SMS.toString(), false, "Unfortunately, the bank didn't process the cancellation of your future payments on time. You were charged AED @amount@. Don't worry; we’ll send you that amount within the next 7 days.", "send to the Client If we receive an amount from the client when he doesn't owe us any amount"));
                add(new Template(MvSmsTemplateCode.MV_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_SMS.toString(), false, "You were charged AED @amount@ today. But don’t worry, we’ll transfer you the same amount by @scheduled_termination_date@.", "send to the Client If we receive an amount from the client when he doesn't owe us any amount"));

                add(new Template(MvSmsTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_INSURANCE_SMS.toString(), false,
                    "Dear @Client_Name@, Thank you for using our services. It’s time to renew your " +
                        "maid’s insurance. Your maid’s insurance will be automatically renewed and you’ll " +
                        "be charged AED @insurance_DD_amount@ on @payment_date@. Please ensure sufficient funds are " +
                        "available in your account to avoid penalties and service interruption. Thank you.",
                    "In case of a postpone direct debit generate with type insurance, the message will be sent to the client before five days of send date"));

                add(new Template(MvSmsTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_SAME_DAY_RECRUITMENT_FEE_SMS.toString(), false,
                    "Dear @Client_Name@, Thank you for using our services. It’s time to renew your maid’s " +
                        "visa. Your maid’s visa will be automatically renewed and you’ll be charged AED " +
                        "@SDR_DD_Amount@ on @SDR_Payment_date@. Please ensure sufficient funds " +
                        "are available in your account to avoid service interruption. Thank you.",
                    "In case of a postpone direct debit generate with type Same day recruitment fee, the message will be sent to the client before five days of send date"));

                add(new Template(MvSmsTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_OTHER_DD_TYPE_SMS.toString(), false,
                    "Dear @Client_Name@, Thank you for using our services. Please note that we’re going to " +
                        "deduct AED @payment_amount@ from your account on @payment_date@ for @DD_TYPE@ " +
                        "fee. Please ensure sufficient funds are available in your account to avoid penalties and service " +
                        "interruption. Thank you.",
                    "In case of a postpone direct debit generate with other types, the message will be sent to the client before five days of send date"));

                add(new Template(MvSmsTemplateCode.MV_DD_PENDING_INFO_SMS.toString(), false,
                    "@greetings@, please click on the following link @link_send_dd_details@ using your phone, and complete your Bank Payment Form.",
                    "DD status is Pending and sub-status is Pending to Receive DD Info/Signature from Client, At 10 AM"));

                add(new Template(MvSmsTemplateCode.MV_PAYTABS_THANKS_MESSAGE_SMS.toString(), false,
                    "Thank you for settling your payment for your maid's service. We just want to remind you that " +
                        "we can't accept Credit or Debit Card payments anymore. Please click on the following link: " +
                        "@link_send_dd_details@ to complete your Monthly Bank Payment Form so we can deduct your future payments.",
                    "IPAM flow, upon receive paytab payment thanks message"));

                // ACC-4591
                add(new Template(MvSmsTemplateCode.MV_ACCOUNTING_PAY_ACCOMMODATION_FEE_SMS.toString(), false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your maid’s accommodation fee of AED @amount@ by " +
                        "Credit card, please click on this link: @paytabs_link@",
                    "Send SMS to client for pay your maid’s accommodation fee"));
                add(new Template(MvSmsTemplateCode.MV_ACCOUNTING_PAY_CC_TO_MV_SMS.name(),false,
                    "Dear @client_name@, Thank you for using maids.cc. To hire your maid under the new payment plan and to " +
                        "pay the transfer fee of AED @amount@ by Credit card, please click @paytabs_link@",
                    "Send SMS to client for pay pay CC to MV"));
                add(new Template(MvSmsTemplateCode.MV_ACCOUNTING_PAY_MONTHLY_PAYMENT_SMS.toString(), false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your monthly payment of AED @amount@ by Credit card, " +
                        "please click: @paytabs_link@",
                    "Send SMS to client for pay Monthly Payment"));
                add(new Template(MvSmsTemplateCode.MV_ACCOUNTING_PAY_OVERSTAY_FEES_SMS.toString(), false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your maid's overstay fines of AED @amount@ by Credit card, " +
                        "please click: @paytabs_link@",
                    "Send SMS to client for pay Overstay fees"));
                add(new Template(MvSmsTemplateCode.MV_ACCOUNTING_PAY_PCR_TEST_SMS.toString(), false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your maid's PCR fees of " +
                        "AED @amount@ by Credit card, please click: @paytabs_link@",
                    "Send SMS to client for pay PCR Test"));
                add(new Template(MvSmsTemplateCode.MV_ACCOUNTING_PAY_URGENT_VISA_CHARGES_SMS.toString(), false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your maid's urgent visa " +
                        "fees of AED @amount@ by Credit card, please click: @paytabs_link@",
                    "Send SMS to client for pay Urgent Visa Charges"));
                add(new Template(MvSmsTemplateCode.MV_ACCOUNTING_PAY_INSURANCE_SMS.toString(), false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your maid's insurance fees of " +
                        "AED @amount@ by Credit card, please click: @paytabs_link@",
                    "Send SMS to client for pay Urgent Insurance"));
                add(new Template(MvSmsTemplateCode.MV_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_SMS.toString(), false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay AED @amount@ by Credit card, " +
                        "please click: @paytabs_link@",
                    "Send SMS to client for pay Other payments types"));
            }
        };
    }
}
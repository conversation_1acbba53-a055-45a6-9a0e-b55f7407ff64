/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.library.rule;

import com.google.common.base.Joiner;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.BaseRuleLog;
import com.magnamedia.entity.SubRuleLog;
import com.magnamedia.repository.BaseRuleLogRepository;
import com.magnamedia.repository.SubRuleLogRepository;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Logger<T> {

    protected String getGenericName() {
        try {
            return ((Class<T>) ((ParameterizedType) getClass()
                    .getGenericSuperclass()).getActualTypeArguments()[0]).getTypeName();
        } catch (Exception ex) {
            return "UNKNOWN";
        }
    }

    private String getGenericName(GeneralRuleResult<T> generalRuleResult) {
        try {
            return generalRuleResult.getGeneralRuleRemainingResult().get(0).getClass().toString();
        } catch (Exception ex) {

        }

        try {
            return generalRuleResult.getGeneralRuleRemainingResult().get(0).getClass().toString();
        } catch (Exception ex) {

        }

        return "UNKNOWN";

    }

    private BaseRuleLogRepository getBaseRuleLogRepository() {
        return Setup.getApplicationContext().getBean(BaseRuleLogRepository.class);
    }

    private SubRuleLogRepository getSubRuleLogRepository() {
        return Setup.getApplicationContext().getBean(SubRuleLogRepository.class);
    }

    public Boolean LogGeneralRuleResults(List<GeneralRuleResult<T>> results, LoggingMode loggingMode) {
        if (loggingMode == LoggingMode.OFF || results == null) {
            return false;
        }

        if (results.isEmpty()) {
            return false;
        }

        for (GeneralRuleResult<T> result : results) {
            LogGeneralRuleResult(result, loggingMode);
        }

        return true;
    }

    public Boolean LogGeneralRuleResult(GeneralRuleResult<T> result, LoggingMode loggingMode) {
        if (loggingMode == LoggingMode.OFF || result == null) {
            return false;
        }

        if (loggingMode == LoggingMode.ONLY_UPDATES && !result.getWithUpdate()) {
            return false;
        }

        BaseRuleLog log = new BaseRuleLog();
        log.setBaseRuleCode(result.getGeneralRuleApplied().getCode());
        log.setBaseRuleName(result.getGeneralRuleApplied().getName());
        log.setBaseRuleDescription(result.getGeneralRuleApplied().getDescription());
        log.setBaseRuleCreator(result.getGeneralRuleApplied().getCreator());
        log.setRemainingSize(result.getGeneralRuleRemainingResultSize());
        log.setSubRulesApplied(result.getSubRulesApplied());
        log.setBaseRuleEntityType(this.getGenericName(result));
        log.setRemainingIds(Joiner.on(",").join(result.getGeneralRuleRemainingResultIds()));
        log.setWithUpdate(result.getWithUpdate());

        List<SubRuleLog> subLogs = new ArrayList<>();

        SubRulesResultSet<T> subRulesResult = result.getSubRulesResultSet();
        if (subRulesResult != null && subRulesResult.getSubRulesResults() != null) {

            for (SubRuleResult<T> subResult : subRulesResult.getSubRulesResults()) {
                SubRuleLog subLog = new SubRuleLog();
                subLog.setResultSize(subResult.getSubRuleResultSize());
                subLog.setSubRuleCode(subResult.getSubRuleApplied().code());
                subLog.setSubRuleCreator(subResult.getSubRuleApplied().creator());
                subLog.setSubRuleName(subResult.getSubRuleApplied().name());
                subLog.setWithUpdate(subResult.getWithUpdate());

                subLog.setResultIds(Joiner.on(",").join(subResult.getSubRuleResultIds()));

                subLog.setBaseRuleLog(log);

                subLogs.add(subLog);
            }
        }

        this.getBaseRuleLogRepository().save(log);
        this.getSubRuleLogRepository().save(subLogs);

        return true;
    }

    enum LoggingMode {
        ALL, ONLY_UPDATES, OFF
    }
}

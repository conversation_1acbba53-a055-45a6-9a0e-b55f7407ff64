package com.magnamedia.helper;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.PaymentTermConfig;
import com.magnamedia.extra.DiscountsWithVatHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.service.ContractPaymentTermServiceNew;
import org.springframework.stereotype.Component;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 21, 2021
 *         Jirra CMA-996
 */

@Component
public class CcAppContentHelper {
    
    protected static final Logger logger = Logger.getLogger(CcAppContentHelper.class.getName());
    
    // Used by CMS, @DD_amendment_to_non_filipina@ @DD_second_amendment_to_non_filipina@
    public String getMaidNationalityPrice(Long maidId, Long currentCPTId) {
        logger.log(Level.SEVERE, "getMaidNationalityPrice: " + maidId);
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(maidId);
        ContractPaymentTerm currentCPT = Setup.getRepository(ContractPaymentTermRepository.class).findOne(currentCPTId);
        
        PicklistItem nationality = housemaid.getNationality();
        PicklistItem maidCC = PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE, AccountingModule.MAID_CC_PROSPECT_TYPE);

        ContractPaymentTermServiceNew contractPaymentTermServiceNew = Setup.getApplicationContext()
                .getBean(ContractPaymentTermServiceNew.class);
        PaymentTermConfig termConfig = contractPaymentTermServiceNew.findSuitableConfig(nationality,
                maidCC,
                contractPaymentTermServiceNew.getContractPaymentTypeOfContract(currentCPT.getContract(), housemaid.getLiveOut()),
                currentCPT.getPackageType());

        return String.format("%.0f", Math.floor(DiscountsWithVatHelper.getAmountWithoutVat(termConfig.getMonthlyPayment())));
    }
}

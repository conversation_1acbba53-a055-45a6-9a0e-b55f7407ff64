package com.magnamedia.helper;

import java.util.Random;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created at Apr 7, 2019
 * <AUTHOR> kanaan <<EMAIL>>
 * Jirra ACC-1092
 */
public class UaePhoneNormlizer {

    static final String AB = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    static Random rnd = new Random();

    public static String randomString(int len) {
        StringBuilder sb = new StringBuilder(len);
        for (int i = 0; i < len; i++) {
            sb.append(AB.charAt(rnd.nextInt(AB.length())));
        }
        return sb.toString();
    }
    
    public static String NormalizePhoneNumber(String number) {

        if ((number == null) || (number.isEmpty()))
            return "";
        String NormalizedNumber = number;
        NormalizedNumber = NormalizedNumber.replace("-", "");
        NormalizedNumber = NormalizedNumber.replace("+", "");
        NormalizedNumber = NormalizedNumber.replace(")", "");
        NormalizedNumber = NormalizedNumber.replace("(", "");
        NormalizedNumber = NormalizedNumber.replace(" ", "");

        NormalizedNumber = removeFirst(NormalizedNumber, "00");

        if (NormalizedNumber.startsWith("0")) {
            NormalizedNumber = removeFirst(NormalizedNumber, "0");
            NormalizedNumber = "971" + NormalizedNumber;
        }
        if (NormalizedNumber.startsWith("9710")) {
            NormalizedNumber = removeFirst(NormalizedNumber, "9710");
            NormalizedNumber = "971" + NormalizedNumber;
        }
        return NormalizedNumber;
    }

    public static String removeFirst(String s,
            String toRemove) {
        if (s.startsWith(toRemove)) {
            s = s.substring(toRemove.length());
        }
        return s;
    }
    
    public static boolean startWith9715Normlized(String phone){
        phone=NormalizePhoneNumber(phone);
        return phone.startsWith("9715");
    }
}

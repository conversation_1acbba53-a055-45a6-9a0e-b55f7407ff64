package com.magnamedia.helper;

import java.util.ArrayList;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 20, 2018
 */
public class PaginationUtil {

    public static Page<?> listToPage(Pageable pageable, List<?> list){
        int start = (int) pageable.getOffset();
        int end = (start + (pageable).getPageSize()) > list.size()?
                list.size() : (start + (pageable).getPageSize());

        return new PageImpl<>(
                start > end ? new ArrayList<>() : list.subList(start, end),
                pageable,
                list.size());
    }
    
}

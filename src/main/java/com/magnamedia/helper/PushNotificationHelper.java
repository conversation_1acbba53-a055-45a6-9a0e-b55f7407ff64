package com.magnamedia.helper;

import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.master.repository.ModuleRepository;
import com.magnamedia.core.repository.PushNotificationRepository;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.repository.LocalPushNotificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 25, 2021
 *         Jirra ACC-2956
 * 
 */

@Component
public class PushNotificationHelper {

    private static final Logger logger = Logger.getLogger(PushNotificationHelper.class.getName());

    @Autowired
    private PushNotificationRepository pushNotificationRepository;
    
    @Autowired
    private ModuleRepository moduleRepository;

    @Autowired
    private BackgroundTaskService backgroundTaskService;

    @Autowired
    private LocalPushNotificationRepository localPushNotificationRepository;

    public List<PushNotification> getByOwnerTypeAndId(String ownerType, Long ownerId) {
        return localPushNotificationRepository.findByOwnerIdAndOwnerTypeAndCreatorModule(
                ownerId, ownerType, moduleRepository.findByCode("accounting").getId());
    }

    public List<PushNotification> getByOwnerTypeAndIdAndCreationDateAfter(String ownerType, Long ownerId, Date date) {
        return localPushNotificationRepository.findByOwnerIdAndOwnerTypeAndCreatorModuleAndCreationDateGreaterThan(
                ownerId, ownerType, moduleRepository.findByCode("accounting").getId(), date);
    }
    
    // ACC-3351
    public List<PushNotification> getByRecepientTypeAndIdOrderByCreationDesc(String recepientType, Long recepientId) {
        return localPushNotificationRepository.findByRecepientIdAndRecepientTypeAndCreatorModuleOrderByCreationDateDesc(
                recepientId.toString(), recepientType, moduleRepository.findByCode("accounting").getId());
    }

    public List<PushNotification> getNotificationsToMoveInbox(String recepientType, Long recepientId, String type) {
        logger.log(Level.INFO, "recepientId " + recepientId);

        return localPushNotificationRepository.getNotificationsToMoveInbox(
                recepientId.toString(), recepientType, moduleRepository.findByCode("accounting").getId(),
                NotificationLocation.HOME, type + "%");
    }

    public void stopDisplaying(List<PushNotification> notifications) {
        if (notifications == null) return;

        for (PushNotification notification : notifications) {
            long time = new Date().getTime();

            backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                    "stopDisplaying_PushNotification#" + notification.getId() + "_" + time,
                    "pushNotificationHelper",
                    "accounting",
                    "stopDisplayingPushNotification",
                    "PushNotification",
                    notification.getId(),
                    true,
                    false,
                    new Class<?>[]{Long.class},
                    new Object[]{notification.getId()});
        }
    }

    @Transactional
    public void stopDisplayingPushNotification(Long notificationId) {
        stopDisplayingNotification(pushNotificationRepository.findOne(notificationId));
    }
    
    @Transactional
    public void stopDisplayingNotification(PushNotification notification) {
        logger.log(Level.INFO, "Hiding Notification#" + notification.getId());
        
        notification.setSmsAlwaysSend(false);
        notification.setReceived(true);
        notification.setLocation(NotificationLocation.INBOX);
        notification.setDisabled(true);
        pushNotificationRepository.save(notification);
    }
}
package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.BaseCompanyReportController;
import com.magnamedia.core.annotation.BeforeDelete;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.repository.BaseCompanyReportRepository;

import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@MappedSuperclass
public class BaseReportCompany extends BaseEntity {

    @Label
    @NotNull
    protected String name;

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    @NotNull
    protected Company company;

    @Transient
    protected Double expensesValue;

    @Transient
    protected Double revenuesValue;

    //Jirra ACC-1389
    @Transient
    protected Double profitAdjustmentsValue;

    @Transient
    protected Long relatedExpensesRatio;

    @Transient
    protected Long relatedRevenuesRatio;

    @Transient
    protected Double outputVATCollected;

    @Transient
    protected Double inputVATCollected;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public List<BasePLNode> getSortedChildren() {
        return null;
    }

    public List<BasePLNode> getChildren() {
        return null;
    }

    public void setChildren(List<BasePLNode> children) {
    }


    public Double getExpensesValue() {
        return expensesValue;
    }

    public void setExpensesValue(Double expensesValue) {
        this.expensesValue = expensesValue;
    }

    public Double getRevenuesValue() {
        return revenuesValue;
    }

    public void setRevenuesValue(Double revenuesValue) {
        this.revenuesValue = revenuesValue;
    }

    public Double getProfitAdjustmentsValue() {
        return profitAdjustmentsValue;
    }

    public void setProfitAdjustmentsValue(Double profitAdjustmentsValue) {
        this.profitAdjustmentsValue = profitAdjustmentsValue;
    }

    public Double getOutputVATCollected() {
        return outputVATCollected == null ? 0.0 : outputVATCollected;
    }

    public void setOutputVATCollected(Double outputVATCollected) {
        this.outputVATCollected = outputVATCollected;
    }

    public Double getInputVATCollected() {
        return inputVATCollected == null ? 0.0 : inputVATCollected;
    }

    public void setInputVATCollected(Double inputVATCollected) {
        this.inputVATCollected = inputVATCollected;
    }

    public Long getRelatedExpensesRatio() {
        return relatedExpensesRatio;
    }

    public void setRelatedExpensesRatio(Long relatedExpensesRatio) {
        this.relatedExpensesRatio = relatedExpensesRatio;
    }

    public Long getRelatedRevenuesRatio() {
        return relatedRevenuesRatio;
    }

    public void setRelatedRevenuesRatio(Long relatedRevenuesRatio) {
        this.relatedRevenuesRatio = relatedRevenuesRatio;
    }

    public void calculateValue(Date fromDate, Date toDate, BaseCompanyReportController.SearchCriteria searchCriteria) {
    }

    //Jirra ACC-385 ACC-583
    public Long calculateRelatedRatio(List<BasePLNode> nodes, Double value, Long allRatio) {

        Long childrenRatio = 0L;
        for (int i = 0; i < nodes.size(); i++) {
            BasePLNode plNode = nodes.get(i);
            Long childRatio = Math.round(plNode.getValue() * 1000 / value);
            plNode.setRelatedRatio(childRatio);
            childrenRatio += childRatio;
        }
        if (childrenRatio != allRatio) {
            Long complementary = allRatio - childrenRatio;
            for (int i = nodes.size() - 1; i >= 0; i--)
                if (nodes.get(i).getValue() != 0.0D) {
                    nodes.get(i).setRelatedRatio(
                            nodes.get(i).getRelatedRatio() + complementary);
                    break;
                }
        }
        for (BasePLNode node : nodes)
            node.calculateRelatedRatio(value);
        return childrenRatio;
    }

    //Jirra ACC-583
    public Long calculateRatio(List<BasePLNode> nodes, Double value, Long allRatio) {

        Long childrenRatio = 0L;
        for (int i = 0; i < nodes.size(); i++) {
            BasePLNode plNode = nodes.get(i);
            Long childRatio = Math.round(plNode.getValue() * 1000 / value);
            plNode.setRatio(childRatio);
            childrenRatio += childRatio;
        }
        if (childrenRatio != allRatio) {
            Long complementary = allRatio - childrenRatio;
            for (int i = nodes.size() - 1; i >= 0; i--)
                if (nodes.get(i).getValue() != 0.0D) {
                    nodes.get(i).setRatio(
                            nodes.get(i).getRatio() + complementary);
                    break;
                }
        }
        return childrenRatio;

    }

    //Jirra ACC-804
    public Double calculateOutputVATCollected(Date fromDate, Date toDate) {
        Double result = 0D;
        for (BasePLNode plNode : getChildren()) {
            result += plNode.calculateOutputVATCollected(fromDate, toDate);
        }

        return result;
    }

    //Jirra ACC-804
    public Double calculateInputVATCollected(Date fromDate, Date toDate) {
        Double result = 0D;
        for (BasePLNode plNode : getChildren()) {
            result += plNode.calculateInputVATCollected(fromDate, toDate);
        }

        return result;
    }

    @BeforeInsert
    @BeforeUpdate
    public void validate() {
        if (getCompany() == null || getCompany().getId() == null)
            throw new RuntimeException("Company should not be empty.");

        if (getName() == null || getName().isEmpty())
            throw new RuntimeException("Name should not be empty.");
    }

    @BeforeDelete
    public void beforDelete() {
        if (!getChildren().isEmpty())
            throw new RuntimeException("the company Could not be deleted, it has a structure.");
    }

    @JsonIgnore
    public BaseCompanyReportRepository getRepository() {
        return null;
    }

    @JsonIgnore
    public String getReportTitle() {
        return "";
    }
}

package com.magnamedia.entity;

import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;

import java.sql.Date;
import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 20, 2018
 */
@Entity
public class InsuranceAgreement extends BaseEntity {

    @Label
    private String companyName;

    @Column
    private Date startDate;

    @Column
    private Date endDate;

    @Column
    private Double price;

    @Column
    private Double debitAmount;

    @Column
    private Double creditAmount;

    @Column
    private Integer numberOfDaysChargedAfterCancelation;

    @Column
    private Double basmaCharge;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Integer getNumberOfDaysChargedAfterCancelation() {
        return numberOfDaysChargedAfterCancelation;
    }

    public void setNumberOfDaysChargedAfterCancelation(Integer numberOfdaysCahrgedAfterCancelation) {
        this.numberOfDaysChargedAfterCancelation = numberOfdaysCahrgedAfterCancelation;
    }

    public Double getDebitAmount() {
        return debitAmount;
    }

    public void setDebitAmount(Double debitAmount) {
        this.debitAmount = debitAmount;
    }

    public Double getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(Double creditAmount) {
        this.creditAmount = creditAmount;
    }

    public Double getBasmaCharge() {
        return basmaCharge;
    }

    public void setBasmaCharge(Double basmaCharge) {
        this.basmaCharge = basmaCharge;
    }

    @BeforeUpdate
    @BeforeInsert
    public void validate() {
        if (this.getBasmaCharge() == null) {
            throw new RuntimeException("Basma charge is Mandatory");
        }
    }
}

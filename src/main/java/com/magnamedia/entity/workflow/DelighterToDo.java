package com.magnamedia.entity.workflow;

import com.magnamedia.core.entity.User;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.Housemaid;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 22, 2020
 *         Jirra ACC-2802
 */

@Entity
public class DelighterToDo extends WorkflowEntity {

    public DelighterToDo() {
        super("");
    }

    public DelighterToDo(String startTaskName) {
        super(startTaskName);
    }

    @ManyToOne(fetch = FetchType.LAZY)
    private Housemaid housemaid;

    @Column
    private Double amountMaidHasToPay = 0.0;

    @Transient
    private User userWhoClosedFinalSettlementToDo;
    
    //Jirra SMM-2764
    private Long userIdWhoClosedTask;
    
    public Long getUserIdWhoClosedTask() {
        return userIdWhoClosedTask;
    }

    public void setUserIdWhoClosedTask(Long userIdWhoClosedTask) {
        this.userIdWhoClosedTask = userIdWhoClosedTask;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Double getAmountMaidHasToPay() {
        return amountMaidHasToPay;
    }

    public void setAmountMaidHasToPay(Double amountMaidHasToPay) {
        this.amountMaidHasToPay = amountMaidHasToPay;
    }

    public User getUserWhoClosedFinalSettlementToDo() {
        return userWhoClosedFinalSettlementToDo;
    }

    public void setUserWhoClosedFinalSettlementToDo(User userWhoClosedFinalSettlementToDo) {
        this.userWhoClosedFinalSettlementToDo = userWhoClosedFinalSettlementToDo;
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

    @Override
    public String getFinishedTaskName() {
        return null;
    }
}

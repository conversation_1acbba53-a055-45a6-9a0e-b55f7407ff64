package com.magnamedia.entity.workflow;

import com.magnamedia.module.type.MaidManagerWorkorderType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jul 24, 2019
 * Jirra ACC-737
 */
@Entity
public class MaidManagerWorkOrder extends WorkOrder {

    @Column
    @Enumerated(EnumType.STRING)
    private MaidManagerWorkorderType type;

    
    
    public MaidManagerWorkOrder() {
        super("");
    }

    public MaidManagerWorkorderType getType() {
        return type;
    }

    public void setType(MaidManagerWorkorderType type) {
        this.type = type;
    }
}

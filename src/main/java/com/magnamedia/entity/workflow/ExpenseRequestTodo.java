package com.magnamedia.entity.workflow;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.ExpensePaymentController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.annotation.AfterUpdate;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.*;
import com.magnamedia.entity.serializer.BucketIdLabelCodeSerializer;
import com.magnamedia.entity.serializer.ExpensePaymentSerializer;
import com.magnamedia.entity.serializer.ExpenseSerializer;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import com.magnamedia.helper.AttachmentHelper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.helper.StringHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.CurrencyExchangeSevice;
import com.magnamedia.service.EmailTemplateService;
import com.magnamedia.service.ExpenseNotificationService;
import com.magnamedia.service.ExpensePaymentService;
import com.magnamedia.workflow.type.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Formula;
import org.hibernate.annotations.Where;
import org.hibernate.envers.NotAudited;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Ketrawi
 *         Created on Jan 18, 2021
 *         Jirra ACC-2913
 */

@Entity
@Where(clause = "DELETED <> true")
public class ExpenseRequestTodo extends WorkflowEntity {
    private static final Logger logger = Logger.getLogger(ExpenseRequestTodo.class.getName());

    public ExpenseRequestTodo() {
        super("");
    }

    public ExpenseRequestTodo(String string) {
        super(string);
    }

    @ManyToOne
    @JsonSerialize(using = ExpenseSerializer.class)
    private Expense expense;


    @Enumerated(EnumType.STRING)
    private ExpenseRequestStatus status;


    @Column(nullable = false)
    private Double amount;


    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem currency;

    @Column(nullable = false)
    private Double amountInLocalCurrency;


    @Enumerated(EnumType.STRING)
    private ExpensePaymentMethod paymentMethod;


    @Column
    private Double loanAmount;


    @Column(nullable = false)
    private Double amountToPay;

    @Column
    @Enumerated(EnumType.STRING)
    private ExpenseBeneficiaryType beneficiaryType;

    @Column
    private Long relatedToId;


    @Column
    @Enumerated(EnumType.STRING)
    private ExpenseRelatedTo.ExpenseRelatedToType relatedToType;

    @Column
    private Long beneficiaryId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User requestedBy;

    @Lob
    private String approvedBy;

    @Enumerated(EnumType.STRING)
    private ExpenseRequestTodoFlowActions managerAction;

    @Enumerated(EnumType.STRING)
    private ExpenseRequestTodoFlowActions cooAction;

    @Lob
    private String description;

    @Lob
    private String notes;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense expenseToPost;

    @Column
    private Date requestFrom;

    @Column
    private Date requestTo;

    @Column
    private String invoiceNumber;

    @Transient
    private Boolean taxable;

    @Column
    private Double vatAmount;

    @Column
    private String beneficiaryName;

    @Column
    private String beneficiaryAccountNumber;

    @Column
    private String beneficiaryAccountName;

    @Column
    private String beneficiaryAddress;

    @Column
    private String beneficiaryEid;

    @Column
    private String beneficiaryMobileNumber;

    @Column
    private String beneficiaryIban;

    @Column
    private Boolean beneficiaryHasNoIban = false;

    @Column
    private String swift;

    @Column
    private Boolean international;

    @Lob
    private String address;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = ExpensePaymentSerializer.class)
    private ExpensePayment expensePayment;

    @Column(columnDefinition = "boolean default false")
    private Boolean isRefunded = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean refundConfirmed = false;

    @Column
    private Date refundDate;

    @Column
    private Double refundAmount;

    @Column(nullable = false, columnDefinition = "varchar(255) default 'NEW_REQUEST'")
    @Enumerated(EnumType.STRING)
    private ExpenseRequestType expenseRequestType = ExpenseRequestType.NEW_REQUEST;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User approveHolder;

    @Column
    private Boolean invoiceAttached;

    @Column
    private Boolean attachedInvoiceIsValidVatInvoice;

    @Column
    private Boolean approvalEmailSent;

    @Column
    private Boolean sendApprovalEmail;

    @Column
    private String token;

    @Column(columnDefinition = "boolean default false")
    private Boolean linkedToFopRequest = false;

    @Transient
    private Boolean businessRuleFiredBefore = false;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "EXPENSE_REQUEST_TELECOM_PHONES",
            joinColumns = @JoinColumn(
                    name = "REQUEST_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "TELECOM_ID",
                    referencedColumnName = "ID")
    )
    @JsonSerialize(using = IdLabelListSerializer.class)
    private List<TelecomPhone> telecomPhones = new ArrayList<>();

    @Column
    private Double percentageDeductedFromEmployee;

    @JsonSerialize(using = IdLabelSerializer.class)
    @OneToOne(cascade = {CascadeType.ALL})
    private ExpenseRequestTicket ticket;

    @Column
    private String paymentLink;

    @Column(columnDefinition = "boolean default false")
    private Boolean paymentAlreadyPaid = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean amountAlreadyPaid = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = BucketIdLabelCodeSerializer.class)
    private Bucket bucket;

    @Transient
    private Boolean canBeRefunded;

    @Column(columnDefinition = "boolean default false")
    private Boolean confirmed = Boolean.FALSE;

    @OneToOne
    private LogisticsWorkOrder covidLaserTestTaxiOrder;

    @Transient
    private BillType transportationBillType;
    @Transient
    private String sendToGetBetterPriceNote;

    @Column
    private String cooRequestType;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PaymentRequestPurpose paymentRequestPurpose;

    @Column
    private Integer vacationDays;

    // ACC-1187
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem purposeAdditionalDescription;

    @Column
    private String approveHolderEmail;

    @Column
    private Boolean approvedFromEmail;

    @Column(columnDefinition = "boolean default false")
    private Boolean deleted = false;

    @Lob
    private String rejectionNotes;

    @NotAudited
    @Formula("(select Case when e.RELATED_TO_TYPE = 'TEAM' Then concat('TEAM - ', p.NAME) " +
            "when e.RELATED_TO_TYPE = 'APPLICANT' Then concat('APPLICANT - ', mc.NAME) " +
            "when e.RELATED_TO_TYPE = 'MAID' Then concat('MAID - ', h.NAME) " +
            "when e.RELATED_TO_TYPE = 'OFFICE_STAFF' Then concat('OFFICE_STAFF - ', o.NAME) " +
            "ELSE '' END " +
            "from EXPENSEREQUESTTODOS e " +
            "left join PICKLISTS_ITEMS  p on e.RELATED_TO_TYPE = 'TEAM' and  p.ID = e.RELATED_TO_ID " +
            "left join MAIDSATCANDIDATEWAS mc on  e.RELATED_TO_TYPE = 'APPLICANT' and mc.ID = e.RELATED_TO_ID " +
            "left join HOUSEMAIDS h on e.RELATED_TO_TYPE = 'MAID' and h.ID = e.RELATED_TO_ID " +
            "left join OFFICESTAFFS o on e.RELATED_TO_TYPE = 'OFFICE_STAFF' and o.ID = e.RELATED_TO_ID " +
            "where e.ID = ID)")
    @Basic(fetch = FetchType.LAZY)
    @JsonIgnore
    private String relatedToInfo;

    @NotAudited
    @Formula("(select Case when e.BENEFICIARY_TYPE = 'MAID' Then concat('MAID - ', h.NAME) " +
            "when e.BENEFICIARY_TYPE = 'OFFICE_STAFF' Then concat('Staff - ', o.NAME) " +
            "when e.BENEFICIARY_TYPE = 'SUPPLIER' Then concat('Supplier - ', s.NAME) " +
            "when e.BENEFICIARY_TYPE = 'TAXI_DRIVER' Then 'Taxi Driver' " +
            "when e.BENEFICIARY_TYPE = 'NOT_DETERMINED' Then e.BENEFICIARY_NAME " +
            "ELSE '' END " +
            "from EXPENSEREQUESTTODOS e " +
            "left join HOUSEMAIDS h on e.BENEFICIARY_TYPE = 'MAID' and h.ID = e.BENEFICIARY_ID " +
            "left join OFFICESTAFFS o on e.BENEFICIARY_TYPE = 'OFFICE_STAFF' and o.ID = e.BENEFICIARY_ID " +
            "left join SUPPLIERS s on e.BENEFICIARY_TYPE = 'SUPPLIER' and s.ID = e.BENEFICIARY_ID " +
            "where e.ID = ID)")
    @Basic(fetch = FetchType.LAZY)
    @JsonIgnore
    private String benefeciaryInfo;

    @NotAudited
    @Formula("(SELECT CASE " +
            "WHEN e.TASK_NAME = 'WAITING_COO_APPROVAL' THEN 'Pending CEO' " +
            "WHEN e.TASK_NAME  = 'WAITING_MANAGER_APPROVAL' AND e.APPROVE_HOLDER_ID IS NOT NULL AND t.id IS NOT NULL " +
                "then concat('Pending ', t.FULL_NAME) " +
            "WHEN e.TASK_NAME = 'WAITING_MANAGER_APPROVAL' AND e.APPROVE_HOLDER_EMAIL IS NOT NULL " +
                "THEN CONCAT('Pending ', e.APPROVE_HOLDER_EMAIL) " +
            "ELSE '' END " +
            "FROM EXPENSEREQUESTTODOS e " +
            "LEFT JOIN USERS t ON t.ID = e.APPROVE_HOLDER_ID " +
            "WHERE e.ID = ID AND e.STATUS = 'PENDING')")
    @Basic(fetch = FetchType.LAZY)
    @JsonIgnore
    private String pendingForApproval;

    private Long flightTicketId;

    @Transient
    private Double electricity;
    @Transient
    private Double water;
    @Transient
    private Double housing;
    @Transient
    private Double sewage;

    @Column
    private Long referredMaidId;

    public Boolean getApprovedFromEmail() {
        return approvedFromEmail;
    }

    public void setApprovedFromEmail(Boolean approvedFromEmail) {
        this.approvedFromEmail = approvedFromEmail;
    }

    public String getApproveHolderEmail() {
        return approveHolderEmail;
    }

    public void setApproveHolderEmail(String approveHolderEmail) {
        this.approveHolderEmail = approveHolderEmail;
    }

    public String getSendToGetBetterPriceNote() {
        return sendToGetBetterPriceNote;
    }

    public void setSendToGetBetterPriceNote(String sendToGetBetterPriceNote) {
        this.sendToGetBetterPriceNote = sendToGetBetterPriceNote;
    }

    public Integer getVacationDays() {
        return vacationDays;
    }

    public void setVacationDays(Integer vacationDays) {
        this.vacationDays = vacationDays;
    }

    public PicklistItem getPurposeAdditionalDescription() {
        return purposeAdditionalDescription;
    }

    public void setPurposeAdditionalDescription(PicklistItem purposeAdditionalDescription) {
        this.purposeAdditionalDescription = purposeAdditionalDescription;
    }

    public PaymentRequestPurpose getPaymentRequestPurpose() {
        return paymentRequestPurpose;
    }

    public void setPaymentRequestPurpose(PaymentRequestPurpose paymentRequestPurpose) {
        this.paymentRequestPurpose = paymentRequestPurpose;
    }

    public LogisticsWorkOrder getCovidLaserTestTaxiOrder() {
        return covidLaserTestTaxiOrder;
    }

    public void setCovidLaserTestTaxiOrder(LogisticsWorkOrder covidLaserTestTaxiOrder) {
        this.covidLaserTestTaxiOrder = covidLaserTestTaxiOrder;
    }

    public Boolean getConfirmed() {
        return confirmed != null && confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public String getCooRequestType() {
        return cooRequestType;
    }

    public void setCooRequestType(String cooRequestType) {
        this.cooRequestType = cooRequestType;
    }

    public Bucket getBucket() {
        return bucket;
    }

    public void setBucket(Bucket bucket) {
        this.bucket = bucket;
    }

    public Boolean getPaymentAlreadyPaid() {
        return paymentAlreadyPaid != null && paymentAlreadyPaid;
    }

    public void setPaymentAlreadyPaid(Boolean paymentAlreadyPaid) {
        this.paymentAlreadyPaid = paymentAlreadyPaid;
    }

    public Boolean getAmountAlreadyPaid() {
        return amountAlreadyPaid;
    }

    public void setAmountAlreadyPaid(Boolean amountAlreadyPaid) {
        this.amountAlreadyPaid = amountAlreadyPaid;
    }

    public String getPaymentLink() {
        return paymentLink;
    }

    public void setPaymentLink(String paymentLink) {
        this.paymentLink = paymentLink;
    }

    public ExpenseRequestTicket getTicket() {
        return ticket;
    }

    public void setTicket(ExpenseRequestTicket ticket) {
        this.ticket = ticket;
    }

    public Double getPercentageDeductedFromEmployee() {
        return percentageDeductedFromEmployee;
    }

    public void setPercentageDeductedFromEmployee(Double percentageDeductedFromEmployee) {
        this.percentageDeductedFromEmployee = percentageDeductedFromEmployee;
    }

    public List<TelecomPhone> getTelecomPhones() {
        return telecomPhones;
    }

    public void setTelecomPhones(List<TelecomPhone> telecomPhones) {
        this.telecomPhones = telecomPhones;
    }

    public BillType getTransportationBillType() {
        return transportationBillType;
    }

    public Boolean getCanBeRefunded() {
        return status != null && status.equals(ExpenseRequestStatus.PAID) && paymentMethod != null &&
                (paymentMethod.equals(ExpensePaymentMethod.CREDIT_CARD) || paymentMethod.equals(ExpensePaymentMethod.BANK_TRANSFER))
                && !BooleanUtils.toBoolean(isRefunded);
    }

    public String getBeneficiaryIban() {
        return beneficiaryIban;
    }

    public void setBeneficiaryIban(String beneficiaryIban) {
        this.beneficiaryIban = beneficiaryIban;
    }

    public Boolean getBeneficiaryHasNoIban() {
        return beneficiaryHasNoIban;
    }

    public void setBeneficiaryHasNoIban(Boolean beneficiaryHasNoIban) {
        this.beneficiaryHasNoIban = beneficiaryHasNoIban;
    }

    public String getSwift() {
        return swift;
    }

    public void setSwift(String swift) {
        this.swift = swift;
    }

    public Boolean getInternational() {
        return international;
    }

    public void setInternational(Boolean international) {
        this.international = international;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBeneficiaryAccountName() {
        return beneficiaryAccountName;
    }

    public void setBeneficiaryAccountName(String beneficiaryAccountName) {
        this.beneficiaryAccountName = beneficiaryAccountName;
    }


    @Column
    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Boolean getLinkedToFopRequest() {
        return linkedToFopRequest;
    }

    public void setLinkedToFopRequest(Boolean linkedToFopRequest) {
        this.linkedToFopRequest = linkedToFopRequest;
    }

    public Boolean getBusinessRuleFiredBefore() {
        return businessRuleFiredBefore;
    }

    public void setBusinessRuleFiredBefore(Boolean businessRuleFiredBefore) {
        this.businessRuleFiredBefore = businessRuleFiredBefore;
    }

    public Boolean getApprovalEmailSent() {
        return approvalEmailSent != null && approvalEmailSent;
    }

    public void setApprovalEmailSent(Boolean approvalEmailSent) {
        this.approvalEmailSent = approvalEmailSent;
    }

    public Boolean getSendApprovalEmail() {
        return sendApprovalEmail != null && sendApprovalEmail;
    }

    public void setSendApprovalEmail(Boolean sendApprovalEmail) {
        this.sendApprovalEmail = sendApprovalEmail;
    }

    public Boolean getInvoiceAttached() {
        return invoiceAttached != null && invoiceAttached;
    }

    public void setInvoiceAttached(Boolean invoiceAttached) {
        this.invoiceAttached = invoiceAttached;
    }

    public Boolean getAttachedInvoiceIsValidVatInvoice() {
        return attachedInvoiceIsValidVatInvoice != null && attachedInvoiceIsValidVatInvoice;
    }

    public void setAttachedInvoiceIsValidVatInvoice(Boolean attachedInvoiceIsValidVatInvoice) {
        this.attachedInvoiceIsValidVatInvoice = attachedInvoiceIsValidVatInvoice;
    }

    public User getApproveHolder() {
        return approveHolder;
    }

    public void setApproveHolder(User approveHolder) {
        this.approveHolder = approveHolder;
    }

    public ExpenseRequestType getExpenseRequestType() {
        return expenseRequestType;
    }

    public void setExpenseRequestType(ExpenseRequestType expenseRequestType) {
        this.expenseRequestType = expenseRequestType;
    }

    public ExpenseBeneficiaryType getBeneficiaryType() {
        return beneficiaryType;
    }

    public void setBeneficiaryType(ExpenseBeneficiaryType beneficiaryType) {
        this.beneficiaryType = beneficiaryType;
    }

    public ExpenseRelatedTo.ExpenseRelatedToType getRelatedToType() {
        return relatedToType;
    }

    public void setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType relatedToType) {
        this.relatedToType = relatedToType;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public ExpenseRequestStatus getStatus() {
        return status;
    }

    public void setStatus(ExpenseRequestStatus status) {
        this.status = status;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public PicklistItem getCurrency() {
        return currency;
    }

    public void setCurrency(PicklistItem currency) {
        this.currency = currency;
    }

    public Double getAmountInLocalCurrency() {
        return amountInLocalCurrency;
    }

    public void setAmountInLocalCurrency(Double amountInLocalCurrency) {
        this.amountInLocalCurrency = amountInLocalCurrency;
    }

    public Double getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(Double loanAmount) {
        this.loanAmount = loanAmount;
    }

    public Double getAmountToPay() {
        return amountToPay;
    }

    public void setAmountToPay(Double amountToPay) {
        this.amountToPay = amountToPay;
    }

    public Long getRelatedToId() {
        return relatedToId;
    }

    public void setRelatedToId(Long relatedToId) {
        this.relatedToId = relatedToId;
    }

    public Long getBeneficiaryId() {
        return beneficiaryId;
    }

    public void setBeneficiaryId(Long beneficiaryId) {
        this.beneficiaryId = beneficiaryId;
    }

    public User getRequestedBy() {
        return requestedBy;
    }

    public void setRequestedBy(User requestedBy) {
        this.requestedBy = requestedBy;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public String getRejectionNotes() { return rejectionNotes; }

    public void setRejectionNotes(String rejectionNotes) { this.rejectionNotes = rejectionNotes; }

    public void addApproval(String newApproval) {
        List<String> items = new ArrayList<>();

        if (this.approvedBy != null) {
            items = Stream.of(this.approvedBy.split(", "))
                    .map(String::trim)
                    .collect(Collectors.toList());
        }

        items.add(newApproval);
        this.approvedBy = StringUtils.join(items, ", ");
    }

    public ExpenseRequestTodoFlowActions getManagerAction() {
        return managerAction;
    }

    public void setManagerAction(ExpenseRequestTodoFlowActions managerAction) {
        this.managerAction = managerAction;
    }

    public ExpenseRequestTodoFlowActions getCooAction() {
        return cooAction;
    }

    public void setCooAction(ExpenseRequestTodoFlowActions cooAction) {
        this.cooAction = cooAction;
    }

    @Transient
    @JsonIgnore
    private PurchaseOrder order;

    public PurchaseOrder getOrder() {
        return order;
    }

    public void setOrder(PurchaseOrder purchaseOrder) {
        this.order = purchaseOrder;
    }

    public void updateDescription() {
        logger.log(Level.INFO, "description: " + description);
        this.description = "";
        PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);

        PicklistItem currencyItem =
                (currency != null && currency.getId() != null) ?
                        picklistItemRepository.findOne(currency.getId()) :
                        Setup.getApplicationContext().getBean(CurrencyExchangeSevice.class).getLocalCurrency();
        String caption = expense != null && expense.getCaption() != null && !expense.getCaption().isEmpty() ? ("/" + expense.getCaption()) : "";
        String relatedToInfo = getRelatedToInfo();

        if (relatedToInfo != null && !relatedToInfo.isEmpty())
            relatedToInfo = "/" + relatedToInfo;

        switch (expenseRequestType) {
            case TRANSPORTATION:
            case ARAMEX: {
                DateFormat dayMonthDayYearFormat = new SimpleDateFormat(
                        "EEE, MMMMM dd,yyyy");
                this.description = "Ex" + getId() + caption + relatedToInfo
                        + "/" + getAmount() + "/" + (currencyItem != null ? currencyItem.getName() : "No currency selected") + (getNotes() != null ? "/" + getNotes() : "") + "/" + dayMonthDayYearFormat.format(requestFrom) + "/" + dayMonthDayYearFormat.format(requestTo);
                break;
            }
            case DEWA: {
                this.description = "Ex" + getId() + caption + relatedToInfo + "/" + getAmount() + "/" +
                        (currencyItem != null ? currencyItem.getName() : "No currency selected") +
                        (getNotes() != null ? "/" + getNotes() : "") + "/" + getUsageText();
                break;
            }
            case TELECOM: {
                this.description = "Ex" + getId() + caption + relatedToInfo + "/" + getAmount() + "/" + (currencyItem != null ? currencyItem.getName() : "No currency selected") + (getNotes() != null ? "/" + getNotes() : "") + "/[" + getUsageText() + "]";
                break;
            }
            case TICKETING: {
                this.description = "Ex" + getId() + caption + relatedToInfo + "/" + getAmount() + "/" + (currencyItem != null ? currencyItem.getName() : "No currency selected") + (getNotes() != null ? "/" + getNotes() : "");
                String departureCountry = getTicket().getDepartureCountry() != null ? picklistItemRepository.findOne(getTicket().getDepartureCountry().getId()).getName() : "";
                String departureAirport = getTicket().getDepartureAirport() != null ? picklistItemRepository.findOne(getTicket().getDepartureAirport().getId()).getName() : "";
                String arrivalCountry = getTicket().getArrivalCountry() != null ? picklistItemRepository.findOne(getTicket().getArrivalCountry().getId()).getName() : "";
                String arrivalAirport = getTicket().getArrivalAirport() != null ?  picklistItemRepository.findOne(getTicket().getArrivalAirport().getId()).getName() : "";
                this.description += "/From " + departureCountry + " " + departureAirport + "/To " + arrivalCountry + " " + arrivalAirport;
                break;
            }
            case MAINTENANCE: {
                this.description = "Ex" + getId() + caption + (getNotes() != null ? "/" + getNotes() : "");
                break;
            }
            case PURCHASING: {
                this.description = "Ex" + getId() + caption;
                if (order != null && order.getPurchasingToDo() != null && order.getPurchasingToDo().getCategory() != null
                        && order.getPurchasingToDo().getCategory().getName() != null && !order.getPurchasingToDo().getCategory().getName().isEmpty())
                    this.description += "/ Purchase items for " + order.getPurchasingToDo().getCategory().getName();
                if (order != null && order.getPurchasingToDo() != null && order.getPurchasingToDo().getOrderCycleName() != null
                        && !order.getPurchasingToDo().getOrderCycleName().isEmpty())
                    this.description += "/ order of " + order.getPurchasingToDo().getOrderCycleName();
                break;
            }
            case INSURANCE: {
                this.description = "Ex" + getId() + caption;
                break;
            }
            case AUTO_DEDUCT: {
                SupplierRepository supplierRepository = Setup.getRepository(SupplierRepository.class);
                CurrencyExchangeSevice currencyExchangeSevice = Setup.getApplicationContext().getBean(CurrencyExchangeSevice.class);
                Supplier supplier = beneficiaryId != null ? supplierRepository.findOne(beneficiaryId) : null;
                this.description = "Ex" + getId() + caption + (supplier != null ? "/" + supplier.getName() : "") + "/" + this.amount + "/" + currencyExchangeSevice.getLocalCurrency().getName();
                break;
            }
            default: {
                this.description = "Ex" + getId() + caption + relatedToInfo + "/" + getAmount() + "/" + (currencyItem != null ? currencyItem.getName() : "No currency selected") + (getNotes() != null ? "/" + getNotes() : "");
                break;
            }
        }

        //ACC-3916
        String beneficiaryInfo = getBenefeciaryInfo();
        logger.log(Level.INFO, "Beneficiary Info: " + beneficiaryInfo);
        if (beneficiaryInfo != null && !beneficiaryInfo.isEmpty()) {
            this.description += " Beneficiary : " + beneficiaryInfo;
        }

        if (expenseRequestType.equals(ExpenseRequestType.DEWA)) {
            // ACC-5817
            this.description += "/" + System.lineSeparator();
            this.description += "Electricity " + getElectricity() +" AED" + System.lineSeparator();
            this.description += "Water " + getWater() + " AED" + System.lineSeparator();
            this.description += "Housing " + getHousing() + " AED" + System.lineSeparator();
            this.description += "Sewage " + getSewage() + " AED" + System.lineSeparator();
            this.description += "___________________________" + System.lineSeparator();
            this.description += "Current month total is " + getAmount()  + " AED";
        }
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getNotes() {
        return notes;
    }

    @JsonIgnore
    public String getCooAllNotes() {
        String cooAllNotes = notes;

        if (this.expenseRequestType != null && cooAllNotes != null && this.expenseRequestType.equals(ExpenseRequestType.INSURANCE)) {
            cooAllNotes += System.lineSeparator() + "Last Invoice: " + getRequestFrom();
        }

        if (this.expenseRequestType != null && cooAllNotes != null && this.expenseRequestType.equals(ExpenseRequestType.TICKETING) && getTicket() != null) {
            cooAllNotes += System.lineSeparator() + "Ticket Date: " + getTicket().getDepartOn();
        }

        if (this.relatedToType != null && cooAllNotes != null && this.relatedToType.equals(ExpenseRelatedTo.ExpenseRelatedToType.MAID)) {
            cooAllNotes += System.lineSeparator() + "{" + (getHousemaid() != null ? getHousemaid().getName() : "") + "}";
        }

        return cooAllNotes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Expense getExpenseToPost() {
        return expenseToPost;
    }

    public void setExpenseToPost(Expense expenseToPost) {
        this.expenseToPost = expenseToPost;
    }

    public Date getRequestFrom() {
        return requestFrom;
    }

    public void setRequestFrom(Date from) {
        this.requestFrom = from;
    }

    public Date getRequestTo() {
        return requestTo;
    }

    public void setRequestTo(Date to) {
        this.requestTo = to;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public Boolean getTaxable() {
        taxable = null;
        if (beneficiaryType != null && beneficiaryId != null
                && beneficiaryType.equals(ExpenseBeneficiaryType.SUPPLIER)) {

            Supplier supplier = Setup.getRepository(SupplierRepository.class)
                    .findOne(beneficiaryId);
            if (supplier != null) taxable = supplier.getVatRegistered();
        }

        return taxable;
    }

    public Double getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(Double vatAmount) {
        this.vatAmount = vatAmount;
    }

    public String getBeneficiaryName() {
        return beneficiaryName;
    }

    public void setBeneficiaryName(String beneficiaryName) {
        this.beneficiaryName = beneficiaryName;
    }

    public String getBeneficiaryAccountNumber() {
        return beneficiaryAccountNumber;
    }

    public void setBeneficiaryAccountNumber(String beneficiaryAccountNumber) {
        this.beneficiaryAccountNumber = beneficiaryAccountNumber;
    }

    public String getBeneficiaryAddress() {
        return beneficiaryAddress;
    }

    public void setBeneficiaryAddress(String beneficiaryAddress) {
        this.beneficiaryAddress = beneficiaryAddress;
    }

    public String getBeneficiaryEid() {
        return beneficiaryEid;
    }

    public void setBeneficiaryEid(String beneficiaryEid) {
        this.beneficiaryEid = beneficiaryEid;
    }

    public String getBeneficiaryMobileNumber() {
        return beneficiaryMobileNumber;
    }

    public void setBeneficiaryMobileNumber(String beneficiaryMobileNumber) {
        this.beneficiaryMobileNumber = beneficiaryMobileNumber;
    }

    public Boolean getIsRefunded() {
        return isRefunded;
    }

    public void setIsRefunded(Boolean refunded) {
        this.isRefunded = refunded;
    }

    public Boolean getRefundConfirmed() {
        return refundConfirmed != null && refundConfirmed;
    }

    public void setRefundConfirmed(Boolean refundConfirmed) {
        this.refundConfirmed = refundConfirmed;
    }

    public Date getRefundDate() {
        return refundDate;
    }

    public void setRefundDate(Date refundDate) {
        this.refundDate = refundDate;
    }

    public Double getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        this.refundAmount = refundAmount;
    }

    public ExpensePaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public String getRelatedToName() {
        return Setup.getApplicationContext().getBean(ExpensePaymentService.class)
                .getRelatedToName(this.relatedToType, this.relatedToId);
    }

    @Transient
    private PaymentRequestMethod paymentOrderMethod;

    public PaymentRequestMethod getPaymentOrderMethod() {
        switch (this.paymentMethod) {
            case CASH:
                return PaymentRequestMethod.CASH;
            case SALARY:
                return PaymentRequestMethod.SALARY;
            case BANK_TRANSFER:
                return PaymentRequestMethod.WIRE_TRANSFER;
            case MONEY_TRANSFER:
                return PaymentRequestMethod.ANSARI_TRANSFER;
        }
        return null;
    }

    public void setPaymentMethod(ExpensePaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public ExpensePayment getExpensePayment() {
        return expensePayment;
    }

    public void setExpensePayment(ExpensePayment expensePayment) {
        this.expensePayment = expensePayment;
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

    @Override
    public String getFinishedTaskName() {
        return null;
    }

    @JsonIgnore
    public static List<ExpenseRequestType> getTypesCreatedFromSystemParameters() {
        return Arrays.asList(ExpenseRequestType.COVID_TEST,
                ExpenseRequestType.COVID_LASER_TEST, ExpenseRequestType.TAXI_REIMBURSEMENT,
                ExpenseRequestType.TRANSPORTATION, ExpenseRequestType.INSURANCE,
                ExpenseRequestType.TICKETING);
    }

    @Transactional
    public void createPayment() {
        ExpensePayment expensePayment = Setup.getApplicationContext().getBean(ExpensePaymentService.class)
                .fillPaymentAttributesFromRequest(this);

        Setup.getApplicationContext().getBean(ExpensePaymentController.class)
                .createEntity(expensePayment);
    }

    @Transactional
    public void moveToCooApprovalStep(boolean putInCOODirectly) {
        logger.log(Level.INFO, "amount(): " + getAmount());
        if (putInCOODirectly || expense.cooLimitExceeded(getAmountInLocalCurrency())) {
            setTaskName(ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString());
            logger.log(Level.INFO, "cooLimitExceeded.");
        } else {
            if (getPaymentMethod() == null || !getPaymentMethod().equals(ExpensePaymentMethod.CHEQUE) ||
                    !getPaymentMethod().equals(ExpensePaymentMethod.INVOICED)) {

                setStatus(ExpenseRequestStatus.PENDING_PAYMENT);
            }

            setTaskName(ExpenseRequestTodoType.PAYMENT_OBJECT_CREATED.toString());
        }
    }

    @Transactional
    @AfterUpdate
    public void afterUpdateTrigger() {
        if (getDeleted()) return;
        sendApprovalEmailTrigger();
        paymentTrigger();
    }

    @Transactional
    public void sendApprovalEmailTrigger() {
        logger.log(Level.INFO, "sendApprovalEmail");
        if (getSendApprovalEmail() && !getApprovalEmailSent() && approveHolderEmail != null) {
            logger.log(Level.INFO, "sendApprovalEmail triggered");

            Setup.getApplicationContext().getBean(EmailTemplateService.class)
                    .sendExpenseRequestTodoEmail(approveHolderEmail,
                            "Expense Approval Request for " +
                                    (this.getExpense() != null ? this.getExpense().getCaption() : ""),
                            "expense_request_approval", this);
            setApprovalEmailSent(true);
        }
    }

    @AfterInsert
    @Transactional
    public void afterInsertTrigger() {
        attachmentsTriggers();
        setTokenTrigger();
        updateDescription();
        notificationTrigger();
        sendApprovalEmailTrigger();
        beneficiaryFieldsTrigger();
        cooRequestTypeTrigger();
        setCurrencyTrigger();
    }

    private void setCurrencyTrigger() {
        if (currency != null) return;
        currency = Setup.getApplicationContext().getBean(CurrencyExchangeSevice.class).getLocalCurrency();
    }

    @Transactional
    public void setTokenTrigger() {
        logger.log(Level.INFO, "setTokenTrigger trigger has been fired.");
        if (token == null) setToken(StringHelper.randomString(16));
    }

    @Transactional
    public void notificationTrigger() {
        Setup.getApplicationContext().getBean(ExpenseNotificationService.class)
                .sendNotifications(this);
    }

    @Transactional
    public void paymentTrigger() {
        logger.log(Level.INFO, "paymentTrigger");
        if (status != null && status.equals(ExpenseRequestStatus.PENDING_PAYMENT) &&
                expensePayment == null) {

            boolean skipPayment = getPaymentMethod() == null ||
                    (expense == null && expenseToPost == null) ||
                    getPaymentMethod().equals(ExpensePaymentMethod.CHEQUE) ||
                    getPaymentMethod().equals(ExpensePaymentMethod.INVOICED) ||
                    (expenseRequestType != null &&
                            (expenseRequestType.equals(ExpenseRequestType.MAINTENANCE) ||
                                    expenseRequestType.equals(ExpenseRequestType.PURCHASING)));
            if(skipPayment) return;
            createPayment();
        }
    }

    @Transactional
    private void attachmentsTriggers() {
        logger.info("attachmentsTriggers");
        Attachment expenseInvoice = this.getAttachment(AttachmentTag.EXPENSE_REQUEST_INVOICE.toString());

        if (getInvoiceAttached() && expenseInvoice == null) {
            Attachment expenseAttachment = null;
            logger.info("No attachmen 'EXPENSE_REQUEST_INVOICE' ID : " + this.getId());

            for (Attachment a : this.getAttachments()) {
                if (a.getTag().toUpperCase().contains("SIGNATURE")) continue;
                logger.info("using tag: " + a.getTag());
                expenseAttachment = a;
                break;
            }
            if (expenseAttachment == null)
                throw new RuntimeException("there is no invoice attached on the Expense Request.");

            expenseInvoice = Storage.cloneTemporary(expenseAttachment,
                    AttachmentTag.EXPENSE_REQUEST_INVOICE.toString());
            this.addAttachment(expenseInvoice);
        }

        logger.severe("checking invoice");
        if (this.getAttachedInvoiceIsValidVatInvoice()) {
            if (expenseInvoice != null) {
                Attachment paymentVatInvoice = Storage.cloneTemporary(expenseInvoice, AttachmentTag.EXPENSE_REQUEST_VAT_INVOICE.toString());
                this.addAttachment(paymentVatInvoice);
            }
        }
    }

    @Transactional
    @BeforeInsert()
    public void beforeInsertTrigger() {
        // ACC-6275
        processPayment();
        expenseSetupValidationTrigger();
        expenseToPostTrigger();
        setAmountsTrigger();
    }

    private boolean isPaymentValid() {
        return getExpense() != null &&
                ((getPaymentMethod().equals(ExpensePaymentMethod.CASH) && getExpense().getPaidOnTheSpotCash()) ||
                        (getPaymentMethod().equals(ExpensePaymentMethod.CREDIT_CARD) && getExpense().getPaidOnTheSpotCreditCard()));
    }

    public void processPayment() {
        if (!getPaymentAlreadyPaid() && isPaymentValid()) {
            boolean validateBucketBalance = true;
            if (getPaymentMethod().equals(ExpensePaymentMethod.CASH)) {
                setPaymentAlreadyPaid(getExpense().getPaidOnTheSpotCash());

                if ((getBucket() == null || getBucket().getId() == null) &&
                        getExpense().getFromCashBucket() != null) {
                    setBucket(getExpense().getFromCashBucket());
                    validateBucketBalance = false;
                }

            } else if (getPaymentMethod().equals(ExpensePaymentMethod.CREDIT_CARD)) {
                setPaymentAlreadyPaid(getExpense().getPaidOnTheSpotCreditCard());

                if ((getBucket() == null || getBucket().getId() == null) &&
                        getExpense().getFromCreditCardBucket() != null) {
                    setBucket(getExpense().getFromCreditCardBucket());
                    validateBucketBalance = false;
                }
            }

            if (validateBucketBalance && getBucket() != null) {
                Bucket bucket = Setup.getRepository(BucketRepository.class).findOne(getBucket().getId());
                if (bucket != null && getAmount() > bucket.getBalance()) {
                    throw new BusinessException("The selected bucket doesn’t have enough balance, please choose another one");
                }
            }
        }
    }

    public void expenseToPostTrigger() {
        if (expenseToPost != null) return;

        if (expense != null && relatedToType != null) {
            List<ExpenseRelatedTo> expenseRelatedTos = expense.getRelatedTos();
            if (expenseRelatedTos != null && !expenseRelatedTos.isEmpty()) {
                List<ExpenseRelatedTo> expenseRequestTodoRelatedTos = expenseRelatedTos.stream()
                        .filter(e -> e.getRelatedToType().equals(relatedToType))
                        .collect(Collectors.toList());

                if (!expenseRequestTodoRelatedTos.isEmpty())
                    switch (relatedToType) {
                        case TEAM: {
                            List<ExpenseRelatedToTeam> expenseRelatedToTeams = expenseRequestTodoRelatedTos.get(0).getTeams();
                            if (expenseRelatedToTeams != null && !expenseRelatedToTeams.isEmpty()) {
                                List<ExpenseRelatedToTeam> expenseRequestTodoRelatedToTeams = expenseRelatedToTeams.stream()
                                        .filter(e -> e.getTeam() != null && e.getTeam().getId().equals(relatedToId))
                                        .collect(Collectors.toList());

                                if (!expenseRequestTodoRelatedToTeams.isEmpty()) {
                                    setExpenseToPost(expenseRequestTodoRelatedToTeams.get(0).getRelatedExpense());
                                }
                            }
                            break;
                        }
                        case OFFICE_STAFF:
                        case APPLICANT: {
                            setExpenseToPost(expenseRequestTodoRelatedTos.get(0).getRelatedExpense());
                            break;
                        }
                        case MAID: {
                            Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(relatedToId);
                            if (housemaid != null) {
                                if (housemaid.getHousemaidType() != null && housemaid.getHousemaidType().equals(HousemaidType.MAID_VISA)) {
                                    setExpenseToPost(expenseRequestTodoRelatedTos.get(0).getExpenseVisa());
                                } else {
                                    setExpenseToPost(expenseRequestTodoRelatedTos.get(0).getExpenseCC());
                                }
                            }

                            break;
                        }
                    }
            }
        }

        if (expenseToPost == null) setExpenseToPost(expense);
    }

    public void expenseSetupValidationTrigger() {
        if (expense != null) {
            if (relatedToType != null) {
                String stringRelatedToType = relatedToType.toString();
                List<ExpenseRelatedTo> expenseRelatedTos = expense.getRelatedTos();
                if (expenseRelatedTos != null && expenseRelatedTos.stream()
                        .noneMatch(rt -> rt.getRelatedToType().equals(getRelatedToType())))
                    throw new RuntimeException("According to expense " + expense.getName() + " setup, related to can't be " + stringRelatedToType.substring(0, 1).toUpperCase() +
                            stringRelatedToType.substring(1).toLowerCase());
            }

            if (beneficiaryType != null) {
                if (expense.getBeneficiaryType() != null && !expense.getBeneficiaryType().equals(beneficiaryType)) {
                    String stringBeneficiaryType = expense.getBeneficiaryType().toString();
                    throw new RuntimeException("According to expense " + expense.getName() + " setup, beneficiary type should be " + stringBeneficiaryType.substring(0, 1).toUpperCase() +
                            stringBeneficiaryType.substring(1).toLowerCase());
                }
            }

            if (expense.getBeneficiaryType() != null) {
                if (beneficiaryType == null || !beneficiaryType.equals(expense.getBeneficiaryType()))
                    throw new RuntimeException("Beneficiary should be " + expense.getBeneficiaryType());

                if (expense.getBeneficiaryType().equals(ExpenseBeneficiaryType.SUPPLIER)) {
                    List<Supplier> suppliers = expense.getSuppliers();
                    if (suppliers != null && !suppliers.isEmpty()) {
                        if (beneficiaryId == null)
                            throw new RuntimeException("Beneficiary should be selected");
                        else if (suppliers.stream().noneMatch(sup -> sup.getId().equals(beneficiaryId))) {
                            throw new RuntimeException("Beneficiary should be one of the expense setup suppliers");
                        }
                    }
                }
            }

            Set<ExpensePaymentMethod> paymentMethods = expense.getPaymentMethods();
            if (paymentMethods != null && !paymentMethods.isEmpty()) {
                if (paymentMethod != null) {
                    if (paymentMethods.stream().noneMatch(pm -> pm.equals(paymentMethod)))
                        throw new RuntimeException("Payment method should be on of expense setup payment methods");
                } else
                    throw new RuntimeException("Payment method should be selected");
            }

            if (expenseRequestType != null && expenseRequestType.equals(ExpenseRequestType.TAXI_REIMBURSEMENT)) {
                if (paymentMethods == null || paymentMethods.isEmpty() || paymentMethods.stream().noneMatch(pm -> pm.equals(ExpensePaymentMethod.SALARY)) || paymentMethods.stream().noneMatch(pm -> pm.equals(ExpensePaymentMethod.CASH) || pm.equals(ExpensePaymentMethod.CREDIT_CARD)))
                    throw new RuntimeException("Taxi reimbursement expense should have salary and (cash or credit card) payment methods");

            }
        }
    }

    public void setAmountsTrigger() {
        if (amountInLocalCurrency == null) {
            String parameter = Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.EXPENSE_LOCAL_CURRENCY);

            PicklistItem localCurrency = PicklistHelper.getItemNoException(AccountingModule.EXPENSE_CURRENCY, parameter);
            if (currency == null || currency.getId().equals(localCurrency.getId()))
                amountInLocalCurrency = amount;
            else {
                amountInLocalCurrency = Setup.getApplicationContext().getBean(CurrencyExchangeSevice.class)
                        .exchangeToLocal(currency, amount);
            }
        }

        if(amountToPay == null) amountToPay = amount;
    }

    @Transactional
    public void cooRequestTypeTrigger() {
        if (expenseRequestType != null && (cooRequestType == null || cooRequestType.isEmpty()))
            cooRequestType = "Approve " + expenseRequestType.getValue() + " Request";
    }

    @Transactional
    public void beneficiaryFieldsTrigger() {
        if (beneficiaryType != null && !beneficiaryType.equals(ExpenseBeneficiaryType.NOT_DETERMINED)) {
            switch (beneficiaryType) {
                case MAID: {
                    Housemaid housemaid = Setup.getRepository(HousemaidRepository.class)
                            .findOne(beneficiaryId);
                    setBeneficiaryName(housemaid.getName());
                    setBeneficiaryMobileNumber(housemaid.getPhoneNumber());
                    break;
                }
                case OFFICE_STAFF: { // ACC-3473
                    OfficeStaff officeStaff = Setup.getRepository(OfficeStaffRepository.class)
                            .findOne(beneficiaryId);
                    setBeneficiaryName(officeStaff.getNameDestination());
                    setBeneficiaryMobileNumber(officeStaff.getPhoneNumberDestination());
                    setBeneficiaryAccountName(officeStaff.getAccountNameDestination());
                    setBeneficiaryAccountNumber(officeStaff.getAccountNumberDestination());
                    setBeneficiaryAddress(officeStaff.getFullAddressDestination());
                    setBeneficiaryIban(officeStaff.getIbanDestination());
                    setSwift(officeStaff.getSwiftDestination());
                    setBeneficiaryEid(officeStaff.getEidNumber());
                    break;
                }
                case SUPPLIER: {
                    Supplier supplier = Setup.getRepository(SupplierRepository.class)
                            .findOne(beneficiaryId);
                    setBeneficiaryName(supplier.getName());
                    setBeneficiaryAccountName(supplier.getAccountName());
                    setBeneficiaryAccountNumber(supplier.getAccountNumber());
                    setBeneficiaryMobileNumber(supplier.getPhoneNumber());
                    setBeneficiaryAddress(supplier.getAddress());
                    setBeneficiaryIban(supplier.getIban());
                    setInternational(supplier.getInternational());
                    setSwift(supplier.getSwift());
                    break;
                }
                case TAXI_DRIVER: {
                    setBeneficiaryName("Taxi Driver");
                    break;
                }
            }
        }
    }

    @JsonIgnore
    public String getRelatedToInfo() {
        String result = "";
        if (relatedToType != null) {
            switch (relatedToType) {
                case TEAM: {
                    PicklistItem item = Setup.getRepository(PicklistItemRepository.class)
                            .findOne(relatedToId);
                    if (item != null) result = "Team - " + item.getName();
                    break;
                }
                case APPLICANT: {
                    MaidsAtCandidateWA item = Setup.getRepository(MaidsAtCandidateWARepository.class)
                            .findOne(relatedToId);
                    if (item != null) result = "Applicant - " + item.getName();
                    break;
                }
                case MAID: {
                    Housemaid item = Setup.getRepository(HousemaidRepository.class)
                            .findOne(relatedToId);
                    if (item != null) result = "Maid - " + item.getName();
                    break;
                }
                case OFFICE_STAFF: {
                    OfficeStaff item = Setup.getRepository(OfficeStaffRepository.class)
                            .findOne(relatedToId);
                    if (item != null) result = "Staff - " + item.getName();
                    break;
                }
            }
        }
        return result;
    }

    @JsonIgnore
    public Housemaid getHousemaid() {
        return relatedToId == null || relatedToType == null ||
                !relatedToType.equals(ExpenseRelatedTo.ExpenseRelatedToType.MAID) ?
                null : Setup.getRepository(HousemaidRepository.class).findOne(relatedToId);
    }

    @JsonIgnore
    public String getBenefeciaryInfo() {
        String result = "";
        if (beneficiaryType != null) {
            switch (beneficiaryType) {
                case MAID: {
                    result = "Maid - " + Setup.getRepository(HousemaidRepository.class)
                            .findOne(getBeneficiaryId()).getName();
                    break;
                }
                case OFFICE_STAFF: {
                    result = "Staff - " + Setup.getRepository(OfficeStaffRepository.class)
                            .findOne(getBeneficiaryId()).getName();
                    break;
                }
                case SUPPLIER: {
                    result = "Supplier - " + Setup.getRepository(SupplierRepository.class)
                            .findOne(getBeneficiaryId()).getName();
                    break;
                }
                case NOT_DETERMINED: {
                    result = getBeneficiaryName();
                    break;
                }
                case TAXI_DRIVER: {
                    result = "Taxi Driver";
                    break;
                }
            }
        }
        return result;
    }

    @JsonIgnore
    public String getBeneficiaryDetails() {
        String result = "";
        if (paymentMethod != null) {
            switch (paymentMethod) {
                case MONEY_TRANSFER: {
                    result = getBeneficiaryMobileNumber();
                    break;
                }
                case BANK_TRANSFER: {
                    result = "IBAN: " + (getBeneficiaryIban() != null ? getBeneficiaryIban() : "") + "<br/>";
                    result += "Account Name: " + (getBeneficiaryAccountName() != null ? getBeneficiaryAccountName() : "") + "<br/>";
                    result += "Swift: " + (getSwift() != null ? getSwift() : "");
                    break;
                }
            }
        }
        return result;
    }

    @JsonIgnore
    String getUsageText() {
        TelecomPhoneRepository telecomPhoneRepository = Setup.getRepository(TelecomPhoneRepository.class);
        String result = "";
        List<TelecomPhone> phones = getTelecomPhones();
        if (phones != null && !phones.isEmpty()) {
            int index = 1;
            for (TelecomPhone telecomPhone : phones) {
                TelecomPhone persistedTelecomPhone = telecomPhoneRepository.findOne(telecomPhone.getId());

                result += persistedTelecomPhone.getUsageText() + " ";
                if (index != phones.size()) result += ",";
                index++;
            }
        }
        return result;
    }

    @JsonIgnore
    public void setBeneficiaryDetailsFromSupplier(Supplier supplier) {
        if (supplier.getPaymentMethod() == null)
            throw new RuntimeException("Supplies has no Payment Method!");
        setPaymentMethod(supplier.getPaymentMethod());
        setBeneficiaryType(ExpenseBeneficiaryType.SUPPLIER);
        setBeneficiaryId(supplier.getId());
        setBeneficiaryName(supplier.getName());
        setBeneficiaryMobileNumber(supplier.getMobileNumber());
        setBeneficiaryAccountName(supplier.getAccountName());
        setBeneficiaryAccountNumber(supplier.getAccountNumber());
        setBeneficiaryIban(supplier.getIban());
        setInternational(supplier.getInternational());
        setSwift(supplier.getSwift());
        setAddress(supplier.getAddress());
    }

    public boolean isInvoiceMessingOrTaxInvoiceMessing() {
        Expense expenseToUse = getExpense();

        if (expenseToUse == null || expenseToUse.getRequireInvoice() == null) return false;
        if (expenseToUse.getRequireInvoice().equals(Boolean.FALSE)) return false;

        Attachment invoice = AttachmentHelper.getRequestAttachment(this, AttachmentTag.EXPENSE_REQUEST_INVOICE.toString());
        if (invoice == null) return true;

        if (getTaxable() == null) return true;
        if (getTaxable().equals(Boolean.FALSE)) return false;

        if (getVatAmount() == null) return true;
        if (getAttachedInvoiceIsValidVatInvoice() == null) return true;
        if (getAttachedInvoiceIsValidVatInvoice().equals(Boolean.TRUE)) return false;

        Attachment vatInvoice = AttachmentHelper.getRequestAttachment(this, AttachmentTag.EXPENSE_REQUEST_VAT_INVOICE.toString());
        if (vatInvoice == null) return true;

        return false;
    }

    @Transient
    private CooQuestion.QuestionedPage cooQuestionedPage;

    public CooQuestion.QuestionedPage getCooQuestionedPage() {
        return cooQuestionedPage;
    }

    public void setCooQuestionedPage(CooQuestion.QuestionedPage cooQuestionedPage) {
        this.cooQuestionedPage = cooQuestionedPage;
    }

    @JsonIgnore
    public List<CooQuestion> getCooQuestions() {
        if (this.cooQuestionedPage == null) return new ArrayList();
        return Setup.getRepository(CooQuestionRepository.class).findByRelatedEntityAndQuestionedPage(this, this.cooQuestionedPage);
    }

    @JsonIgnore
    public boolean isAllQuestionsAnswered() {
        if (this.cooQuestionedPage == null) return false;

        List<CooQuestion> cooQuestions = getCooQuestions();
        return !cooQuestions.isEmpty() && !cooQuestions.stream().anyMatch(q -> !q.isAnswered());
    }

    @JsonIgnore
    public boolean isOneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;
        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() >= 1l;
    }

    @JsonIgnore
    public boolean isNoneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;
        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() == 0l;
    }

    @JsonIgnore
    public String getSupplier() {
        return beneficiaryType == null || getBeneficiaryId() == null ?
                "" : getBenefeciaryInfo();
    }

    @JsonIgnore
    public Long getExpenseTransaction () {
        if(getExpensePayment() == null) return null;
        if(getExpensePayment().getTransaction() == null) return null;

        return Setup.getRepository(ExpensePaymentRepository.class)
                .getExpensePaymentTransactionId(getExpensePayment().getId());
    }

    public Long getFlightTicketId() {
        return flightTicketId;
    }

    public void setFlightTicketId(Long flightTicketId) {
        this.flightTicketId = flightTicketId;
    }

    public Double getElectricity() { return electricity; }

    public void setElectricity(Double electricity) { this.electricity = electricity; }

    public Double getWater() { return water; }

    public void setWater(Double water) { this.water = water; }

    public Double getHousing() { return housing; }

    public void setHousing(Double housing) { this.housing = housing; }

    public Double getSewage() { return sewage; }

    public void setSewage(Double sewage) { this.sewage = sewage; }

    public Long getReferredMaidId() { return referredMaidId; }

    public void setReferredMaidId(Long referredMaidId) { this.referredMaidId = referredMaidId; }

    public Boolean getDeleted() {return deleted;}

    public void setDeleted(Boolean deleted) {this.deleted = deleted;}
}
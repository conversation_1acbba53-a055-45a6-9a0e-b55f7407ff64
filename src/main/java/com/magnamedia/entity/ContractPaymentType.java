package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.extra.DiscountsWithVatHelper;
import com.magnamedia.service.CalculateDiscountsWithVatService;
import org.hibernate.annotations.ColumnDefault;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
@Entity
public class ContractPaymentType extends AbstractPaymentTypeConfig {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ContractPaymentTerm contractPaymentTerm;

    @Transient
    private Integer additionalDiscountedPaymentsCount;

    @Transient
    private double vatParameterValue = 0.0;

    @Column
    @ColumnDefault("false")
    private Boolean forceDedicatedDd = false;

    @Column(columnDefinition = "boolean default false")
    private boolean postponedDdGenerated = false;

    public ContractPaymentType() {
    }

    public ContractPaymentType(
            PicklistItem type, String description, double amount, Double discount,
            Integer discountEffectiveAfter, Integer startsOn, Integer recurrence, Integer
                    endsAfter, Boolean affectsPaidEndDate, Boolean affectedByAdditionalDiscount,
            ContractPaymentTerm contractPaymentTerm, PicklistItem subType) {

        super(type, description, amount, discount, discountEffectiveAfter, startsOn, recurrence, endsAfter, affectsPaidEndDate, affectedByAdditionalDiscount, subType);
        this.contractPaymentTerm = contractPaymentTerm;
    }

    public ContractPaymentType(PaymentTypeConfig paymentTypeConfig) {
        this(paymentTypeConfig.getType(), paymentTypeConfig.getDescription(), paymentTypeConfig.getAmount(),
                paymentTypeConfig.getDiscount(), paymentTypeConfig.getDiscountEffectiveAfter(),
                paymentTypeConfig.getStartsOn(), paymentTypeConfig.getRecurrence(),
                paymentTypeConfig.getEndsAfter(), paymentTypeConfig.getAffectsPaidEndDate(), paymentTypeConfig.getAffectedByAdditionalDiscount(), null, paymentTypeConfig.getSubType());
    }

    public ContractPaymentTerm getContractPaymentTerm() {
        return contractPaymentTerm;
    }

    public void setContractPaymentTerm(ContractPaymentTerm contractPaymentTerm) {
        this.contractPaymentTerm = contractPaymentTerm;
    }

    public List<ContractPayment> generateContractPayments(DateTime fromDate, DateTime toDate) {
        List<ContractPayment> contractPayments = new ArrayList<>();

        for (DateTime dateTime = fromDate.plusMonths(this.getStartsOn());
             (dateTime.getYear() < toDate.getYear() || (dateTime.getYear() == toDate.getYear()
                     && dateTime.getMonthOfYear() <= toDate.getMonthOfYear()));
             dateTime = dateTime.plusMonths(this.getRecurrence())) {
            
            DateTime cpDate = dateTime;

            if (this.getEndsAfter() != null && cpDate.isAfter(
                    fromDate.plusMonths(this.getEndsAfter())
                            .dayOfMonth().withMinimumValue().withTimeAtStartOfDay())) {
                break;
            }

            // ACC-3500#2.1
            if (cpDate.isAfter(fromDate)) {
                cpDate = cpDate.dayOfMonth().withMinimumValue().withTimeAtStartOfDay();
            }
            ContractPayment payment = generateContractPayment(cpDate);
            contractPayments.add(payment);
            Logger.getLogger(ContractPaymentType.class.getName()).info("Generated Payment: " + 
                    "Date: " + new LocalDate(payment.getDate().getTime()).toString("yyyy-MM-dd"));
            
            if (this.getRecurrence().equals(0)) break;
        }

        return contractPayments;
    }

    public ContractPayment generateContractPayment(DateTime cpDate) {

        ContractPayment payment = new ContractPayment();

        Map<String, Object> map = Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .getContractPaymentAmount(cpDate, this);
        payment.setAmount((Double) map.get("amount"));
        payment.setAdditionalDiscountAmount((Double) map.get("additionalDiscountAmount"));
        payment.setDate(cpDate.toDate());
        payment.setDescription(this.getDescription());
        payment.setAffectsPaidEndDate(this.getAffectsPaidEndDate());
        payment.setContractPaymentTerm(this.contractPaymentTerm);
        payment.setPaymentType(this.getType());
        payment.setSubType(this.getSubType());
        if (map.get("discount") != null)
            payment.setDiscountAmount((Double) map.get("discount"));

        if (map.containsKey("moreAdditionalDiscount")) {
            payment.setMoreAdditionalDiscount((Double) map.get("moreAdditionalDiscount"));
        }

        return payment;
    }

    public Boolean isForceDedicatedDd() { 
        return forceDedicatedDd != null && forceDedicatedDd; 
    }

    public void setForceDedicatedDd(Boolean forceDedicatedDd) { 
        this.forceDedicatedDd = forceDedicatedDd; 
    }

    public boolean isPostponedDdGenerated() { return postponedDdGenerated; }

    public void setPostponedDdGenerated(boolean postponedDdGenerated) { this.postponedDdGenerated = postponedDdGenerated; }

    public Integer getAdditionalDiscountedPaymentsCount() {

        // Initial
        if (this.additionalDiscountedPaymentsCount == null) {

            setAdditionalDiscountedPaymentsCount(getContractPaymentTerm().getAdditionalDiscountMonthsCount(this.getType().getCode())) ;
        }

        return additionalDiscountedPaymentsCount;
    }

    public void setAdditionalDiscountedPaymentsCount(Integer additionalDiscountedPaymentsCount) {
        this.additionalDiscountedPaymentsCount = additionalDiscountedPaymentsCount;
    }

    public Double getAdditionalDiscountAmountPerPayment() {
        return getContractPaymentTerm().getAdditionalDiscount() != null && getAdditionalDiscountedPaymentsCount() != 0 ?
                getContractPaymentTerm().getAdditionalDiscount() / getAdditionalDiscountedPaymentsCount() : 0D;
    }
}

package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.DirectDebitSignatureStatus;

import javax.persistence.*;



@Entity
public class DirectDebitSignature extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ContractPaymentTerm contractPaymentTerm;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private DirectDebitSignatureStatus signatureStatus = DirectDebitSignatureStatus.UNUSED;

    public ContractPaymentTerm getContractPaymentTerm() {
        return contractPaymentTerm;
    }

    @Column
    Integer usedNumber;

    @Column
    private String eid;

    @Column(columnDefinition = "boolean default false")
    boolean disable = false;

    @Transient
    private boolean newSignature = false;

    @Column(columnDefinition = "boolean default false")
    private boolean approvedOnce = false;

    private int SignatureOrder = 0;

    private int setOrder = 0;

    public void setContractPaymentTerm(ContractPaymentTerm contractPaymentTerm) {
        this.contractPaymentTerm = contractPaymentTerm;
    }

    public DirectDebitSignatureStatus getSignatureStatus() {
        return signatureStatus;
    }

    public void setSignatureStatus(DirectDebitSignatureStatus signatureStatus) {
        this.signatureStatus = signatureStatus;
    }

    public Integer getUsedNumber() {
        return usedNumber == null ? 0 : usedNumber;
    }

    public void setUsedNumber(Integer usedNumber) {
        this.usedNumber = usedNumber;
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public boolean isDisable() { return disable; }

    public void setDisable(boolean disable) { this.disable = disable; }

    public boolean isNewSignature() { return newSignature; }

    public void setNewSignature(boolean newSignature) { this.newSignature = newSignature; }

    public boolean isApprovedOnce() { return approvedOnce;}

    public void setApprovedOnce(boolean approvedOnce) { this.approvedOnce = approvedOnce; }

    @JsonIgnore
    public Attachment getSignatureAttachment(){
        return getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE);
    }
    // ACC-5812
    public int getSignatureOrder() { return SignatureOrder; }

    public void setSignatureOrder(int signatureOrder) { SignatureOrder = signatureOrder; }

    public int getSetOrder() { return setOrder; }

    public void setSetOrder(int setOrder) { this.setOrder = setOrder; }
}
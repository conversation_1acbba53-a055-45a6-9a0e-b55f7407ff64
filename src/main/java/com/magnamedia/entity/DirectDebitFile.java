package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.DirectDebitJsonSerializer;
import com.magnamedia.helper.ConcurrentModificationHelper;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.BankDirectDebitActivationRecordRepository;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.service.AttachmentService;
import com.magnamedia.service.DirectDebitService;
import org.hibernate.annotations.Where;
import org.hibernate.envers.NotAudited;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.util.*;
import java.util.logging.Logger;


/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Mar 5, 2019
 *         Jirra ACC-475 ACC-984
 */
@Entity
@Where(clause = "exists( select dd.id from DIRECTDEBITS dd where dd.id = DIRECT_DEBIT_ID  and dd.IS_DELETED = false)")
@Table(indexes = {
        @Index(columnList = "applicationId", name = "IDX_applicationId"),
        @Index(columnList = "ddaRefNo", name = "IDX_ddaRefNo", unique = false),
})
public class DirectDebitFile extends BaseEntity {

    private static final Logger logger = Logger.getLogger(DirectDebitFile.class.getName());
    public static final String FILE_TAG_DD_SIGNATURE = "direct_debit_signature";
    public static final String FILE_TAG_DD_ACTIVATION = "direct_debit_activation";

    @ManyToOne(optional = false)
    @JsonSerialize(using = DirectDebitJsonSerializer.class)
    private DirectDebit directDebit;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private DirectDebitFileStatus status = DirectDebitFileStatus.NOT_SENT;

    // SAL-1200
    @Column(length = 1000)
    private String rejectionReason;

    // ACC-1587 from here
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private DirectDebitStatus ddStatus = DirectDebitStatus.IN_COMPLETE;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitType ddFrequency;

    @Column(nullable = false)
    private String applicationId;

    @Column
    private String ddaRefNo;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitMethod ddMethod;

    @Column(nullable = false)
    @Temporal(TemporalType.DATE)
    private Date startDate;

    @Column(nullable = false)
    @Temporal(TemporalType.DATE)
    private Date expiryDate;

    @Column
    private String accountName;

    @Column(nullable = false)
    @Min(0)
    private Double amount;

    @Column
    private String eid;

    @Column
    private String ibanNumber;

    @Column
    private Date statusChangeDate;

    @JsonIgnore
    @NotAudited
    @Transient
    private List<BankDirectDebitActivationRecord> records;

    @Column
    private String bankName;

    @Column
    @Temporal(TemporalType.TIMESTAMP)
    private Date presentmentDate;

    @Column
    private Boolean confirmedBankInfo = Boolean.FALSE;

    @Column
    @Lob
    private String notes;
    // ACC-1587 to here

    // acc-1595
    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitRejectCategory rejectCategory;

    @Column(columnDefinition = "int default 0")
    private int trialNumber = 0; // we will use this flag to detect how many files i should clone when dd type B gets rejected

    //Jirra ACC-1721
    @Column(columnDefinition = "boolean default false")
    private Boolean forBouncingPayment;

    // acc-1810
    @Column(columnDefinition = "boolean default false")
    private Boolean needAccountantReConfirmation;

    @Transient
    private Boolean isFromAccountantAction = false;

    //Jirra ACC-2059
    @Column(columnDefinition = "boolean default false")
    private boolean ddDataEntryNotificationSent = false;

    @Column
    private Boolean ignoreDDRejectionFlow = false;

    //Jirra ACC-2571
    @Column(columnDefinition = "boolean default false")
    private boolean downloadedByRPA = false;

    //Jirra ACC-2659
    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitCancellationToDoReason cancellationReason;

    @Column
    private Date requestedToCancelDate;

    @Column
    private Long senderForCancellation;

    @Column
    private Long whoRequestedToCancellDD;

    @Column
    private Date sendForCancellationDate;

    @Column
    private Date cancellationDate;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private DirectDebitSignature directDebitSignature;

    @Column
    private Date lastDataCorrectionDate;

    @Column
    private Long manualDDFToSendId;

    @Column
    private Long ddfBatchForRpaId;

    //Jirra ACC-3639
    @Transient
    private boolean doNotCancelExpiredDirectly = false;

    @Transient
    private DirectDebitFileCancellationTrigger cancellationTrigger = DirectDebitFileCancellationTrigger.AUTOMATIC;

    @Transient
    private String cancelingRequester;

    @Transient
    private Date lastStatusModificationDate;

    @Transient
    private boolean forceUpdate = false;

    public DirectDebit getDirectDebit() {
        return directDebit;
    }

    public void setDirectDebit(DirectDebit directDebit) {
        this.directDebit = directDebit;
    }

    public Long getManualDDFToSendId() { return manualDDFToSendId; }

    public void setManualDDFToSendId(Long manualDDFToSendId) { this.manualDDFToSendId = manualDDFToSendId; }

    public Long getDdfBatchForRpaId() { return ddfBatchForRpaId; }

    public void setDdfBatchForRpaId(Long ddfBatchForRpaId) { this.ddfBatchForRpaId = ddfBatchForRpaId; }

    public DirectDebitFileStatus getStatus() {
        return status;
    }

    public void setStatus(DirectDebitFileStatus status) {
        this.status = status;
    }

    public DirectDebitStatus getDdStatus() {
        return ddStatus;
    }

    public void setDdStatus(DirectDebitStatus ddStatus) {
        this.ddStatus = ddStatus;
    }

    public String getPreviousDDStatus() {
        HistorySelectQuery<DirectDebitFile> historySelectQuery = new HistorySelectQuery(DirectDebitFile.class);
        historySelectQuery.filterBy("id", "=", this.getId());

        historySelectQuery.filterByChanged("ddStatus");

        historySelectQuery.sortBy("lastModificationDate", false, true);
        historySelectQuery.setLimit(2);

        List<DirectDebitFile> oldDDFs = historySelectQuery.execute();

        if (oldDDFs == null || oldDDFs.size() < 2) return null;

        return oldDDFs.get(1).getDdStatus().toString();
    }

    public DirectDebitType getDdFrequency() {
        return ddFrequency;
    }

    public void setDdFrequency(DirectDebitType ddFrequency) {
        this.ddFrequency = ddFrequency;
    }

    public String getDdaRefNo() {
        return ddaRefNo;
    }

    public void setDdaRefNo(String ddaRefNo) {
        this.ddaRefNo = ddaRefNo;
    }

    public DirectDebitMethod getDdMethod() {
        return ddMethod;
    }

    public void setDdMethod(DirectDebitMethod ddMethod) {
        this.ddMethod = ddMethod;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public String getIbanNumber() {
        return ibanNumber;
    }

    public void setIbanNumber(String ibanNumber) {
        this.ibanNumber = ibanNumber;
    }

    public Date getStatusChangeDate() {
        return statusChangeDate;
    }

    public void setStatusChangeDate(Date statusChangeDate) {
        this.statusChangeDate = statusChangeDate;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public List<BankDirectDebitActivationRecord> getRecords() {
        if (records == null || records.isEmpty()) {
            records = Setup.getRepository(BankDirectDebitActivationRecordRepository.class)
                    .findByDirectDebitFileId(getId());
        }
        return records;
    }

    public void setRecords(List<BankDirectDebitActivationRecord> records) {
        this.records = records;
    }

    public String getRejectionReason() {
        return rejectionReason;
    }

    public void setRejectionReason(String rejectionReason) {
        this.rejectionReason = rejectionReason;
    }

    public String getType() {
        return (this.ddMethod != null ? this.ddMethod.getLabel() : "") + " " + (this.ddFrequency != null ? this.ddFrequency.getLabel() : "");
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public Date getPresentmentDate() {
        return presentmentDate;
    }

    public void setPresentmentDate(Date presentmentDate) {
        this.presentmentDate = presentmentDate;
    }

    public Boolean getConfirmedBankInfo() {
        return confirmedBankInfo == null ? false : confirmedBankInfo;
    }

    public void setConfirmedBankInfo(Boolean confirmedBankInfo) {
        this.confirmedBankInfo = confirmedBankInfo;
    }

    // ACC-1588
    public void copyDDInfo(DirectDebit directDebit, Boolean withAttachments) {
        setStartDate(directDebit.getStartDate());

        if (directDebit.getCategory().equals(DirectDebitCategory.A)) {
            setDdFrequency(DirectDebitType.ONE_TIME);
            setDdaExpiryDate(directDebit);
        } else {
            setDdFrequency(this.ddMethod.equals(DirectDebitMethod.AUTOMATIC) ?
                    DirectDebitType.MONTHLY : DirectDebitType.DAILY);
            setExpiryDate(directDebit.getExpiryDate());
        }

        setAmount(directDebit.getAmount());
        copyDDBankInfo(directDebit, withAttachments);
    }

    // ACC-1588
    public void copyDDBankInfo(DirectDebit directDebit, Boolean withAttachments) {
        logger.info("copying bank info into files DD ID " + directDebit.getId());

        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);

        this.setAccountName(directDebit.getAccountName());
        this.setBankName(directDebit.getBankName());
        this.setIbanNumber(directDebit.getIbanNumber());
        this.setEid(directDebit.getEid());
        this.setConfirmedBankInfo(directDebit.getConfirmedBankInfo());
        this.setDdStatus(directDebit.getNonCompletedInfo() ? DirectDebitStatus.IN_COMPLETE :
                (this.getConfirmedBankInfo() ? DirectDebitStatus.PENDING :
                        DirectDebitStatus.PENDING_DATA_ENTRY));

        List<String> attachmentTags = Arrays.asList(
                ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME,
                ContractPaymentTermController.FILE_TAG_BANK_INFO_EID,
                ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN,
                ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR);

        if (withAttachments) {
            //ACC-4657
            if (directDebit.getAttachments().stream()
                    .anyMatch(a -> a.getTag().equals(ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR))) {

                this.getAttachments().stream()
                        .filter(a -> Arrays.asList(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID,
                                ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN,
                                ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME).contains(a.getTag()))
                        .forEach(att -> attachementRepository.delete(att));
            }

            for (Attachment att : directDebit.getAttachments()) {
                if (attachmentTags.contains(att.getTag())) {
                    Attachment previousAttachment = this.getAttachment(att.getTag());
                    if (previousAttachment != null &&
                        Arrays.asList(
                            ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME,
                            ContractPaymentTermController.FILE_TAG_BANK_INFO_EID,
                            ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN).contains(att.getTag())) {

                        attachementRepository.delete(previousAttachment);
                    }

                    this.addAttachment(att);
                }
            }
        }
    }

    public void setDdaExpiryDate(DirectDebit dd) {
        if(!dd.getCategory().equals(DirectDebitCategory.A)) return;
        this.setExpiryDate(new DateTime(this.getStartDate()).plusMonths(directDebit.getDdConfiguration().getDdaTimeFrame()).toDate());
    }

    //ACC-1588
    @BeforeInsert
    public void generateApplicationId() {
        if (this.getApplicationId() != null) return;

        this.generateNewApplicationIdForExistingDDF(0);
    }

    // ACC-2724
    // ACC-8561
    public void generateNewApplicationIdForExistingDDF(int serial) {
        ConcurrentModificationHelper.emptyExpiredKeys();

        DirectDebitFileRepository directDebitFileRepository = Setup.getRepository(DirectDebitFileRepository.class);
        Contract contract = this.getDirectDebit().getContractPaymentTerm().getContract();
        String lockKey = this.getDdMethod().getLabel().substring(0, 1)
                + this.getDdFrequency().getLabel().substring(0, 1) + "-" + contract.getId();
        logger.info("lockKey value : " + lockKey);
        synchronized (lockKey.intern()) {
            Integer serialNumber = 0;
            if (ConcurrentModificationHelper.isExistInGlobalData(lockKey)) {
                serialNumber = (Integer) ((Map<String, Object>) ConcurrentModificationHelper.getValueFromGlobalData(lockKey)).get("serialNumber");
                logger.info("serialNumber exist in global shared data : " + serialNumber);
                ConcurrentModificationHelper.unLockGlobalDataKey(lockKey);
            } else {
                List<DirectDebitFile> latestDDFs = directDebitFileRepository.findByDdMethodAndDdFrequencyAndDirectDebit_ContractPaymentTerm_ContractOrderByApplicationIdDesc(
                        this.getDdMethod(), this.getDdFrequency(), contract);

                DirectDebitFile latestDDF = null;
                if (latestDDFs != null && !latestDDFs.isEmpty()) {
                    latestDDF = latestDDFs.get(0);
                }

                if (latestDDF != null) {
                    serialNumber = Integer.valueOf(latestDDF.getApplicationId().split("-")[0].substring(2));
                }
            }

            Map<String, Object> lockObject = new HashMap<>();
            lockObject.put("serialNumber", ++serialNumber);
            lockObject.put("deletedDate", new DateTime().plusMinutes(30));
            ConcurrentModificationHelper.lockGlobalDataNewKey(lockKey, lockObject);

            this.setApplicationId(this.getDdMethod().getLabel().substring(0, 1)
                    + this.getDdFrequency().getLabel().substring(0, 1) + String.format("%04d", serialNumber)
                    + "-" + contract.getId());

            this.setStatusChangeDate(new Date());
        }
    }

    @BeforeUpdate
    public void chechStatusChanged() {
        HistorySelectQuery query = new HistorySelectQuery(DirectDebitFile.class);

        query.filterBy("id", "=", this.getId());
        query.sortBy("lastModificationDate", false, true);
        query.filterByChanged("ddStatus");
        query.setLimit(1);

        List<DirectDebitFile> oldDDFs = query.execute();

        if (oldDDFs != null && !oldDDFs.isEmpty() && !oldDDFs.get(0).getDdStatus().equals(this.ddStatus))
            this.setStatusChangeDate(new Date());
    }

    @Basic(fetch = FetchType.LAZY)
    public Date getResultDate() {
        if (!getRecords().isEmpty()) {
            Date a = getRecords().stream().map(x -> x.getCreationDate()).max(Date::compareTo).get();
            return a;
        } else return null;
    }

    public DirectDebitRejectCategory getRejectCategory() {
        return rejectCategory;
    }

    public void setRejectCategory(DirectDebitRejectCategory rejectCategory) {
        this.rejectCategory = rejectCategory;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
    //Jirra ACC-1435
//    @BeforeUpdate
//    public void preUpdate() {
//         check if there is a previous approved direct debit file
//        if ((this.getStatus() == DirectDebitFileStatus.APPROVED || this.getStatus() == DirectDebitFileStatus.SENT)
//                && Setup.getRepository(DirectDebitFileRepository.class).existsByDirectDebitAndStatusInAndIdNot(
//                this.getDirectDebit(),
//                Arrays.asList(DirectDebitFileStatus.APPROVED, DirectDebitFileStatus.SENT), this.getId()))
//            throw new RuntimeException("There is a previous approved or sent signature");
//    }

    //Jirra ACC-1435
//    @AfterUpdate
//    public void checkStatus() {
//        checkAllDirectDebitFilesStatus();
//    }

    //Jirra ACC-1435
//    public void checkAllDirectDebitFilesStatus() {
//        if (!this.getStatus().equals(DirectDebitFileStatus.REJECTED)) {
//            return;
//        }
//        HistorySelectQuery<DirectDebitFile> query = new HistorySelectQuery<>(DirectDebitFile.class);
//        query.filterBy("id", "=", this.getId());
//        query.sortBy("lastModificationDate", false);
//        query.setLimit(1);
//        List<DirectDebitFile> DDFs = query.execute();
//        if (DDFs.size() > 0 && DDFs.get(0).getStatus().equals(DirectDebitFileStatus.REJECTED)) {
//            return;
//        }
//        DirectDebit dd = Setup.getRepository(DirectDebitRepository.class).findOne(this.getDirectDebit().getId());
//        for (DirectDebitFile ddf : dd.getDirectDebitFiles()) {
//            if (!ddf.getStatus().equals(DirectDebitFileStatus.REJECTED)) {
//                return;
//            }
//        }
//        DirectDebitMessagesJob.sendDDRejectedMessage(dd);
//    }


    public void setTrialNumber(int trialNumber) {
        this.trialNumber = trialNumber;
    }

    public int getTrialNumber() {
        return trialNumber;
    }

    public Boolean getForBouncingPayment() {
        return forBouncingPayment != null && forBouncingPayment;
    }

    public void setForBouncingPayment(Boolean forBouncingPayment) {
        this.forBouncingPayment = forBouncingPayment;
    }

    public DirectDebitFile clone(int trialNumber) {
        return this.clone(trialNumber, null);
    }

    public DirectDebitFile clone(int trialNumber, Date startDate) {
        DirectDebitFile ddf = new DirectDebitFile();
        ddf.setAmount(this.amount);
        ddf.setDirectDebitSignature(this.getDirectDebitSignature());
        
        for (Attachment attachment : this.getAttachments()) {
            if (!attachment.getTag().equalsIgnoreCase(DirectDebitFile.FILE_TAG_DD_ACTIVATION)) {
                ddf.addAttachment(attachment);
            }
        }

        // ACC-2550
        ddf.setDdFrequency(this.ddFrequency.equals(DirectDebitType.WEEKLY) ? DirectDebitType.DAILY : this.ddFrequency);
        ddf.setAccountName(this.accountName);
        ddf.setEid(this.eid);
        ddf.setIbanNumber(this.ibanNumber);
        ddf.setBankName(this.bankName);
        ddf.setDdMethod(this.ddMethod);
        ddf.setDirectDebit(this.directDebit);
        ddf.setExpiryDate(this.expiryDate);
        ddf.setPresentmentDate(this.presentmentDate);
        ddf.setStartDate(startDate != null ? startDate : this.startDate);
        ddf.setConfirmedBankInfo(this.confirmedBankInfo);
        ddf.setTrialNumber(trialNumber);
        ddf.setStatus(DirectDebitFileStatus.NOT_SENT);
        ddf.setDdStatus(DirectDebitStatus.PENDING);

        DirectDebitFileRepository repository = Setup.getRepository(DirectDebitFileRepository.class);

        repository.save(ddf);
        ddf = repository.findOne(ddf.getId());

        Setup.getApplicationContext().getBean(DirectDebitService.class).createDirectDebitActivationAttachmentIfNotExist(ddf);

        return repository.findOne(ddf.getId());
    }

    public DirectDebitFile cloneToNewDD(DirectDebit directDebit, int trialNumber) {
        DirectDebitFile ddf = clone(trialNumber);
        ddf.setDirectDebit(directDebit);
        return ddf;
    }

    public DirectDebitFile cloneWithNewStartDate(int trialNumber) {
        Date newStartDate = new LocalDate(this.startDate).plusMonths(2).withDayOfMonth(1).toDate();
        DirectDebitFile ddf = clone(trialNumber, newStartDate);

        return ddf;
    }

    public Boolean getNeedAccountantReConfirmation() {
        return needAccountantReConfirmation;
    }

    public void setNeedAccountantReConfirmation(Boolean needAccountantReConfirmation) {
        this.needAccountantReConfirmation = needAccountantReConfirmation;
    }

    public boolean isDdDataEntryNotificationSent() {
        return ddDataEntryNotificationSent;
    }

    public void setDdDataEntryNotificationSent(boolean ddDataEntryNotificationSent) {
        this.ddDataEntryNotificationSent = ddDataEntryNotificationSent;
    }

    public Boolean getIgnoreDDRejectionFlow() {
        return ignoreDDRejectionFlow;
    }

    public void setIgnoreDDRejectionFlow(Boolean ignoreDDRejectionFlow) {
        this.ignoreDDRejectionFlow = ignoreDDRejectionFlow;
    }

    public boolean isDownloadedByRPA() {
        return downloadedByRPA;
    }

    public void setDownloadedByRPA(boolean downloadedByRPA) {
        this.downloadedByRPA = downloadedByRPA;
    }

    @Transient
    @JsonIgnore
    public boolean isOldByApplicationId() {
        return this.getApplicationId().startsWith("CONTX-") || this.getApplicationId().startsWith("contx") || this.getApplicationId().startsWith("Contr-") || this.getApplicationId().startsWith("Conta-");
    }

    public Boolean getIsFromAccountantAction() {
        return isFromAccountantAction;
    }

    public void setIsFromAccountantAction(Boolean isFromAccountantAction) {
        this.isFromAccountantAction = isFromAccountantAction;
    }

    public DirectDebitCancellationToDoReason getCancellationReason() {
        return cancellationReason;
    }

    public void setCancellationReason(DirectDebitCancellationToDoReason cancellationReason) {
        this.cancellationReason = cancellationReason;
    }

    public Date getRequestedToCancelDate() {
        return requestedToCancelDate;
    }

    public void setRequestedToCancelDate(Date requestedToCancelDate) {
        this.requestedToCancelDate = requestedToCancelDate;
    }

    public Long getSenderForCancellation() {
        return senderForCancellation;
    }

    public void setSenderForCancellation(Long senderForCancellation) {
        this.senderForCancellation = senderForCancellation;
    }

    public Long getWhoRequestedToCancellDD() {
        return whoRequestedToCancellDD;
    }

    public void setWhoRequestedToCancellDD(Long whoRequestedToCancellDD) {
        this.whoRequestedToCancellDD = whoRequestedToCancellDD;
    }

    public Date getSendForCancellationDate() {
        return sendForCancellationDate;
    }

    public void setSendForCancellationDate(Date sendForCancellationDate) {
        this.sendForCancellationDate = sendForCancellationDate;
    }
    
    public boolean isDoNotCancelExpiredDirectly() {
        return doNotCancelExpiredDirectly;
    }

    public void setDoNotCancelExpiredDirectly(boolean doNotCancelExpiredDirectly) {
        this.doNotCancelExpiredDirectly = doNotCancelExpiredDirectly;
    }
    
    public Date getCancellationDate() {
        return cancellationDate;
    }

    public void setCancellationDate(Date cancellationDate) {
        this.cancellationDate = cancellationDate;
    }

    public DirectDebitFileCancellationTrigger getCancellationTrigger() {
        return cancellationTrigger;
    }

    public void setCancellationTrigger(DirectDebitFileCancellationTrigger cancellationTrigger) {
        this.cancellationTrigger = cancellationTrigger;
    }

    public Date getLastDataCorrectionDate() {
        return lastDataCorrectionDate;
    }

    public void setLastDataCorrectionDate(Date lastDataCorrectionDate) {
        this.lastDataCorrectionDate = lastDataCorrectionDate;
    }

    //Jirra ACC-2744
    @JsonIgnore
    public List<Attachment> getSecuredAttachments() {
        return Setup.getApplicationContext()
                .getBean(AttachmentService.class)
                .getDdfSecuredAttachments(super.getAttachments());
    }

    public boolean hasDdForm() {
        return getAttachment(FILE_TAG_DD_ACTIVATION) != null;
    }

    public DirectDebitSignature getDirectDebitSignature() { return directDebitSignature; }

    public void setDirectDebitSignature(DirectDebitSignature directDebitSignature) {
        this.directDebitSignature = directDebitSignature;
    }

    @JsonIgnore
    public Attachment getSignatureAttachment(){
        return directDebitSignature == null ? null : directDebitSignature.getSignatureAttachment();
    }
    
    public String getCancelingRequester() {
        return cancelingRequester;
    }

    public void setCancelingRequester(String cancelingRequester) {
        this.cancelingRequester = cancelingRequester;
    }

    public Date getLastStatusModificationDate() {
        return lastStatusModificationDate;
    }

    public void setLastStatusModificationDate(Date lastStatusModificationDate) {
        this.lastStatusModificationDate = lastStatusModificationDate;
    }

    public boolean isForceUpdate() {
        return forceUpdate;
    }

    public void setForceUpdate(boolean forceUpdate) {
        this.forceUpdate = forceUpdate;
    }
}

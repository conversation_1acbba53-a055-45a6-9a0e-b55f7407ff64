package com.magnamedia.entity.OnlineCardStatement;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.serializer.IdOnlySerializer;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;


/**
 * <AUTHOR> <PERSON>hfoud
 **/

// ACC-5587
@Entity
public class OnlineCardStatementTransaction extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private OnlineCardStatementRecord onlineCardStatementRecord;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private Payment payment;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private Transaction transaction;

    public OnlineCardStatementRecord getOnlineCardStatementRecord() { return onlineCardStatementRecord; }

    public void setOnlineCardStatementRecord(OnlineCardStatementRecord onlineCardStatementRecord) {
        this.onlineCardStatementRecord = onlineCardStatementRecord;
    }

    public Payment getPayment() { return payment; }

    public void setPayment(Payment payment) { this.payment = payment; }

    public Transaction getTransaction() { return transaction; }

    public void setTransaction(Transaction transaction) { this.transaction = transaction; }
}

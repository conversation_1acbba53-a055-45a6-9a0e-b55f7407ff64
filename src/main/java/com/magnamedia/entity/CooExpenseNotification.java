package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;

@Entity
@Where(clause = "EXISTS(SELECT 1 FROM EXPENSEREQUESTTODOS E WHERE E.ID = EXPENSE_REQUEST_TODO_ID AND E.DELETED = 0)")
public class CooExpenseNotification extends BaseEntity {
    @Lob
    private String text;

    @Column(columnDefinition = "boolean default false")
    private Boolean hidden = false;

    @ManyToOne
    private ExpenseRequestTodo expenseRequestTodo;

    @Lob
    String reason;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public ExpenseRequestTodo getExpenseRequestTodo() {
        return expenseRequestTodo;
    }

    public void setExpenseRequestTodo(ExpenseRequestTodo expenseRequestTodo) {
        this.expenseRequestTodo = expenseRequestTodo;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Boolean getHidden() {
        return hidden;
    }

    public void setHidden(Boolean hidden) {
        this.hidden = hidden;
    }
}

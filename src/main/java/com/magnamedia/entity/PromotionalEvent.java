package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 25, 2020
 *         Jirra ACC-1435
 */
@Entity
public class PromotionalEvent extends BaseEntity {

    @Label
    @Column(nullable = false, unique = true)
    private String name;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem location;

    @Column
    private Boolean active = true;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public PicklistItem getLocation() {
        return location;
    }

    public void setLocation(PicklistItem location) {
        this.location = location;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

}

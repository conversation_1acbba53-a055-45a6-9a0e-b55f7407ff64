package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR> on 2017-07-19
 *
 */
@Entity
public class TicketComment extends BaseEntity {

	@Column(length = 1000)
	private String comment;

	@ManyToOne(fetch = FetchType.LAZY)
	@JsonSerialize(using = CustomIdLabelSerializer.class)
	protected TicketRequest ticketrequest;

	public TicketRequest getTicketrequest() {
		return ticketrequest;
	}

	public void setTicketrequest(TicketRequest ticketrequest) {
		this.ticketrequest = ticketrequest;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}
}

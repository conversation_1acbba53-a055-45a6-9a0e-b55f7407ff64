package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.IdLabelCodeOwner;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.*;
import com.magnamedia.mastersearch.Searchable;
import com.magnamedia.mastersearch.SearchableField;
import com.magnamedia.module.type.*;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */

@Entity
@Searchable(showunName = "Expense", order = 4, permissionCode = "Expenses")
public class Expense extends BaseEntity implements Serializable, IdLabelCodeOwner {

    @Column
    @SearchableField(headerName = "Code", order = 1)
    private String code;

    @SearchableField(headerName = "Name", order = 2)
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem pnlExpenseType;

    private Long adjustedExpense;

    @Column
    private Boolean isSecure;

    @Column(columnDefinition = "boolean default false")
    private Boolean deleted = false;

    @Column(columnDefinition = "boolean default false")
    private boolean disabled = false;

    @Transient
    private String nameLabel;

    @Transient
    private String codeLabel;

    //self join for parent and childrens
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parentExpense")
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense parent;

    @OneToMany(mappedBy = "parent", fetch = FetchType.LAZY)
    @JsonSerialize(using = ExpenseChildrenSerializer.class)
    private List<Expense> children = new ArrayList<>();

    @Transient
    private boolean isMatchedSearchFilter;

    @Label
    @Column
    private String caption;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User manager;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem requestedFrom;

    @Column
    private Boolean requireAttachment;

    @Column
    private Boolean requireInvoice;

    //Approval Flow
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "EXPENSE_REQUESTORS",
            joinColumns = @JoinColumn(
                    name = "EXPENSE_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "USER_ID",
                    referencedColumnName = "ID")
    )
    @JsonSerialize(using = IdLabelListSerializer.class)
    private List<User> requestors = new ArrayList<>();


    @Column
    @Enumerated(EnumType.STRING)
    private ExpenseApprovalMethod approvalMethod;

    @Column
    @Enumerated(EnumType.STRING)
    private ExpenseApproveHolderType approveHolderType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User approveHolder;

    @Column
    private String approveHolderEmail;

    @Column
    private Double limitForApproval;

    @Column
    private Boolean isLimitedCOO;

    @Column
    private Double LimitCOO;


    //  ExpensePaymentDetails
    @ElementCollection(fetch = FetchType.LAZY)
    @CollectionTable(name = "EXPENSE_PAYMENTS_METHODS",
            joinColumns = @JoinColumn(name = "id"))
    @Enumerated(EnumType.STRING)
    private Set<ExpensePaymentMethod> paymentMethods;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem salaryAdditionType;

    @Column
    private Double defaultAmount;

    @Column
    @Enumerated(EnumType.STRING)
    private ExpenseBeneficiaryType beneficiaryType;


    @ManyToMany(fetch = FetchType.LAZY)
//    @ManyToMany(targetEntity = Supplier.class, cascade = {CascadeType.ALL})
//    @JoinTable(name = "EXPENSESUPPLIER", joinColumns = { @JoinColumn(name = "person_id") },
//            inverseJoinColumns = { @JoinColumn(name = "color_id") })
    @JsonSerialize(using = SupplierIdNameLabelListSerializer.class)
    private List<Supplier> suppliers = new ArrayList<>();


    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private ExpenseNotification notification;

    //statement parser
    @Column
    private Boolean autoDeducted;

    @ElementCollection
    @NotAudited
    @CollectionTable(name = "EXPENSE_NAMES", joinColumns = @JoinColumn(name = "expense_id"))
    private List<String> namesInFinancialStatements = new ArrayList<>();

    //related to

    @OneToMany(mappedBy = "expense", fetch = FetchType.LAZY)
    @JsonSerialize(using = ExpenseRelatedToSerializer.class)
    private List<ExpenseRelatedTo> relatedTos = new ArrayList<>();


    @Column
    @Enumerated(EnumType.STRING)
    private LoanType loanType;

    @Column
    private Boolean allowSubExpense;

    @Column
    private Boolean allowToAddLoan;

    @Column
    private String subExpenseLabel;

    @Column
    private Boolean paidOnTheSpotCash;

    @Column(columnDefinition = "boolean default false")
    private boolean hideRequestRefundButton = false;

    // ACC-3286
    @Column
    private Boolean paidOnTheSpotCreditCard;

    // ACC-8074
    @Column
    @Lob
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdNameSerializer.class)
    private Bucket fromCashBucket;

    // ACC-3286
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdNameSerializer.class)
    private Bucket fromCreditCardBucket;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "EXPENSE_FROM_CASH_BUCKETS",
            joinColumns = @JoinColumn(
                    name = "EXPENSE_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "BUCKET_ID",
                    referencedColumnName = "ID"))
    @JsonSerialize(using = IdNameListSerializer.class)
    private List<Bucket> fromCashBuckets;

    // ACC-6275
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "EXPENSE_FROM_CREDIT_CARD_BUCKETS",
            joinColumns = @JoinColumn(
                    name = "EXPENSE_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "BUCKET_ID",
                    referencedColumnName = "ID"))
    @JsonSerialize(using = IdNameListSerializer.class)
    private List<Bucket> fromCreditCardBuckets;

    @Column(columnDefinition = "boolean default false")
    private boolean allowToAddSupplierToExpenseRequest = false;

    public Bucket getFromCashBucket() { return fromCashBucket; }

    public void setFromCashBucket(Bucket fromCashBucket) { this.fromCashBucket = fromCashBucket; }

    public Bucket getFromCreditCardBucket() { return fromCreditCardBucket; }

    public void setFromCreditCardBucket(Bucket fromCreditCardBucket) { this.fromCreditCardBucket = fromCreditCardBucket; }

    public List<Bucket> getFromCashBuckets() {
        return fromCashBuckets;
    }

    public void setFromCashBuckets(List<Bucket> fromCashBuckets) {
        this.fromCashBuckets = fromCashBuckets;
    }

    public List<Bucket> getFromCreditCardBuckets() {
        return fromCreditCardBuckets;
    }

    public void setFromCreditCardBuckets(List<Bucket> fromCreditCardBuckets) {
        this.fromCreditCardBuckets = fromCreditCardBuckets;
    }

    public boolean isAllowToAddSupplierToExpenseRequest() {
        return allowToAddSupplierToExpenseRequest;
    }

    public void setAllowToAddSupplierToExpenseRequest(boolean allowToAddSupplierToExpenseRequest) {
        this.allowToAddSupplierToExpenseRequest = allowToAddSupplierToExpenseRequest;
    }

    public Boolean getPaidOnTheSpotCash() {
        return paidOnTheSpotCash != null && paidOnTheSpotCash;
    }

    public void setPaidOnTheSpotCash(Boolean paidOnTheSpotCash) {
        this.paidOnTheSpotCash = paidOnTheSpotCash;
    }

    public Boolean getPaidOnTheSpotCreditCard() {
        return paidOnTheSpotCreditCard != null && paidOnTheSpotCreditCard;
    }

    public void setPaidOnTheSpotCreditCard(Boolean paidOnTheSpotCreditCard) {
        this.paidOnTheSpotCreditCard = paidOnTheSpotCreditCard;
    }

    public Boolean getAutoDeducted() {
        return autoDeducted;
    }

    public void setAutoDeducted(Boolean autoDeducted) {
        this.autoDeducted = autoDeducted;
    }

    public LoanType getLoanType() {
        return loanType;
    }

    public void setLoanType(LoanType loanType) {
        this.loanType = loanType;
    }

    public Boolean getAllowSubExpense() {
        return allowSubExpense;
    }

    public void setAllowSubExpense(Boolean allowSubExpense) {
        this.allowSubExpense = allowSubExpense;
    }

    public String getSubExpenseLabel() {
        return subExpenseLabel;
    }

    public void setSubExpenseLabel(String subExpenseLabel) {
        this.subExpenseLabel = subExpenseLabel;
    }

    public List<ExpenseRelatedTo> getRelatedTos() {
        if (relatedTos == null) relatedTos = new ArrayList<>();
        return relatedTos;
    }

    public void setRelatedTos(List<ExpenseRelatedTo> relatedTos) {
        this.relatedTos = relatedTos;
    }

    public List<ExpenseRelatedTo.ExpenseRelatedToType> getRelatedTosTypes() {
        if (relatedTos != null && !relatedTos.isEmpty())
            return relatedTos.stream().map(relatedTo -> relatedTo.getRelatedToType()).collect(Collectors.toList());
        return null;
    }

    public List<PicklistItem> getRelatedTosToTeams() {
        if (relatedTos != null && !relatedTos.isEmpty()) {
            List<ExpenseRelatedTo> expenseRelatedTosTeams =
                    relatedTos.stream().filter(relatedTo -> relatedTo.getRelatedToType()
                            .equals(ExpenseRelatedTo.ExpenseRelatedToType.TEAM))
                            .collect(Collectors.toList());
            if (expenseRelatedTosTeams != null && !expenseRelatedTosTeams.isEmpty())
                return expenseRelatedTosTeams.get(0).getTeams().stream().map(x -> x.getTeam()).collect(Collectors.toList());
        }
        return null;
    }

    public List<String> getNamesInFinancialStatements() {
        return namesInFinancialStatements;
    }

    public void setNamesInFinancialStatements(List<String> namesInFinancialStatements) {
        this.namesInFinancialStatements = namesInFinancialStatements;
    }

    public ExpenseNotification getNotification() {
        return notification;
    }

    public void setNotification(ExpenseNotification notification) {
        this.notification = notification;
    }

    public Set<ExpensePaymentMethod> getPaymentMethods() {
        return paymentMethods;
    }

    public void setPaymentMethods(Set<ExpensePaymentMethod> paymentMethods) {
        this.paymentMethods = paymentMethods;
    }

    public PicklistItem getSalaryAdditionType() {
        return salaryAdditionType;
    }

    public void setSalaryAdditionType(PicklistItem salaryAdditionType) {
        this.salaryAdditionType = salaryAdditionType;
    }

    public Double getDefaultAmount() {
        return defaultAmount;
    }

    public void setDefaultAmount(Double defaultAmount) {
        this.defaultAmount = defaultAmount;
    }

    public ExpenseBeneficiaryType getBeneficiaryType() {
        return beneficiaryType;
    }

    public void setBeneficiaryType(ExpenseBeneficiaryType beneficiaryType) {
        this.beneficiaryType = beneficiaryType;
    }

    public List<Supplier> getSuppliers() {
        return suppliers;
    }

    public void setSuppliers(List<Supplier> suppliers) {
        this.suppliers = suppliers;
    }

    public Boolean getRequireAttachment() {
        return requireAttachment != null && requireAttachment;
    }

    public void setRequireAttachment(Boolean requireAttachment) {
        this.requireAttachment = requireAttachment;
    }

    public Boolean getRequireInvoice() {
        return requireInvoice;
    }

    public void setRequireInvoice(Boolean requireInvoice) {
        this.requireInvoice = requireInvoice;
    }

    @JsonProperty("hideRequestRefundButton")
    public boolean isHideRequestRefundButton() { return hideRequestRefundButton; }

    public void setHideRequestRefundButton(boolean hideRequestRefundButton) { this.hideRequestRefundButton = hideRequestRefundButton; }

    public List<User> getRequestors() {
        return requestors;
    }

    public void setRequestors(List<User> requestors) {
        this.requestors = requestors;
    }

    public PicklistItem getRequestedFrom() {
        return requestedFrom;
    }

    public void setRequestedFrom(PicklistItem requestedFrom) {
        this.requestedFrom = requestedFrom;
    }

    public String getCaption() {
        return caption;
    }

    public void setCaption(String caption) {
        this.caption = caption;
    }

    public User getManager() {
        return manager;
    }

    public void setManager(User manager) {
        this.manager = manager;
    }

    public Expense getParent() {
        return parent;
    }

    public void setParent(Expense parent) {
        this.parent = parent;
    }

    public List<Expense> getChildren() {
        return children;
    }

    public void setChildren(List<Expense> children) {
        this.children = children;
    }

    public Boolean getIsSecure() {
        return isSecure != null && isSecure;
    }

    public void setIsSecure(Boolean isSecure) {
        this.isSecure = isSecure;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public PicklistItem getPnlExpenseType() {
        return pnlExpenseType;
    }

    public void setPnlExpenseType(PicklistItem pnlExpenseType) {
        this.pnlExpenseType = pnlExpenseType;
    }

    public Long getAdjustedExpense() {
        return adjustedExpense;
    }

    public void setAdjustedExpense(Long adjustedExpense) {
        this.adjustedExpense = adjustedExpense;
    }

    public boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public String getNameLabel() {
        return deleted ? this.getLabel()+ " (Deleted)" : (disabled ? this.getLabel() + " (Disabled)" : this.getLabel());
    }

    public void setNameLabel(String nameLabel) {
        this.nameLabel = nameLabel;
    }

    public String getCodeLabel() {
        return deleted ? this.getCode() + " (Deleted)" : (disabled ? this.getCode() + " (Disabled)" : this.getCode());
    }

    public void setCodeLabel(String codeLabel) {
        this.codeLabel = codeLabel;
    }

    public boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public ExpenseApprovalMethod getApprovalMethod() {
        return approvalMethod;
    }

    public void setApprovalMethod(ExpenseApprovalMethod approvalMethod) {
        this.approvalMethod = approvalMethod;
    }

    public ExpenseApproveHolderType getApproveHolderType() {
        return approveHolderType;
    }

    public void setApproveHolderType(ExpenseApproveHolderType approveHolderType) {
        this.approveHolderType = approveHolderType;
    }

    public User getApproveHolder() {
        return approveHolder;
    }

    public void setApproveHolder(User approveHolder) {
        this.approveHolder = approveHolder;
    }

    public String getApproveHolderEmail() {
        return approveHolderEmail;
    }

    public void setApproveHolderEmail(String approveHolderEmail) {
        this.approveHolderEmail = approveHolderEmail;
    }

    public Double getLimitForApproval() {
        return limitForApproval;
    }

    public void setLimitForApproval(Double limitForApproval) {
        this.limitForApproval = limitForApproval;
    }

    public Boolean getLimitedCOO() {
        return isLimitedCOO;
    }

    public void setLimitedCOO(Boolean limitedCOO) {
        isLimitedCOO = limitedCOO;
    }

    public Boolean getIsLimitedCOO() {
        return isLimitedCOO != null && isLimitedCOO;
    }

    public void setIsLimitedCOO(Boolean limitedCOO) {
        isLimitedCOO = limitedCOO;
    }

    public Double getLimitCOO() {
        return LimitCOO;
    }

    public void setLimitCOO(Double limitCOO) {
        LimitCOO = limitCOO;
    }

    public Boolean getAllowToAddLoan() {
        return allowToAddLoan;
    }

    public void setAllowToAddLoan(Boolean allowToAddLoan) {
        this.allowToAddLoan = allowToAddLoan;
    }

    public String getDescription() { return description; }

    public void setDescription(String description) { this.description = description; }

    @JsonIgnore
    public boolean limitExceeded(Double amount) {
        if (!approvalMethod.equals(ExpenseApprovalMethod.APPROVAL_REQUIRED_ON_LIMIT) || limitForApproval == null) {
            return false;
        }
        return amount > limitForApproval;
    }

    @JsonIgnore
    public boolean cooLimitExceeded(Double amount) {

        Logger logger = Logger.getLogger("Expense Request Todo Entity.");
        logger.log(Level.INFO, "moveToCooApprovalStep.isLimitedCOO " + isLimitedCOO);
        logger.log(Level.INFO, "moveToCooApprovalStep.LimitCOO " + LimitCOO);
        if ((isLimitedCOO == null || !isLimitedCOO) || LimitCOO == null) {
            return false;
        }
        return amount > LimitCOO;
    }

    public boolean getMatchedSearchFilter() {
        return isMatchedSearchFilter;
    }

    public void setMatchedSearchFilter(boolean matchedSearchFilter) {
        isMatchedSearchFilter = matchedSearchFilter;
    }
}

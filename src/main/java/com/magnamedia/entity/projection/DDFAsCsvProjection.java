package com.magnamedia.entity.projection;
/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 10, 2020
 *         Jirra ACC-1604
 */

public interface DDFAsCsvProjection {

    Integer getRecordNumber();
    String getOic();
    String getApplicationId();
    String getCustomerType();
    String getCustomerIdType();
    String getEid();
    String getDDAIssuedFor();
    String getPayerName();
    String getPayerMobileNumber();
    String getPayerEmailAddress();
    String getFundingAccountType();
    String getIban();
    String getDDACommencementDate();
    String getDDAExpiryDate();
    Integer getDebitRequestInstanceAllowed();
    String getDDAAmountType();
    String getPaymentFrequency();
    String getDDRDefinedDays();
    Double getDDRMaximumAmount();
    Double getDDRMinimumAmount();
    String getCaptureMode();
    String getScannedDDAFileName();

}

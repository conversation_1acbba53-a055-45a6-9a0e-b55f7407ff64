package com.magnamedia.entity.projection;

import java.sql.Date;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jul 3, 2019
 * Jirra ACC-778
 */
public interface BankDirectDebitActivationRecordCsvProjection {

    @Value("#{(target.getDirectDebitFile() != null && "
            + "target.getDirectDebitFile().getDirectDebit() != null && "
            + "target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm() != null && "
            + "target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract() != null && "
            + "target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient() != null) ? "
            + "(target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient().getName()) : ''}")
    String getClientName();
    public String getContract();
    public Double getAmount();
    public Date getPresentmentDate();
    public Date getStartDate();
    public Date getExpiryDate();
    public String getStatus();
    public String getRejectionReason();
    String getAccount();
    String getBank();
    String getNextAction();
    @Value("#{target.getDirectDebitFile() != null && target.getDirectDebitFile().getDdMethod() != null ? "
            + "target.getDirectDebitFile().getDdMethod().getValue() : ''}")
    String getDdType();
}

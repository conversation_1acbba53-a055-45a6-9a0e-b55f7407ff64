package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * FOR ACC-8568
 */
public interface ClientPaymentsSearchProjection {
    Long getId();

    @Value("#{{label: target.getTypeOfPaymentLabel()}}")
    Map<?, ?> getTypeOfPayment();

    @Value("#{target.getIsReplacement()}")
    Boolean getIsReplacement();

    @Value("#{{id: target.getContractId(), client: {id: target.getClientId()}}}")
    Map<?, ?> getContract();

    @Value("#{{id: target.getReplacementForId()}}")
    Map<?, ?> getReplacementFor();

    @Value("#{target.getMethodOfPayment()}")
    String getMethodOfPayment();

    @Value("#{target.getDateOfPayment()}")
    Date getDateOfPayment();

    @Value("#{target.getReplaced()}")
    Boolean isReplaced();

    @Value("#{{label: target.getStatus().getLabel(), value: target.getStatus().getValue()}}")
    Map<?, ?> getStatus();

    @Value("#{{name: target.getReasonOfBouncingChequeName}}")
    Map<?, ?> getReasonOfBouncingCheque();

    @Value("#{target.getDateOfBouncing()}")
    Date getDateOfBouncing();

    Double getAmountOfPayment();

    Double getDiscount();

    Boolean getIsInitial();

    String getOldApplicationId();

    @Value("#{{applicationId: target.getDirectDebitFileApplicationId()}}")
    Map<?, ?> getDirectDebitFile();

    String getChequeNumber();

    String getChequeName();

    @Value("#{target.getCreationDate()}")
    Date getCreationDate();

    @Value("#{target.getDateChangedToPDP()}")
    Date getDateChangedToPDP();

    @Value("#{target.getDateChangedToReceived()}")
    Date getDateChangedToReceived();

    Double getVat();

    @Value("#{target.getBouncingTrials()}")
    Integer getBouncingTrials();

    @Value("#{target.getLastLogDate()}")
    Date getLastLogDate();

    @Value("#{target.getPrepareToRefund()}")
    Boolean isPrepareToRefund();

    @Value("#{{label:target.getBankNameLabel()}}")
    Map<?, ?> getBankName();

    String getNote();

    Boolean getVatPaidByClient();

    @Value("#{target.getFilteredAttachments()}")
    List<Map> getAttachments();

    @Value("#{target.getCreatorFullName()}")
    String getCreator();

    @Value("#{target.getLastModifierFullName()}")
    String getLastModifier();

    Date getStatusLastModificationDate();
}
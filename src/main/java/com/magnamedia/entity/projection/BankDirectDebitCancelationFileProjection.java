package com.magnamedia.entity.projection;

import java.util.Date;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 20, 2019
 * Jirra ACC-1134
 */
public interface BankDirectDebitCancelationFileProjection {
    
    public Long getId();
    
    public String getBankName();

    public Date getDate();

    public Date getOldestPendingForCancellationDD();

    @Value("#{target.getAttachments().size()>0?target.getAttachments().get(0).getName():null}")
    public String getFileName();
    
    @Value("#{target.getAttachments().size()>0?target.getAttachments().get(0).getUuid():null}")
    public String getUuid();
    
}

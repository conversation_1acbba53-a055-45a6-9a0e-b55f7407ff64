/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.module.type.SalaryCurrency;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public interface OfficeStaffRoster {
    static class Counter
    {
        static int i=1;
        public static void resetCounter()
        {
            i=1;
        }
    }
    void setRow(Integer value);
    default Integer getRow(){
        return Counter.i++;
    }
    String getName();

    OfficeStaffStatus getStatus();

    Double getBasicSalary();

    SalaryCurrency getSalaryCurrency();

    default Double getCompanySalaryAed(){
        return getSalaryCurrency()==SalaryCurrency.AED?getBasicSalary():null;
    }
    default Double getCompanySalaryUsd(){
        return getSalaryCurrency()==SalaryCurrency.USD?getBasicSalary():null;
    }

    PicklistItem getCity();

    PicklistItem getManager();

    PicklistItem getTeam();
}

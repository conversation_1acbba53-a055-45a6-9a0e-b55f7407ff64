package com.magnamedia.entity.projection;

import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;
/**
 * <AUTHOR>
 */
public interface ExpenseRequestSearchApiProjection {

    Long getId();

    @Value("#{{id: target.getPaymentId(), " +
            "transaction: target.getPaymentTransactionId() != null ? { id : target.getPaymentTransactionId()} : null }}")
    Map<?, ?> getPayment();

    @Value("#{target.getHousemaidName() != null ? { name : target.getHousemaidName()} : null }")
    Map<?, ?> getHousemaid();

    @Value("#{{name: target.getExpenseName(), id: target.getExpenseId()}}")
    Map<?, ?> getExpense();

    String getExpenseCaption();

    @Value("#{target.getPurposeAdditionalDescriptionLabel() != null ? " +
            "{ label : target.getPurposeAdditionalDescriptionLabel()} : null }")
    Map<?, ?> getPurposeAdditionalDescription();

    Date getCreationDate();

    String getAmount();

    @Value("#{target.getCurrencyLabel() != null ? { label : target.getCurrencyLabel()} : null }")
    Map<?, ?> getCurrency();

    Double getLoanAmount();

    ExpensePaymentMethod getPaymentMethod();

    ExpenseRequestStatus getStatus();

    @Value("#{target.getRequestedByName() != null ? { name : target.getRequestedByName()} : null }")
    Map<?, ?> getRequestedBy();

    String getApprovedBy();

    String getNotes();

    List<Map> getAttachments();

    String getDescription();

    String getRelatedToInfo();

    String getBenefeciaryInfo();

    String getTransferManager();

    Double getAmountInLocalCurrency();

    @Value("#{target.getBucketName() != null ? { name : target.getBucketName()} : null }")
    Map<?, ?> getBucket();

    Boolean getCanBeRefunded();
    String getRejectionNotes(); // ACC-5341

    String getPendingForApproval();
}
package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.serializer.IdNameSerializer;
import com.magnamedia.module.type.BucketReplenishmentTodoStatus;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2/27/2021
 */
public interface BucketReplenishmentProjection {

    Long getId();

    String getTodoName();

    Date getCreationDate();

    Date getRequestDate();

    Boolean getShowDone();

    @JsonSerialize(using = IdNameSerializer.class)
    Bucket getBucket();

    @JsonSerialize(using = IdLabelSerializer.class)
    Bucket getFillFrom();

    Double getAmount();

    BucketReplenishmentTodoStatus getStatus();

    // JIRA ACC-4505
    Double getBucketBalance();
}

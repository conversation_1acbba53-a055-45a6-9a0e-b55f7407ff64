package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.CollectionFlowStatus;
import com.magnamedia.module.type.DirectDebitRejectionToDoType;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 3/5/2022
 **/

@Entity
public class CollectionFlowLog extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem flowType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;

    @Column
    private Long relatedToId;

    @Column
    private String relatedToEntity;

    @Column
    private Date triggerDate;

    @Column
    private Date idleSinceDate;

    @Column
    @Enumerated(EnumType.STRING)
    private CollectionFlowStatus status;

    @Column
    private String notes;

    @Column(columnDefinition = "boolean default false")
    private Boolean ended = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean rejectionTodoIsRescheduledWhenEnded = false;

    public CollectionFlowLog() {
        super();
    }

    public CollectionFlowLog(PicklistItem flowType, Contract contract, Long relatedToId, String relatedToEntity, Date triggerDate, Date idleSinceDate, CollectionFlowStatus status, String notes) {
        super();
        this.flowType = flowType;
        this.contract = contract;
        this.relatedToId = relatedToId;
        this.relatedToEntity = relatedToEntity;
        this.triggerDate = triggerDate;
        this.idleSinceDate = idleSinceDate;
        this.status = status;
        this.notes = notes;
    }

    public PicklistItem getFlowType() {
        return flowType;
    }

    public void setFlowType(PicklistItem flowType) {
        this.flowType = flowType;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Long getRelatedToId() {
        return relatedToId;
    }

    public void setRelatedToId(Long relatedToId) {
        this.relatedToId = relatedToId;
    }

    public String getRelatedToEntity() {
        return relatedToEntity;
    }

    public void setRelatedToEntity(String relatedToEntity) {
        this.relatedToEntity = relatedToEntity;
    }

    public Date getTriggerDate() {
        return triggerDate;
    }

    public void setTriggerDate(Date triggerDate) {
        this.triggerDate = triggerDate;
    }

    public Date getIdleSinceDate() {
        return idleSinceDate;
    }

    public void setIdleSinceDate(Date idleSinceDate) {
        this.idleSinceDate = idleSinceDate;
    }

    public CollectionFlowStatus getStatus() {
        return status;
    }

    public void setStatus(CollectionFlowStatus status) {
        this.status = status;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Boolean getEnded() {
        return ended;
    }

    public void setEnded(Boolean ended) {
        this.ended = ended;
    }

    public Boolean getRejectionTodoIsRescheduledWhenEnded() {
        return rejectionTodoIsRescheduledWhenEnded;
    }

    public void setRejectionTodoIsRescheduledWhenEnded(Boolean rejectionTodoIsRescheduledWhenEnded) {
        this.rejectionTodoIsRescheduledWhenEnded = rejectionTodoIsRescheduledWhenEnded;
    }
}

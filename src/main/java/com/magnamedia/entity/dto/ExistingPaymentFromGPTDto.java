package com.magnamedia.entity.dto;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ExistingPaymentFromGPTDto {
    private Long contractId;
    private String paymentMethod;
    private boolean payingOnline = true;
    private String selectedPaymentID;
    private String selectedPaymentIsBounced = "false";

    public boolean selectedPaymentIsBounced() {
        return Boolean.parseBoolean(selectedPaymentIsBounced);
    }
}
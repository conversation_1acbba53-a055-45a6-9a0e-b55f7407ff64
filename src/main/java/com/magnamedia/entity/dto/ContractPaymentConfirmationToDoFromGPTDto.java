package com.magnamedia.entity.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
public class ContractPaymentConfirmationToDoFromGPTDto {
    private Long contractId;
    private String paymentMethod;
    private boolean payingOnline = true;
    private String newPaymentDate;
    private String newPaymentIsProrated = "false";
    private Date parsedDate;
    private Double amount;
    private Double discountAmount;
    private Double additionAmount;
    private String paymentType;

    public boolean isNewPaymentProrated() {
        return Boolean.parseBoolean(newPaymentIsProrated);
    }

    public boolean hasAmount() { return amount != null && amount > 0; }

    public boolean hasDiscount() { return discountAmount != null && discountAmount > 0; }

    public boolean hasAddition() { return additionAmount != null && additionAmount > 0; }

    public boolean hasBothDiscountAndAddition() { return hasDiscount() && hasAddition(); }
}
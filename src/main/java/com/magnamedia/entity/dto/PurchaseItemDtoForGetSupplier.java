package com.magnamedia.entity.dto;

import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.module.type.PurchaseItemSupplierStatus;

import java.math.BigDecimal;

/**
 * <PERSON> (Feb 02, 2021)
 */
public class PurchaseItemDtoForGetSupplier {
    public PurchaseItemDtoForGetSupplier(PurchaseItem purchaseItem) {
        this.id = purchaseItem.getId();
        this.name = purchaseItem.getItem().getName();
        this.unitOfMeasure = purchaseItem.getItem().getUnitOfMeasureShortName();
        this.quantity = purchaseItem.getQuantity() != null ?
                purchaseItem.getQuantity().toString() : null;
        this.cheapestPrice = purchaseItem.getItem().getBestPrice();
        this.cheapestSupplier = purchaseItem.getItem().getBestSupplier() != null ?
                purchaseItem.getItem().getBestSupplier().getName() : null;

        this.previousPrice = purchaseItem.getItem().getLastPrice();
        this.previousSupplier = purchaseItem.getItem().getLastSupplier() != null ?
                purchaseItem.getItem().getLastSupplier().getName() : null;

        this.packageSize = purchaseItem.getPackageSize();
        this.packagePrice = purchaseItem.getPackagePrice();
        if (purchaseItem.getCurrentSupplier() != null) {
            this.currentSupplier = purchaseItem.getCurrentSupplier().getName();
            this.currentSupplierId = purchaseItem.getCurrentSupplier().getId();
            this.currentPrice = purchaseItem.getUnitPrice();
        }
        this.supplierStatus = purchaseItem.getSupplierStatus();
        this.sendToGetBetterPriceNote = purchaseItem.getSendToGetBetterPriceNote();
        this.managerNote = purchaseItem.getManagerNotes();
    }

    private Long id;
    private String name;
    private String quantity;
    private String unitOfMeasure;
    private String previousSupplier;
    private Double previousPrice;
    private String cheapestSupplier;
    private Double cheapestPrice;
    private String currentSupplier;
    private Long currentSupplierId;
    private BigDecimal currentPrice;
    private BigDecimal packageSize;
    private BigDecimal packagePrice;
    private PurchaseItemSupplierStatus supplierStatus;
    private String managerNote;

    private String sendToGetBetterPriceNote;

    public Long getId() {
        return id;
    }

    public PurchaseItemSupplierStatus getSupplierStatus() {
        return supplierStatus;
    }

    public void setSupplierStatus(PurchaseItemSupplierStatus supplierStatus) {
        this.supplierStatus = supplierStatus;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public String getPreviousSupplier() {
        return previousSupplier;
    }

    public void setPreviousSupplier(String previousSupplier) {
        this.previousSupplier = previousSupplier;
    }

    public Double getPreviousPrice() {
        return previousPrice;
    }

    public void setPreviousPrice(Double previousPrice) {
        this.previousPrice = previousPrice;
    }

    public String getCheapestSupplier() {
        return cheapestSupplier;
    }

    public void setCheapestSupplier(String cheapestSupplier) {
        this.cheapestSupplier = cheapestSupplier;
    }

    public Double getCheapestPrice() {
        return cheapestPrice;
    }

    public void setCheapestPrice(Double cheapestPrice) {
        this.cheapestPrice = cheapestPrice;
    }

    public String getCurrentSupplier() {
        return currentSupplier;
    }

    public void setCurrentSupplier(String currentSupplier) {
        this.currentSupplier = currentSupplier;
    }

    public Long getCurrentSupplierId() {
        return currentSupplierId;
    }

    public void setCurrentSupplierId(Long currentSupplierId) {
        this.currentSupplierId = currentSupplierId;
    }

    public BigDecimal getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(BigDecimal currentPrice) {
        this.currentPrice = currentPrice;
    }

    public String getSendToGetBetterPriceNote() {
        return sendToGetBetterPriceNote;
    }

    public void setSendToGetBetterPriceNote(String sendToGetBetterPriceNote) {
        this.sendToGetBetterPriceNote = sendToGetBetterPriceNote;
    }

    public BigDecimal getPackageSize() {
        return packageSize;
    }

    public void setPackageSize(BigDecimal packageSize) {
        this.packageSize = packageSize;
    }

    public BigDecimal getPackagePrice() {
        return packagePrice;
    }

    public void setPackagePrice(BigDecimal packagePrice) {
        this.packagePrice = packagePrice;
    }

    public String getManagerNote() {
        return managerNote;
    }

    public void setManagerNote(String managerNote) {
        this.managerNote = managerNote;
    }
}

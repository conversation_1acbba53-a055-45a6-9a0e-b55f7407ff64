package com.magnamedia.entity.dto;

import com.magnamedia.entity.MaintenanceRequest;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.workflow.type.PurchasingToDoType;

import java.util.Date;

/**
 * <PERSON> (Feb 02, 2021)
 */
public class PurchasingAuditorListDto {
    public PurchasingAuditorListDto(PurchasingToDo purchasingToDo) {
        this.id = purchasingToDo.getId();
        this.creationDate = purchasingToDo.getTaskModifiedDate() != null ?
                purchasingToDo.getTaskModifiedDate() : purchasingToDo.getCreationDate();
        this.orderCycleName = purchasingToDo.getOrderCycleName();
        this.orderCycle = purchasingToDo.getCategory().getOrderCycle().getName();
        this.categoryName = purchasingToDo.getCategory().getName();
        this.todoName = purchasingToDo.getTaskName();
        if (todoName.equals(PurchasingToDoType.PA_CONFIRM_BEST_SUPPLIER.toString())) {
            if (purchasingToDo.getTaskHistorys().stream()
                    .anyMatch(t -> t.getTaskName().equals(PurchasingToDoType.PM_GET_BETTER_SUPPLIER.toString()))) {
                this.todoName = "confirm updated prices";
            }
        }
    }

    public PurchasingAuditorListDto(MaintenanceRequest request) {
        this.id = request.getId();
        this.creationDate = request.getTaskModifiedDate() != null ?
                request.getTaskModifiedDate() : request.getCreationDate();
        this.todoName = request.getTaskName();
    }


    private Long id;
    private Date creationDate;
    private String orderCycleName;
    private String categoryName;
    private String orderCycle;
    private String todoName;

    public String getOrderCycle() {
        return orderCycle;
    }

    public void setOrderCycle(String orderCycle) {
        this.orderCycle = orderCycle;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getOrderCycleName() {
        return orderCycleName;
    }

    public void setOrderCycleName(String orderCycleName) {
        this.orderCycleName = orderCycleName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getTodoName() {
        return todoName;
    }

    public void setTodoName(String todoName) {
        this.todoName = todoName;
    }
}

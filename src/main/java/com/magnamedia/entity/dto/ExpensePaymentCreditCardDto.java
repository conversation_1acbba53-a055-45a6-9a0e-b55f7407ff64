package com.magnamedia.entity.dto;

import com.magnamedia.entity.workflow.ExpensePayment;

import java.util.Date;

/**
 * <PERSON> (Jan 26, 2021)
 */
public class ExpensePaymentCreditCardDto {
    public ExpensePaymentCreditCardDto(ExpensePayment expensePayment) {
        this.id = expensePayment.getId();
        this.description = expensePayment.getDescription();
        this.amount = expensePayment.getAmount();
        this.beneficiary = expensePayment.getBeneficiaryName();
        this.currency = expensePayment.getCurrency() != null ?
                expensePayment.getCurrency().getName() : null;
        this.creationDate = expensePayment.getCreationDate();
    }

    private Long id;
    private String description;
    private String beneficiary;
    private Double amount;
    private String currency;
    private Date creationDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBeneficiary() {
        return beneficiary;
    }

    public void setBeneficiary(String beneficiary) {
        this.beneficiary = beneficiary;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Date getCreationDate() { return creationDate; }

    public void setCreationDate(Date creationDate) { this.creationDate = creationDate; }
}

package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.extra.VisaServiceAppStatus;

import javax.persistence.*;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jun 20, 2020
 *         Jirra ACC-2059
 */

@Entity
public class VisaServiceApplication extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    protected Contract contract;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private VisaServiceAppStatus status = VisaServiceAppStatus.PENDING;

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public void setStatus(VisaServiceAppStatus status) {
        this.status = status;
    }

    public VisaServiceAppStatus getStatus() {
        return status;
    }
}
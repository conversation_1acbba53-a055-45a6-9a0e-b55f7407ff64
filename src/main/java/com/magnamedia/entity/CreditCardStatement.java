/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.OperationType;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@Entity
public class CreditCardStatement extends BaseEntity {

	public enum MatchingDecision {
		MATCHED,
		DISMISSED
	}

	@Column
	private Double balance;

	@Column
	private String bookingConfirmationCode;

	@Column
	private String company;

	@Column
	private Double credit;

	@Column
	private Double debit;

	@Column
	private Double amount;

	@Column
	private String description;

	@ManyToOne
	@JsonSerialize(using = IdLabelSerializer.class)
	private Ticket matchedTicket;

	@Column
	private Date operationDate;

	@Column
	@Enumerated(EnumType.STRING)
	private OperationType opertaionType;

	@OneToOne
	@JsonSerialize(using = IdLabelSerializer.class)
	private Transaction transaction;

	@Column
	private Date transactionDate;

//	@Column
//	@Enumerated(EnumType.STRING)
//	private MatchingDecision matchToRefundsDecision;
//
//	@Column
//	@Enumerated(EnumType.STRING)
//	private MatchingDecision matchToPurchaseDecision;
        
        @Column
	@Enumerated(EnumType.STRING)
	private MatchingDecision matchDecision;

	public Double getBalance() {
		return balance;
	}

	public void setBalance(Double balance) {
		this.balance = balance;
	}

	public String getBookingConfirmationCode() {
		return bookingConfirmationCode;
	}

	public void setBookingConfirmationCode(String bookingConfirmationCode) {
		this.bookingConfirmationCode = bookingConfirmationCode;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public Double getCredit() {
		return credit;
	}

	public void setCredit(Double credit) {
		this.credit = credit;
	}

	public Double getDebit() {
		return debit;
	}

	public void setDebit(Double debit) {
		this.debit = debit;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Ticket getMatchedTicket() {
		return matchedTicket;
	}

	public void setMatchedTicket(Ticket matchedTicket) {
		this.matchedTicket = matchedTicket;
	}

	public Date getOperationDate() {
		return operationDate;
	}

	public void setOperationDate(Date operationDate) {
		this.operationDate = operationDate;
	}

	public OperationType getOpertaionType() {
		return opertaionType;
	}

	public void setOpertaionType(OperationType opertaionType) {
		this.opertaionType = opertaionType;
	}

	public Date getTransactionDate() {
		return transactionDate;
	}

	public void setTransactionDate(Date transactionDate) {
		this.transactionDate = transactionDate;
	}

//	public MatchingDecision getMatchToRefundsDecision() {
//		return matchToRefundsDecision;
//	}
//
//	public void setMatchToRefundsDecision(MatchingDecision matchToRefundsDecision) {
//		this.matchToRefundsDecision = matchToRefundsDecision;
//	}
//
//	public MatchingDecision getMatchToPurchaseDecision() {
//		return matchToPurchaseDecision;
//	}
//
//	public void setMatchToPurchaseDecision(MatchingDecision matchToPurchaseDecision) {
//		this.matchToPurchaseDecision = matchToPurchaseDecision;
//	}

	public Transaction getTransaction() {
		return transaction;
	}

	public void setTransaction(Transaction transaction) {
		this.transaction = transaction;

	}

        public MatchingDecision getMatchDecision() {
            return matchDecision;
        }

        public void setMatchDecision(MatchingDecision MatchDecision) {
            this.matchDecision = MatchDecision;
        }

        
	public String getCardNo() {

		if (description == null) {
			return null;
		}
		String desc = description;
		String[] spaceSplitted = desc.split(" ");
		String res = null;

		boolean isNext = false;
		for (String token : spaceSplitted) {
			if (isNext) {
				return token;
			}
			String trimmed = token.trim();
			if (trimmed.contains("*")) {
				isNext = true;
				continue;
			}
			if (trimmed.startsWith(("NO."))) {
				res = trimmed.substring(3);
				break;
			}
		}
		if (res == null || res.length() < 6) {
			return res;
		}
		int len = res.length();

		return res.substring(len - 6,
							 len);
	}

	@PrePersist
	@PreUpdate
	public void preSave() {

		if (this.debit == null && this.credit == null) {
			throw new RuntimeException("Debit and Credit cann't both be null");
		} else if (this.debit != null && this.credit != null) {
			throw new RuntimeException(
				"Debit and Credit cann't both take a value");
		}

		if (this.debit != null) {
			amount = this.debit;
		}
		if (this.credit != null) {
			amount = this.credit;
		}

	}
}

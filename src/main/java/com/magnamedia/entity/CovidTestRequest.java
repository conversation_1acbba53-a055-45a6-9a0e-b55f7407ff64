package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.extra.CovidTestRequestStatus;

import javax.persistence.*;
import java.io.Serializable;

@Entity
public class CovidTestRequest extends BaseEntity implements Serializable {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;

    @Enumerated(EnumType.STRING)
    @Column
    CovidTestRequestStatus status = CovidTestRequestStatus.CREATED_NEW;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem testReason;

    @Column
    @Lob
    private String note;

    @Transient
    private String delay;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public CovidTestRequestStatus getStatus() {
        return status;
    }

    public String getStatusName() {
        switch (this.status) {
            case CREATED_NEW:
                return "Pending";
            case DONE:
                return "Completed";
            case CANCELLED:
                return "Cancelled";
            default:
                return " ";
        }

    }

    public void setStatus(CovidTestRequestStatus status) {
        this.status = status;
    }

    public PicklistItem getTestReason() {
        return testReason;
    }

    public void setTestReason(PicklistItem testReason) {
        this.testReason = testReason;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
}

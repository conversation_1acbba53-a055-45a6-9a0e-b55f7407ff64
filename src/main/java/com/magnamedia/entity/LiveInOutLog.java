package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.ContractAffected;
import com.magnamedia.module.type.HousemaidLiveplace;
import com.magnamedia.module.type.LiveInOutLogReason;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ManyToOne;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

/**
 *
 * <AUTHOR> at Oct 19, 2017
 */
@Entity
@JsonIgnoreProperties(ignoreUnknown = true)
public class LiveInOutLog extends BaseEntity implements Serializable {

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne
    private Housemaid housemaid;
    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne
    private Contract contract;

    @Enumerated(EnumType.STRING)
    private ContractAffected affected;
    @Enumerated(EnumType.STRING)
    private LiveInOutLogReason reason;

    @Column
    private Date date;

    @Column
    private String note;

    @Enumerated(EnumType.STRING)
    private HousemaidLiveplace newValue;

    @Enumerated(EnumType.STRING)
    private HousemaidLiveplace oldValue;

    public LiveInOutLogReason getReason() {
        return reason;
    }

    public void setReason(LiveInOutLogReason reason) {
        this.reason = reason;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public ContractAffected getAffected() {
        return affected;
    }

    public void setAffected(ContractAffected affected) {
        this.affected = affected;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public HousemaidLiveplace getNewValue() {
        return newValue;
    }

    public void setNewValue(HousemaidLiveplace newValue) {
        this.newValue = newValue;
    }

    public HousemaidLiveplace getOldValue() {
        return oldValue;
    }

    public void setOldValue(HousemaidLiveplace oldValue) {
        this.oldValue = oldValue;
    }

    public Boolean getEditable() {
        if (this.date == null) {
            return true;
        }
//        Calendar todayTuncated = Calendar.getInstance();
//            todayTuncated.set(Calendar.HOUR_OF_DAY, 0);
//            todayTuncated.set(Calendar.MINUTE, 0);
//            todayTuncated.set(Calendar.SECOND, 0);
//            todayTuncated.set(Calendar.MILLISECOND, 0);
//        if (getCreationDate().before(todayTuncated.getTime()) && this.date.before(todayTuncated.getTime())) {
//            return true;
//        }
//        return false;
        DateTime currentDateTime = new DateTime();
        DateTime lastLiveLogDatetime = new DateTime(this.date);
        LocalDate currentLocalDate = currentDateTime.toLocalDate();
        LocalDate lastLiveLogLocalDate = lastLiveLogDatetime.toLocalDate();
        if (currentLocalDate.isEqual(lastLiveLogLocalDate) || currentLocalDate.isBefore(lastLiveLogLocalDate)) {
            return false;
        }
        return true;
    }

//    @AfterUpdate
//    @AfterDelete
//    @AfterInsert
//    private void fixAdjustedEndDate() {
//        this.contract = Setup.getRepository(ContractRepository.class).findOne(this.contract.getId());
//        AdjustedEndDate aDate = PaymentUtil.setAdjustedEndDate(this.contract);
//        this.contract.setAdjustedEndDate(aDate.getAdjustedEndDate() != null ? aDate.getAdjustedEndDate().toDate() : null);
//        this.contract.setInitialAdjustedEndDate(aDate.getInitialAdjustedEndDate() != null ? aDate.getInitialAdjustedEndDate().toDate() : null);
//        this.contract.setPaidEndDate(aDate.getPaidEndDate() != null ? aDate.getPaidEndDate().toDate() : null);
//        this.contract.setAccruedVacationDays(aDate.getAccruedVacationDays());
//        Setup.getRepository(ContractRepository.class).save(this.contract);
//    }
}

package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2/11/2021
 */
@Entity
public class InsuranceAuditingSetup  extends BaseEntity {

    @ElementCollection
    @NotAudited
    @CollectionTable(name = "INSURANCE_AUDITION_DISMISSED_PASSPORTS", joinColumns = @JoinColumn(name = "insurance_auditingSetup_id"))
    private List<String> dismissedPassports = new ArrayList<>();

    @Column
    private  Double max_a;

    @Column
    private  Double max_d;


    @Column
    private  Double min_tot;

    @Column
    private  Double max_tot;


    public List<String> getDismissedPassports() {
        return dismissedPassports;
    }

    public void setDismissedPassports(List<String> dismissedPassports) {
        this.dismissedPassports = dismissedPassports;
    }

    public Double getMax_a() {
        return max_a;
    }



    public void setMax_a(Double max_a) {
        this.max_a = max_a;
    }

    public Double getMax_d() {
        return max_d;
    }

    public void setMax_d(Double max_d) {
        this.max_d = max_d;
    }

    public Double getMin_tot() {
        return min_tot;
    }

    public void setMin_tot(Double min_tot) {
        this.min_tot = min_tot;
    }

    public Double getMax_tot() {
        return max_tot;
    }

    public void setMax_tot(Double max_tot) {
        this.max_tot = max_tot;
    }
}

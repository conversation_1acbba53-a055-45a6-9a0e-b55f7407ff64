package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import org.apache.commons.lang3.BooleanUtils;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import java.util.Arrays;
import java.util.List;

@MappedSuperclass
public abstract class AbstractPaymentTypeConfig extends BaseEntity {

    //Main Types
    public static final String AGENCY_FEE_TYPE_CODE = "same_day_recruitment_fee";
    public static final String MONTHLY_PAYMENT_TYPE_CODE = "monthly_payment";
    public static final String INSURANCE_TYPE_CODE = "insurance";
    public static final String MATCHING_FEE_TYPE_CODE = "matching_fee";
    public static final String TRANSFER_FEE_TYPE_CODE = "transfer_fee";
    public static final String MONTHLY_PAYMENT_ADD_ON_TYPE_CODE = "monthly_payment_add_on";
    public static final String PRE_COLLECTED_PAYMENT_CODE = "pre_collected_payment";
    public static final String PRE_COLLECTED_PAYMENT_NO_VAT_CODE = "pre_collected_payment_no_vat";

    public static final String PRE_COLLECTED_SALARY_REFUND_CODE = "pre_collected_salary_refund";
    public static final String PRE_COLLECTED_SALARY_REFUND_NO_VAT_CODE = "pre_collected_salary_refund_no_vat";

    public static final List<String> monthlyTypes = Arrays.asList(
            MONTHLY_PAYMENT_TYPE_CODE,
            MONTHLY_PAYMENT_ADD_ON_TYPE_CODE);

    public static final List<String> preCollectedPaymentTypes = Arrays.asList(
            PRE_COLLECTED_PAYMENT_CODE,
            PRE_COLLECTED_PAYMENT_NO_VAT_CODE);

    public static final List<String> preCollectedPaymentRefundTypes = Arrays.asList(
            PRE_COLLECTED_SALARY_REFUND_CODE,
            PRE_COLLECTED_SALARY_REFUND_NO_VAT_CODE);

    //Sub Types
    public static final String AGENCY_FEE_SUB_TYPE_CODE = "agency_fee";
    public static final String MATCHING_FEE_SUB_TYPE_CODE = "matching_fee";
    public static final String SERVICE_FEE_SUB_TYPE_CODE = "service_fee";
    public static final String TWO_YEAR_VISA_FEE_SUB_TYPE_CODE = "2_year_visa_fee";
    public static final String HIRING_FEE_SUB_TYPE_CODE = "hiring_fee";
    public static final String TRANSFER_FEE_SUB_TYPE_CODE = "transfer_fee";

    public final static String PREVENT_OTHER_DDS_TAG = "prevent_other_dds";

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem type;

    @Column(nullable = false, length = 1000)
    private String description;

    @Column(nullable = false)
    private double amount;

    @Column
    private Double discount;

    @Column
    private Integer discountEffectiveAfter;

    @Column(nullable = false)
    private Integer startsOn;

    @Column(nullable = false)
    private Integer recurrence;

    @Column
    private Integer endsAfter;

    @Column
    private Boolean affectsPaidEndDate;

    @Column(nullable = false)
    private Boolean affectedByAdditionalDiscount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem subType;

    public AbstractPaymentTypeConfig() {

    }

    public AbstractPaymentTypeConfig(PicklistItem type, String description, double amount, Double discount,
                                     Integer discountEffectiveAfter, Integer startsOn, Integer recurrence,
                                     Integer endsAfter, Boolean affectsPaidEndDate, Boolean affectedByAdditionalDiscount, PicklistItem subType) {
        this.type = type;
        this.description = description;
        this.amount = amount;
        this.discount = discount;
        this.discountEffectiveAfter = discountEffectiveAfter;
        this.startsOn = startsOn;
        this.recurrence = recurrence;
        this.endsAfter = endsAfter;
        this.affectsPaidEndDate = affectsPaidEndDate;
        this.affectedByAdditionalDiscount = affectedByAdditionalDiscount;
        this.subType = subType;
    }

    public PicklistItem getType() {
        return type;
    }

    public void setType(PicklistItem type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Integer getDiscountEffectiveAfter() {
        return discountEffectiveAfter;
    }

    public void setDiscountEffectiveAfter(Integer discountEffectiveAfter) {
        this.discountEffectiveAfter = discountEffectiveAfter;
    }

    public Integer getStartsOn() {
        return startsOn;
    }

    public void setStartsOn(Integer startsOn) {
        this.startsOn = startsOn;
    }

    public Integer getRecurrence() {
        return recurrence == null ? 0 : recurrence;
    }

    public void setRecurrence(Integer recurrence) {
        this.recurrence = recurrence;
    }

    public Integer getEndsAfter() {
        return endsAfter;
    }

    public void setEndsAfter(Integer endsAfter) {
        this.endsAfter = endsAfter;
    }

    public Boolean getAffectsPaidEndDate() {
        return affectsPaidEndDate;
    }

    public void setAffectsPaidEndDate(Boolean affectsPaidEndDate) {
        this.affectsPaidEndDate = affectsPaidEndDate;
    }

    public Boolean getAffectedByAdditionalDiscount() {
        return BooleanUtils.toBoolean(affectedByAdditionalDiscount);
    }

    public void setAffectedByAdditionalDiscount(Boolean affectedByAdditionalDiscount) {
        this.affectedByAdditionalDiscount = affectedByAdditionalDiscount;
    }

    public PicklistItem getSubType() { return subType; }

    public void setSubType(PicklistItem subType) { this.subType = subType; }
}
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 */
@Entity
public class SubRuleLog extends BaseEntity implements Serializable {

    @Column
    private String subRuleName;
   
    @Column
    private String subRuleCode;
    
    @Column
    private String subRuleCreator;
    
    @Column
    private Boolean withUpdate;
    
    @Column
    private Integer resultSize;
    
    @Column(columnDefinition = "TEXT")
    private String resultIds;
    
    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne
    private BaseRuleLog baseRuleLog;

    public String getSubRuleName() {
        return subRuleName;
    }

    public void setSubRuleName(String subRuleName) {
        this.subRuleName = subRuleName;
    }

    public String getSubRuleCode() {
        return subRuleCode;
    }

    public void setSubRuleCode(String subRuleCode) {
        this.subRuleCode = subRuleCode;
    }

    public String getSubRuleCreator() {
        return subRuleCreator;
    }

    public void setSubRuleCreator(String subRuleCreator) {
        this.subRuleCreator = subRuleCreator;
    }

    public Boolean getWithUpdate() {
        return withUpdate;
    }

    public void setWithUpdate(Boolean withUpdate) {
        this.withUpdate = withUpdate;
    }

    public Integer getResultSize() {
        return resultSize;
    }

    public void setResultSize(Integer resultSize) {
        this.resultSize = resultSize;
    }

    public String getResultIds() {
        return resultIds;
    }

    public void setResultIds(String resultIds) {
        this.resultIds = resultIds;
    }

    public BaseRuleLog getBaseRuleLog() {
        return baseRuleLog;
    }

    public void setBaseRuleLog(BaseRuleLog baseRuleLog) {
        this.baseRuleLog = baseRuleLog;
    }
}

package com.magnamedia.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.entity.BaseEntity;


import javax.persistence.*;

/**
 * <AUTHOR>
 */
@Entity
public class AppsServiceDDApprovalTodo extends BaseEntity {

    public enum Result {
        CLOSED_WITH_NO_CONFIRMATION, CLOSED_WITH_CONFIRMATION;
    }

    public enum DdcTodoType {
        PROSPECT_APP,
        GET_CONFIRMATION,
        APPROVAL_TODO,
    }

    @Column
    @Lob
    private String ddDataEntryNote;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private CcServiceApplication ccServiceApplication;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private VisaServiceApplication visaServiceApplication;

    @Column(nullable = false)
    private Boolean isClosed = Boolean.FALSE;

    @Column
    @Enumerated(EnumType.STRING)
    private Result result;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private Contract contract;

    public Result getResult() { return result; }

    public void setResult(Result result) {
        this.result = result;
    }
    public CcServiceApplication getCcServiceApplication() {
        return ccServiceApplication;
    }

    public void setCcServiceApplication(CcServiceApplication ccServiceApplication) {
        this.ccServiceApplication = ccServiceApplication;
    }

    public VisaServiceApplication getVisaServiceApplication() {
        return visaServiceApplication;
    }

    public void setVisaServiceApplication(VisaServiceApplication visaServiceApplication) {
        this.visaServiceApplication = visaServiceApplication;
    }

    public Boolean getClosed() {
        return isClosed;
    }

    public void setClosed(Boolean closed) {
        isClosed = closed;
    }

    public Contract getContract() { return contract; }

    public void setContract(Contract contract) { this.contract = contract; }

    public String getDdDataEntryNote() { return ddDataEntryNote; }
                                                                            
    public void setDdDataEntryNote(String ddDataEntryNote) { this.ddDataEntryNote = ddDataEntryNote; }
}

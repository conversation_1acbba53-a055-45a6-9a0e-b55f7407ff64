package com.magnamedia.entity;

import com.magnamedia.core.workflow.FormField;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.workflow.visa.AfterEntryCancellationExpensesService;
import com.magnamedia.workflow.visa.CancellationPaperExpensesService;
import com.magnamedia.workflow.visa.OnlineCancellationExpensesService;
import com.magnamedia.workflow.visa.OutsideCancellationExpensesService;
import java.io.Serializable;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Index;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
 * <AUTHOR> Jabr <<EMAIL>>
 * Created on Oct 9, 2017
 */
@Entity
@Table(
        indexes = {
            @Index(columnList = "HOUSEMAID_ID", unique = false)
        })
public class CancelRequest extends VisaRequest<CancelRequest, CancelRequestNote, CancelRequestExpense> implements Serializable {

    public CancelRequest() {
        super("Collect Documents");
    }
    
    @Override
    public String getFinishedTaskName() {
        return "Visa processing complete";
    }
    
	@Enumerated
	@Column
	private PaymentType cancellationPaperPaymentType;

	@Column
	private Double cancellationPaperExpenses;

	@Column
	private java.sql.Date signedCancellationPaperDate;
        
	@Column
	private String cancellationType;
        
	@Enumerated
	@Column
	private PaymentType onlineCancellationPaymentType;

	@Column
	private Double onlineCancellationExpenses;
        
	@Column
	private String immigrationCancellationType;
        
	@Enumerated
	@Column
	private PaymentType afterEntryCancellationPaymentType;

	@Column
	private Double afterEntryCancellatioExpenses;
        
	@Enumerated
	@Column
	private PaymentType outsideOnlineCancellationPaymentType;

	@Column
	private Double outsideOnlineCancellationExpenses;
        
	@Column
	private Boolean ansariCancelSent;

	@Column
	private Boolean immigrationCancellationApproved;

	@Column
	private Boolean replacementOfferLetterCreated;

	@Column
	private Boolean replacementWorkPermitCreated;

	@Column
	private Boolean applyAgain;

	@ManyToOne(fetch = FetchType.EAGER)
	private NewRequest newRequest;

	@ManyToOne(fetch = FetchType.EAGER)
	private RenewRequest renewRequest;
        
	public PaymentType getCancellationPaperPaymentType() {
		return cancellationPaperPaymentType;
	}

	public void setCancellationPaperPaymentType(PaymentType cancellationPaperPaymentType) {
		this.cancellationPaperPaymentType = cancellationPaperPaymentType;
	}

	public Double getCancellationPaperExpenses() {
		if (cancellationPaperExpenses == null) {
			cancellationPaperExpenses = CancellationPaperExpensesService.DEFAULT_EXPENSES;
		}
		return cancellationPaperExpenses;
	}

	public void setCancellationPaperExpenses(Double cancellationPaperExpenses) {
		this.cancellationPaperExpenses = cancellationPaperExpenses;
	}

	public java.sql.Date getSignedCancellationPaperDate() {
		return signedCancellationPaperDate;
	}

	public void setSignedCancellationPaperDate(java.sql.Date signedCancellationPaperDate) {
		this.signedCancellationPaperDate = signedCancellationPaperDate;
	}
        
	public String getCancellationType() {
		return cancellationType;
	}

	public void setCancellationType(String cancellationType) {
		this.cancellationType = cancellationType;
	}
        
	public PaymentType getOnlineCancellationPaymentType() {
		return onlineCancellationPaymentType;
	}

	public void setOnlineCancellationPaymentType(PaymentType onlineCancellationPaymentType) {
		this.onlineCancellationPaymentType = onlineCancellationPaymentType;
	}

	public Double getOnlineCancellationExpenses() {
		if (onlineCancellationExpenses == null) {
			onlineCancellationExpenses = OnlineCancellationExpensesService.DEFAULT_EXPENSES;
		}
		return onlineCancellationExpenses;
	}

	public void setOnlineCancellationExpenses(Double onlineCancellationExpenses) {
		this.onlineCancellationExpenses = onlineCancellationExpenses;
	}
        
	public String getImmigrationCancellationType() {
		return immigrationCancellationType;
	}

	public void setImmigrationCancellationType(String immigrationCancellationType) {
		this.immigrationCancellationType = immigrationCancellationType;
	}
        
	public PaymentType getAfterEntryCancellationPaymentType() {
		return afterEntryCancellationPaymentType;
	}

	public void setAfterEntryCancellationPaymentType(PaymentType afterEntryCancellationPaymentType) {
		this.afterEntryCancellationPaymentType = afterEntryCancellationPaymentType;
	}

	public Double getAfterEntryCancellatioExpenses() {
		if (afterEntryCancellatioExpenses == null) {
			afterEntryCancellatioExpenses = AfterEntryCancellationExpensesService.DEFAULT_EXPENSES;
		}
		return afterEntryCancellatioExpenses;
	}

	public void setAfterEntryCancellatioExpenses(Double afterEntryCancellatioExpenses) {
		this.afterEntryCancellatioExpenses = afterEntryCancellatioExpenses;
	}
        
	public PaymentType getOutsideOnlineCancellationPaymentType() {
		return outsideOnlineCancellationPaymentType;
	}

	public void setOutsideOnlineCancellationPaymentType(PaymentType outsideOnlineCancellationPaymentType) {
		this.outsideOnlineCancellationPaymentType = outsideOnlineCancellationPaymentType;
	}

	public Double getOutsideOnlineCancellationExpenses() {
		if (outsideOnlineCancellationExpenses == null) {
			outsideOnlineCancellationExpenses = OutsideCancellationExpensesService.DEFAULT_EXPENSES;
		}
		return outsideOnlineCancellationExpenses;
	}

	public void setOutsideOnlineCancellationExpenses(Double outsideOnlineCancellationExpenses) {
		this.outsideOnlineCancellationExpenses = outsideOnlineCancellationExpenses;
	}
        
	public NewRequest getNewRequest() {
		return newRequest;
	}

	public void setNewRequest(NewRequest newRequest) {
		this.newRequest = newRequest;
	}

	public RenewRequest getRenewRequest() {
		return renewRequest;
	}

	public void setRenewRequest(RenewRequest renewRequest) {
		this.renewRequest = renewRequest;
	}

	public Boolean isAnsariCancelSent() {
		return ansariCancelSent;
	}

	public void setAnsariCancelSent(Boolean ansariCancelSent) {
		this.ansariCancelSent = ansariCancelSent;
	}

	public Boolean isImmigrationCancellationApproved() {
		return immigrationCancellationApproved;
	}

	public void setImmigrationCancellationApproved(Boolean immigrationCancellationApproved) {
		this.immigrationCancellationApproved = immigrationCancellationApproved;
	}

	public Boolean getReplacementOfferLetterCreated() {
		return replacementOfferLetterCreated;
	}

	public void setReplacementOfferLetterCreated(Boolean replacementOfferLetterCreated) {
		this.replacementOfferLetterCreated = replacementOfferLetterCreated;
	}

	public Boolean getReplacementWorkPermitCreated() {
		return replacementWorkPermitCreated;
	}

	public void setReplacementWorkPermitCreated(Boolean replacementWorkPermitCreated) {
		this.replacementWorkPermitCreated = replacementWorkPermitCreated;
	}

	public Boolean getApplyAgain() {
		return applyAgain;
	}

	public void setApplyAgain(Boolean applyAgain) {
		this.applyAgain = applyAgain;
	}

	@Override
	public boolean equals(Object object) {
		if (!(object instanceof CancelRequest)) {
			return false;
		}
		return super.equals(object);
	}

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

}

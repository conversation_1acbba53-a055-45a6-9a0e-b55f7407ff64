package com.magnamedia.entity.ccapp;

import com.magnamedia.core.entity.BaseEntity;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 *
 * <AUTHOR>
 */

@Entity
public class CcAppTracking extends BaseEntity {
    
    private String contractUuid;
    
    @Enumerated(EnumType.STRING)
    private CcAppAction ccAction;

    public String getContractUuid() {
        return contractUuid;
    }

    public void setContractUuid(String contractUuid) {
        this.contractUuid = contractUuid;
    }

    public CcAppAction getCcAction() {
        return ccAction;
    }

    public void setCcAction(CcAppAction ccAction) {
        this.ccAction = ccAction;
    }
    
    public enum CcAppAction {
        CHANGE_BANK_DETAILS, PAY_BY_CARD, VIEW_PAYMENT_HISTORY
    }
}

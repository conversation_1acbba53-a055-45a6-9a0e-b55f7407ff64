package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.log.entity.BaseLogEntity;
import lombok.*;
import org.hibernate.envers.AuditOverride;

import javax.persistence.Entity;
import javax.persistence.Lob;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Entity
@AuditOverride(isAudited = false)
@Getter
@Setter
public class AccountingAPILog extends BaseLogEntity {

    private Long contractId;

    private String path;

    private String method;

    private String controller;

    private String methodName;

    private String ipAddress;

    private Date date;

    @Lob
    private String headers;

    @Lob
    private String data;

    @Lob
    private String response;

    private String moduleName;

    private String userName;

    @Lob
    private String additionalInfo;

    @JsonIgnore
    public Map<String, Object> getAdditionalInfo() {

        if (additionalInfo != null) {
            try {
                return Setup.getApplicationContext().getBean(ObjectMapper.class)
                        .readValue(additionalInfo, new TypeReference<Map<String, Object>>() {
                        });
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return new HashMap<>();
    }

    public void setAdditionalInfo(Map<String, Object> additionalInfo) {
        try {
            this.additionalInfo =
                    Setup.getApplicationContext().getBean(ObjectMapper.class)
                            .writeValueAsString(additionalInfo);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    @JsonIgnore
    public Object getAdditionalValue(String key) {
        return getAdditionalInfo().get(key);
    }

    public void setAdditionalValue(String key, Object value) {

        Map<String, Object> additionalInfo = getAdditionalInfo();
        additionalInfo.put(key, value);
        setAdditionalInfo(additionalInfo);
    }
}
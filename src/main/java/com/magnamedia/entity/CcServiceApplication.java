package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.CcServiceAppStatus;

import javax.persistence.*;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jun 30, 2020
 *         Jirra ACC-2109
 */

@Entity
public class CcServiceApplication extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private CcServiceAppStatus status = CcServiceAppStatus.PENDING;

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public CcServiceAppStatus getStatus() {
        return status;
    }

    public void setStatus(CcServiceAppStatus status) {
        this.status = status;
    }
}
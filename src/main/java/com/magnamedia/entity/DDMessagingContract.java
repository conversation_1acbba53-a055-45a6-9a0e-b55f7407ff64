package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.serializer.TemplateSerilizer;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToOne;
import javax.persistence.Transient;

@MappedSuperclass
public abstract class DDMessagingContract extends BaseEntity {

    @Column(columnDefinition = "boolean default false")
    private Boolean sendToClient = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean sendToSpouse = false;

    @Transient
    private String clientMessage;

    @Transient
    private String clientSmsMessage;

    @Column(columnDefinition = "boolean default false")
    private Boolean sendAsEmail = false;

    @Column
    private String emailSubject;

    @Column(columnDefinition = "boolean default false")
    private Boolean sendToMaid = false;

    @Transient
    private String maidMessage;

    @Column(columnDefinition = "boolean default false")
    private Boolean sendToMaidWhenRetractCancellation = false;

    @Transient
    private String maidWhenRetractCancellationMessage;

    @OneToOne
    @JsonSerialize(using = TemplateSerilizer.class)
    private Template clientTemplate;

    @OneToOne
    @JsonSerialize(using = TemplateSerilizer.class)
    private Template maidTemplate;

    //Jirra ACC-2445
    @OneToOne
    @JsonSerialize(using = TemplateSerilizer.class)
    private Template maidWhenRetractCancellationTemplate;

    public Boolean getSendToClient() {
        return sendToClient != null ? sendToClient : false;
    }

    public void setSendToClient(Boolean sendToClient) {
        this.sendToClient = sendToClient;
    }

    public Boolean getSendToSpouse() {
        return sendToSpouse != null && sendToSpouse;
    }

    public void setSendToSpouse(Boolean sendToSpouse) {
        this.sendToSpouse = sendToSpouse;
    }

    public Boolean getSendToMaidWhenRetractCancellation() {
        return sendToMaidWhenRetractCancellation;
    }

    public void setSendToMaidWhenRetractCancellation(Boolean sendToMaidWhenRetractCancellation) {
        this.sendToMaidWhenRetractCancellation = sendToMaidWhenRetractCancellation;
    }

    public String getClientMessage() {

        if (this.clientTemplate != null &&
                this.clientTemplate.isChannelExist(ChannelSpecificSettingType.Notification)) {
            return this.clientTemplate.getChannelSetting(
                    ChannelSpecificSettingType.Notification.toString()).getText();
        }
        return clientMessage;
    }

    public void setClientMessage(String clientMessage) {
        this.clientMessage = clientMessage;
    }

    public String getClientSmsMessage() {
        if (this.clientTemplate != null &&
                this.clientTemplate.isChannelExist(ChannelSpecificSettingType.SMS)) {
            return this.clientTemplate.getChannelSetting(
                    ChannelSpecificSettingType.SMS.toString()).getText();
        }
        return clientSmsMessage;
    }

    public void setClientSmsMessage(String clientSmsMessage) {
        this.clientSmsMessage = clientSmsMessage;
    }

    public Boolean getSendAsEmail() {
        return sendAsEmail;
    }

    public void setSendAsEmail(Boolean sendAsEmail) {
        this.sendAsEmail = sendAsEmail;
    }

    public String getEmailSubject() {
        return emailSubject;
    }

    public void setEmailSubject(String emailSubject) {
        this.emailSubject = emailSubject;
    }

    public Boolean getSendToMaid() {
        return sendToMaid;
    }

    public void setSendToMaid(Boolean sendToMaid) {
        this.sendToMaid = sendToMaid;
    }

    public String getMaidMessage() {
        if (getMaidTemplate() == null) return maidMessage;

        if (getMaidTemplate().isChannelExist(ChannelSpecificSettingType.Notification))
            return getMaidTemplate().getChannelSetting(ChannelSpecificSettingType.Notification).getText();

        if (getMaidTemplate().isChannelExist(ChannelSpecificSettingType.SMS))
            return getMaidTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText();

        return getMaidTemplate().getText();
    }

    public void setMaidMessage(String maidMessage) {
        this.maidMessage = maidMessage;
    }

    public String getMaidWhenRetractCancellationMessage() {

        if (getMaidWhenRetractCancellationTemplate() == null) return maidWhenRetractCancellationMessage;

        if (getMaidWhenRetractCancellationTemplate().isChannelExist(ChannelSpecificSettingType.Notification))
            return getMaidWhenRetractCancellationTemplate().getChannelSetting(ChannelSpecificSettingType.Notification).getText();

        if (getMaidWhenRetractCancellationTemplate().isChannelExist(ChannelSpecificSettingType.SMS))
            return getMaidWhenRetractCancellationTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText();

        return getMaidWhenRetractCancellationTemplate().getText();
    }

    public void setMaidWhenRetractCancellationMessage(String maidWhenRetractCancellationMessage) {
        this.maidWhenRetractCancellationMessage = maidWhenRetractCancellationMessage;
    }

    public void setClientTemplate(Template clientTemplate) {
        this.clientTemplate = clientTemplate;
    }

    public Template getClientTemplate() {
        return clientTemplate;
    }

    public void setMaidTemplate(Template maidTemplate) {
        this.maidTemplate = maidTemplate;
    }

    public Template getMaidTemplate() {
        return maidTemplate;
    }

    public Template getMaidWhenRetractCancellationTemplate() {
        return maidWhenRetractCancellationTemplate;
    }

    public void setMaidWhenRetractCancellationTemplate(Template maidWhenRetractCancellationTemplate) {
        this.maidWhenRetractCancellationTemplate = maidWhenRetractCancellationTemplate;
    }

    public abstract DDMessaging getDdMessaging();
}

package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.module.type.ExpenseRequestTicketType;

import javax.persistence.*;
import java.util.Date;

@Entity
public class ExpenseRequestTicket extends BaseEntity {
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private PicklistItem departureAirport;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private PicklistItem arrivalAirport;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem departureCountry;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem arrivalCountry;

    @Enumerated(EnumType.STRING)
    private ExpenseRequestTicketType type;

    @Column
    private Date departOn;

    @Column
    private Date returnOn;

    @JsonSerialize(using = IdLabelSerializer.class)
    @OneToOne
    private ExpenseRequestTodo expenseRequestTodo;

    // JIRA ACC-4505
    @Column
    private String ticketNumber;

    public String getTicketNumber() {
        return ticketNumber;
    }

    public void setTicketNumber(String ticketNumber) {
        this.ticketNumber = ticketNumber;
    }


    public ExpenseRequestTodo getExpenseRequestTodo() {
        return expenseRequestTodo;
    }

    public void setExpenseRequestTodo(ExpenseRequestTodo expenseRequestTodo) {
        this.expenseRequestTodo = expenseRequestTodo;
    }

    public PicklistItem getDepartureAirport() {
        return departureAirport;
    }

    public void setDepartureAirport(PicklistItem departureAirport) {
        this.departureAirport = departureAirport;
    }

    public PicklistItem getArrivalAirport() {
        return arrivalAirport;
    }

    public void setArrivalAirport(PicklistItem arrivalAirport) {
        this.arrivalAirport = arrivalAirport;
    }

    public PicklistItem getDepartureCountry() {
        return departureCountry;
    }

    public void setDepartureCountry(PicklistItem departureCountry) {
        this.departureCountry = departureCountry;
    }

    public PicklistItem getArrivalCountry() {
        return arrivalCountry;
    }

    public void setArrivalCountry(PicklistItem arrivalCountry) {
        this.arrivalCountry = arrivalCountry;
    }

    public ExpenseRequestTicketType getType() {
        return type;
    }

    public void setType(ExpenseRequestTicketType type) {
        this.type = type;
    }

    public Date getDepartOn() {
        return departOn;
    }

    public void setDepartOn(Date departOn) {
        this.departOn = departOn;
    }

    public Date getReturnOn() {
        return returnOn;
    }

    public void setReturnOn(Date returnOn) {
        this.returnOn = returnOn;
    }

    // Jira ACC-4505
    @JsonIgnore
    public boolean isTicketNumberValid(){
        return !StringUtils.isEmpty(ticketNumber);
    }

}

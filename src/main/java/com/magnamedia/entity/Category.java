package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.dto.salesbinder.CategoryDto;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <PERSON> (Jan 25, 2021)
 * NickName SalesBinderCategory
 */

@Entity
public class Category extends BaseEntity {
    @Label
    private String name;
    @Column(unique = true)
    private String categoryId;
    private String description;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem orderCycle;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense expense;

    private boolean inActiveCycle;

    public Category() {
    }

    public Category(CategoryDto dto) {
        this.categoryId = dto.getCategoryId();
        this.name = dto.getName();
        this.description = dto.getDescription();
    }

    public boolean isInActiveCycle() {
        return inActiveCycle;
    }

    public void setInActiveCycle(boolean inActiveCycle) {
        this.inActiveCycle = inActiveCycle;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PicklistItem getOrderCycle() {
        return orderCycle;
    }

    public void setOrderCycle(PicklistItem orderCycle) {
        this.orderCycle = orderCycle;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }
}

package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.VacationStatus;
import java.io.Serializable;
import java.sql.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 */
@Entity
public class HousemaidVacation extends BaseEntity implements Serializable {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;



    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;

    @Column
    private Date startDate;

    @Column
    private Date endDate;

    @Enumerated(EnumType.STRING)
    private VacationStatus status;

    @Column
    private long duration;

    @Column
    private boolean maidInformed = false;

    @Column
    private String vactionNotes;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem confirmedWithHousemaid;

    @Column
    private boolean paymentForVacationNotTaken = false;

    @Column
    private Double amountOfPayment;

    @Column
    private Integer cDuration;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public VacationStatus getStatus() {
        return status;
    }

    public void setStatus(VacationStatus status) {
        this.status = status;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        if (Long.valueOf(duration) == null) {
            this.duration = 0;
        }
        this.duration = duration;
    }

    public boolean isMaidInformed() {
        return maidInformed;
    }

    public void setMaidInformed(boolean maidInformed) {
        this.maidInformed = maidInformed;
    }

    public String getVactionNotes() {
        return vactionNotes;
    }

    public void setVactionNotes(String vactionNotes) {
        this.vactionNotes = vactionNotes;
    }



    public PicklistItem getConfirmedWithHousemaid() {
        return confirmedWithHousemaid;
    }

    public void setConfirmedWithHousemaid(PicklistItem confirmedWithHousemaid) {
        this.confirmedWithHousemaid = confirmedWithHousemaid;
    }

    public boolean isPaymentForVacationNotTaken() {
        return paymentForVacationNotTaken;
    }

    public void setPaymentForVacationNotTaken(boolean paymentForVacationNotTaken) {
        this.paymentForVacationNotTaken = paymentForVacationNotTaken;
    }

    public Double getAmountOfPayment() {
        return amountOfPayment;
    }

    public void setAmountOfPayment(Double amountOfPayment) {
        if (Double.valueOf(amountOfPayment) == null) {
            this.amountOfPayment = 0.0;
        }

        this.amountOfPayment = amountOfPayment;
    }

    public Integer getcDuration() {
        return cDuration;
    }

    public void setcDuration(Integer cDuration) {
        this.cDuration = cDuration;
    }

}
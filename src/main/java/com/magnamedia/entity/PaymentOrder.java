package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.PaymentOrderController;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.module.type.LoanType;
import com.magnamedia.module.type.PaymentOrderStatus;
import com.magnamedia.workflow.type.PaymentRequestMethod;

import javax.persistence.*;
import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 6-10-2020
 *         Jirra ACC-1962
 */
@Entity
public class PaymentOrder extends BaseEntity {
    public static final String FINAL_SETTLEMENT_RECEIPT_ATTACHMENT_TAG = "final_settlement_receipt";
    public static final String FINAL_SETTLEMENT_SIGNATURE_ATTACHMENT_TAG = "final_settlement_paying_signature";

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;
    
    @JsonSerialize(using = IdLabelSerializer.class)
    @OneToOne(fetch = FetchType.LAZY)
    private ExpenseRequestTodo expenseRequestTodo;

    @Column
    private Double amount;

    @Column
    @Enumerated(EnumType.STRING)
    private PaymentOrderStatus status = PaymentOrderStatus.PENDING;

    @Column
    @Enumerated(EnumType.STRING)
    private PaymentRequestMethod methodOfPayment;

    @Column
    private Date date;

    @Column
    private String reason;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PaymentRequestPurpose purpose;

    @Column
    private String description;

    @Column(columnDefinition = "boolean default false")
    private boolean withLoan = false;

    @Column
    @Enumerated(EnumType.STRING)
    private LoanType loanType;

    @Column(columnDefinition = "boolean default false")
    private boolean skipLoanRepaymentForCurrentPayroll;

    //Jirra ACC-3230
    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private User userWhoClosedFinalSettlementToDo;

    @Lob
    private String rejectionNotes;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public ExpenseRequestTodo getExpenseRequestTodo() {
        return expenseRequestTodo;
    }

    public void setExpenseRequestTodo(ExpenseRequestTodo expenseRequestTodo) {
        this.expenseRequestTodo = expenseRequestTodo;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public PaymentRequestMethod getMethodOfPayment() {
        return methodOfPayment;
    }

    public void setMethodOfPayment(PaymentRequestMethod methodOfPayment) {
        this.methodOfPayment = methodOfPayment;
    }

    public PaymentOrderStatus getStatus() {
        return status;
    }

    public void setStatus(PaymentOrderStatus status) {
        this.status = status;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public boolean isWithLoan() {
        return withLoan;
    }

    public void setWithLoan(boolean withLoan) {
        this.withLoan = withLoan;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setLoanType(LoanType loanType) {
        this.loanType = loanType;
    }

    public LoanType getLoanType() {
        return loanType;
    }

    public PaymentRequestPurpose getPurpose() {
        return purpose;
    }

    public void setPurpose(PaymentRequestPurpose purpose) {
        this.purpose = purpose;
    }

    public boolean isSkipLoanRepaymentForCurrentPayroll() {
        return skipLoanRepaymentForCurrentPayroll;
    }

    public void setSkipLoanRepaymentForCurrentPayroll(boolean skipLoanRepaymentForCurrentPayroll) {
        this.skipLoanRepaymentForCurrentPayroll = skipLoanRepaymentForCurrentPayroll;
    }

    public User getUserWhoClosedFinalSettlementToDo() {
        return userWhoClosedFinalSettlementToDo;
    }

    public void setUserWhoClosedFinalSettlementToDo(User userWhoClosedFinalSettlementToDo) {
        this.userWhoClosedFinalSettlementToDo = userWhoClosedFinalSettlementToDo;
    }

    public boolean getFinalSettlement() {
        return Arrays.asList(PaymentOrderController.PAYMENT_ORDER_REASON_FINAL_SETTLEMENT,
                PaymentOrderController.PAYMENT_ORDER_REASON_ABSCONDING_REMOVAL).contains(reason);
    }

    public String getRejectionNotes() { return rejectionNotes; }

    public void setRejectionNotes(String rejectionNotes) { this.rejectionNotes = rejectionNotes; }
}

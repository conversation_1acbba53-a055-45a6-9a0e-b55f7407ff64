package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.entity.serializer.PurchasingToDoInPurchaseOrderSerializer;
import com.magnamedia.module.type.PurchaseItemInOrderStatus;
import com.magnamedia.module.type.PurchaseOrderStatus;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <PERSON> (Feb 04, 2021)
 */
@Entity
public class PurchaseOrder extends BaseEntity {
    @JsonSerialize(using = PurchasingToDoInPurchaseOrderSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PurchasingToDo purchasingToDo;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Supplier supplier;
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "purchaseOrder")
    @JsonSerialize(using = IdLabelListSerializer.class)
    private List<PurchaseItem> purchaseItems;
    @Enumerated(EnumType.STRING)
    private PurchaseOrderStatus status = PurchaseOrderStatus.PENDING_PURCHASING;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ExpenseRequestTodo expenseRequestTodo;

    private Boolean taxable;
    private BigDecimal billAmount;
    private BigDecimal itemsCost;
    private BigDecimal vatAmount;
    private BigDecimal deliveryService;

    public Boolean getTaxable() {
        return taxable;
    }

    public void setTaxable(Boolean taxable) {
        this.taxable = taxable;
    }

    public BigDecimal getItemsCost() {
        return itemsCost;
    }

    public void setItemsCost(BigDecimal itemsCost) {
        this.itemsCost = itemsCost;
    }

    public BigDecimal getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(BigDecimal vatAmount) {
        this.vatAmount = vatAmount;
    }

    public BigDecimal getDeliveryService() {
        return deliveryService;
    }

    public void setDeliveryService(BigDecimal deliveryService) {
        this.deliveryService = deliveryService;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public Supplier getSupplier() {
        return supplier;
    }

    public ExpenseRequestTodo getExpenseRequestTodo() {
        return expenseRequestTodo;
    }

    public void setExpenseRequestTodo(ExpenseRequestTodo expenseRequestTodo) {
        this.expenseRequestTodo = expenseRequestTodo;
    }

    public void setSupplier(Supplier supplier) {
        this.supplier = supplier;
    }

    public List<PurchaseItem> getPurchaseItems() {
        return purchaseItems;
    }

    public PurchasingToDo getPurchasingToDo() {
        return purchasingToDo;
    }

    public void setPurchasingToDo(PurchasingToDo purchasingToDo) {
        this.purchasingToDo = purchasingToDo;
    }

    public void setPurchaseItems(List<PurchaseItem> purchaseItems) {
        this.purchaseItems = purchaseItems;
    }

    public PurchaseOrderStatus getStatus() {
        return status;
    }

    public void setStatus(PurchaseOrderStatus status) {
        this.status = status;
    }

    public BigDecimal calculateTotalItemsCost() {
        return this.purchaseItems.stream().filter(t->t.getItemInOrderStatus().equals(PurchaseItemInOrderStatus.INVOLVED))
                .map(t->t.getQuantity().multiply(t.getPackagePrice()).divide(t.getPackageSize(),2, RoundingMode.HALF_UP)
                        .setScale(2, RoundingMode.CEILING)).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}

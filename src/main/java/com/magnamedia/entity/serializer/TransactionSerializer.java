/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Transaction;

import java.io.IOException;

/**
 * <AUTHOR> <<EMAIL>>
 */
public class TransactionSerializer extends JsonSerializer<Transaction> {

    @Override
    public void serialize(Transaction value, JsonGenerator gen, SerializerProvider sp) throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();


        gen.writeObjectField("id", value.getId() != null ? value.getId() : null);
        gen.writeNumberField("amount", value.getAmount() != null ? value.getAmount() : 0);
        gen.writeObjectField("fromBucket", value.getFromBucket() != null ? value.getFromBucket() : "");
        gen.writeObjectField("toBucket", value.getToBucket() != null ? value.getToBucket() : "");
        gen.writeStringField("description", value.getDescription() != null ? value.getDescription().toString() : "");
        gen.writeObjectField("expense", value.getExpense() != null ? value.getExpense() : "");
        gen.writeObjectField("revenue", value.getRevenue() != null ? value.getRevenue() : "");
        gen.writeStringField("date", value.getDate() != null ? value.getDate().toString() : "");
        gen.writeEndObject();
    }

}

package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.Contract;
import java.io.IOException;


/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jan 13, 2019
 */
public class ContractSeriliserForPayment extends JsonSerializer<BaseEntity> {

    @Override
    public void serialize(BaseEntity value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
//        DateFormat df = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
        Contract c = (Contract) value;
        gen.writeStartObject();
        gen.writeNumberField("id", value.getId());
        gen.writeStringField("label", value.getLabel());
        if (c.getClient() != null) {
            gen.writeFieldName("client");
            gen.writeObject(c.getClient());
        } else {
            gen.writeFieldName("client");
            gen.writeStringField("client", " ");
        }

        if (c.getAdjustedEndDate() != null) {
            gen.writeStringField("adjustedEndDate", c.getAdjustedEndDate().toString());
        } else {
            gen.writeStringField("adjustedEndDate", "");
        }
        if(c.getStartOfContract()!=null) {
            gen.writeStringField("startOfContract", c.getStartOfContract().toString());
            gen.writeStringField("daysSinceStartDate",c.getDaysSinceStartDate().toString());
        } else {
            gen.writeStringField("startOfContract", "");
            gen.writeStringField("daysSinceStartDate","");
        }
        if (c.getEndOfContract() != null) {
            gen.writeStringField("endOfContract", c.getEndOfContract().toString());

//            gen.writeStringField("endOfContract", df.format(c.getEndOfContract()));
        } else {
            gen.writeStringField("endOfContract", "");
        }

        if (c.getPaidEndDate() != null) {
            gen.writeStringField("paidEndDate", c.getPaidEndDate().toString());

//            gen.writeStringField("endOfContract", df.format(c.getEndOfContract()));
        } else {
            gen.writeStringField("paidEndDate", "");
        }
        if (c.getStatus() != null) {

            gen.writeStringField("status", c.getStatus().toString());
        } else {
            gen.writeStringField("status", "");
        }
        if (c.getClient() != null) {
            if (c.getClient().getId() != null) {
                gen.writeStringField("clientId", "" + c.getClient().getId());
            } else {
                gen.writeStringField("clientId", "");
            }
            if (c.getClient().getName() != null) {
                gen.writeStringField("name", c.getClient().getName());
            } else {
                gen.writeStringField("name", "");
            }
            if (c.getClient().getMobileNumber() != null) {
                gen.writeStringField("mobileNumber", c.getClient().getMobileNumber());
            } else {
                gen.writeStringField("mobileNumber", "");
            }
            if (c.getClient().getPreferredContactMethod() != null) {
                gen.writeObjectField("preferredContactMethod", c.getClient().getPreferredContactMethod());

            } else {
                gen.writeStringField("preferredContactMethod", "");
            }

            //gen.writeEmbeddedObject(c.getClient().getPreferredContactMethod());
        } else {
            gen.writeStringField("name", "");
            gen.writeStringField("mobileNumber", "");
        }

        gen.writeEndObject();
    }

}


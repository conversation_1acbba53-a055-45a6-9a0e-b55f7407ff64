package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.service.DirectDebitFileService;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Oct 25, 2020
 */

public class DDFListSerializer extends JsonSerializer<List<DirectDebitFile>> {

    @Override
    public void serialize(List<DirectDebitFile> values, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        gen.writeStartArray();

        if (values != null) {
            for (DirectDebitFile value : values) {
                writeNode(value, gen);
            }
        }

        gen.writeEndArray();
    }

    private void writeNode(DirectDebitFile value, JsonGenerator gen) throws IOException {
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeStringField("label",
                value.getLabel());
        gen.writeStringField("applicationId",
                value.getApplicationId());
        gen.writeObjectField("ddFrequency",
                value.getDdFrequency());
        gen.writeStringField("ddaRefNo",
                value.getDdaRefNo());
        gen.writeObjectField("startDate",
                value.getStartDate());
        gen.writeObjectField("expiryDate",
                value.getExpiryDate());
        gen.writeNumberField("amount",
                value.getAmount());
        gen.writeStringField("ibanNumber",
                value.getIbanNumber());
        gen.writeStringField("bankName",
                value.getBankName());
        gen.writeStringField("accountName",
                value.getAccountName());
        gen.writeObjectField("ddStatus",
                value.getDdStatus());
        gen.writeStringField("previousDDStatus",
                value.getPreviousDDStatus());
        gen.writeObjectField("rejectionReason",
                value.getRejectionReason());
        gen.writeBooleanField("isDdFormGenerated",
                value.hasDdForm());

        //ACC-3829
        gen.writeStringField("creator",
                value.getCreator() == null ? null : value.getCreator().getName());

        gen.writeStringField("cancelingRequester",
                value.getCancelingRequester());

        gen.writeObjectField("lastStatusModificationDate",
                value.getLastStatusModificationDate());

        gen.writeObjectField("creationDate",
                value.getCreationDate() != null ? value.getCreationDate() : null);

        gen.writeArrayFieldStart("attachments");
        if (value.getAttachments() != null) {
            for (Attachment attachment : value.getAttachments()) {
                if (attachment.getTag() != null
                        && (attachment.getTag().equalsIgnoreCase(DirectDebitFile.FILE_TAG_DD_ACTIVATION) || attachment.getTag().equalsIgnoreCase(DirectDebitFile.FILE_TAG_DD_SIGNATURE)))
                    continue;
                gen.writeObject(attachment);
            }
        }
        gen.writeEndArray();

        gen.writeStringField("notes",
                value.getNotes());
        gen.writeObjectField("status",
                value.getStatus());
        gen.writeObjectField("subStatus",
                DirectDebitFileService.getSubStatus(value));

        gen.writeEndObject();
    }
}

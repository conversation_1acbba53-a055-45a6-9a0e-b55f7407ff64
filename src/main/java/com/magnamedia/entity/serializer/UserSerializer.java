package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.User;

import java.io.IOException;

/**
 * Created by hp on 4/4/2021.
 */
public class UserSerializer extends JsonSerializer<User> {

    @Override
    public void serialize(User value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeStringField("name",
                value.getName());
        gen.writeStringField("userName",
                value.getUsername());
        gen.writeEndObject();
    }
}

package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Housemaid;
import java.io.IOException;

/**
 * <AUTHOR> Created on 2017-07-20
 *
 */
public class CustomIdLabelStartDateSerializer extends JsonSerializer<Housemaid> {

    /* (non-Javadoc)
	 * @see com.fasterxml.jackson.databind.JsonSerializer#serialize(java.lang.Object, com.fasterxml.jackson.core.JsonGenerator, com.fasterxml.jackson.databind.SerializerProvider)
     */
    @Override
  public void serialize(Housemaid value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeStringField("label",
                value.getLabel());
        if(value.getStartDate()!= null){
            gen.writeStringField("startDate", value.getStartDate().toString());
        }else{
            gen.writeStringField("startDate", "");
        }
        
        gen.writeEndObject();
    }

}

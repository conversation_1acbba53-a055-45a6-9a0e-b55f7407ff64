package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterDelete;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.IdNamePickListItemsSerializer;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.type.DirectDebitMessagingScheduleTermCategory;
import com.magnamedia.repository.DDBankMessagingRepository;
import com.magnamedia.repository.DDMessagingRepository;
import com.magnamedia.service.AccountingTemplateService;
import com.magnamedia.service.DDMessagingService;

import javax.persistence.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mahfoud
 *         Created on Oct 23, 2023
 *         ACC-6544
 */
@Entity
public class DDBankMessaging extends DDMessagingContract {

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "DD_BANK_MESSAGING_BANKS",
            joinColumns = @JoinColumn(
                    name = "DD_Bank_Messaging_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "BANK_ID",
                    referencedColumnName = "ID")
    )
    @JsonSerialize(using = IdNamePickListItemsSerializer.class)
    private List<PicklistItem> banks;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private DDMessaging ddMessaging;

    public List<PicklistItem> getBanks() { return banks; }

    public void setBanks(List<PicklistItem> banks) { this.banks = banks; }

    @Override
    public DDMessaging getDdMessaging() { return ddMessaging; }

    public void setDdMessaging(DDMessaging ddMessaging) { this.ddMessaging = ddMessaging; }

    @BeforeInsert
    @BeforeUpdate
    private void beforeInsertOrUpdate() {
        checkEntity();
        createMessageTemplateIfNeeded(this);
    }

    public void checkEntity() {

        if (getDdMessaging() == null || getDdMessaging().getId() == null)
                throw new BusinessException("The DD Messaging can't be empty");

        setDdMessaging(Setup.getRepository(DDMessagingRepository.class).findOne(getDdMessaging().getId()));

        if (getBanks() == null || getBanks().isEmpty())
                throw new BusinessException("The Bank can't be empty");

        /*if (getSendToClient() || getSendToSpouse())
            if (getClientMessage() == null || getClientMessage().equalsIgnoreCase(""))
                throw new BusinessException("client message can't be empty");*/

        if (getSendAsEmail())
            if (getEmailSubject() == null || getEmailSubject().equalsIgnoreCase(""))
                throw new BusinessException("email subject can't be empty");

        if (getSendToMaid())
            if (getMaidMessage() == null || getMaidMessage().equalsIgnoreCase(""))
                throw new BusinessException("maid message can't be empty");

        if (getSendToMaidWhenRetractCancellation())
            if (getMaidWhenRetractCancellationMessage() == null || getMaidWhenRetractCancellationMessage().equalsIgnoreCase("")
                    || getDdMessaging().getScheduleTermCategory().equals(DirectDebitMessagingScheduleTermCategory.None))
                throw new BusinessException("maid message can't be empty and scheduled date of termination should be greater than or equal today");

        for (PicklistItem p : getBanks()) {
            List<Long> l = Setup.getRepository(DDBankMessagingRepository.class)
                    .findByDDMessagingAndBank(getDdMessaging().getId(), getId(), p);
            if (!l.isEmpty()) {
                if (p.getName() == null) {
                    p = Setup.getRepository(PicklistItemRepository.class).findOne(p.getId());
                }
                throw new BusinessException("Duplicate message for bank " + p.getName() + ". Operation not allowed");
            }
        }
    }

    private void createMessageTemplateIfNeeded(DDBankMessaging entity) {
        DDMessagingService service = Setup.getApplicationContext().getBean(DDMessagingService.class);
        AccountingTemplateService accountingTemplateService = Setup.getApplicationContext().getBean(AccountingTemplateService.class);

        if (entity.getId() == null) {
            entity.setDdMessaging(Setup.getRepository(DDMessagingRepository.class)
                    .findOne(getDdMessaging().getId()));
        }

        SelectQuery<DDBankMessaging> query = new SelectQuery<>(DDBankMessaging.class);
        query.filterBy("ddMessaging", "=", getDdMessaging());
        List<DDBankMessaging> ddMessagingList = query.execute();

        long oldClientDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getClientTemplate() != null)
                .count() + 1;
        long oldMaidDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getMaidTemplate() != null)
                .count() + 1;
        long oldMaidRetractDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getMaidWhenRetractCancellationTemplate() != null)
                .count() + 1;

        Map<String, Object> m = new HashMap<String, Object>() {{
            put("sendToClient", getSendToClient());
            put("sendToSpouse", getSendToSpouse());
            put("sendAsEmail", getSendAsEmail());
            put("clientTemplate", getClientTemplate());
            put("clientMessage", getClientMessage());
            put("smsText", getClientSmsMessage());
            put("notificationTemplateName", service.fetchTemplateName(
                    "Accounting_dd_bank_messaging_setup_client_notification_" +
                            entity.getDdMessaging().getEvent().toString() + "_dd_" + getDdMessaging().getId() + "_",
                    oldClientDdMessagingCount));

            put("sendToMaid", getSendToMaid());
            put("maidTemplate", getMaidTemplate());
            put("maidMessage", getMaidMessage());
            put("maid_k", service.fetchTemplateName(
                    "Accounting_dd_bank_messaging_setup_maid_notification_" +
                            entity.getDdMessaging().getEvent().toString() + "_dd_" + getDdMessaging().getId() + "_",
                    oldMaidDdMessagingCount));

            put("sendToMaidWhenRetractCancellation", getSendToMaidWhenRetractCancellation());
            put("maidWhenRetractCancellationTemplate", getMaidWhenRetractCancellationTemplate());
            put("maidWhenRetractCancellationMessage", getMaidWhenRetractCancellationMessage());
            put("maidRetract_k", service.fetchTemplateName(
                    "Accounting_dd_bank_messaging_setup_maid_notification_retractCancellation_" +
                            entity.getDdMessaging().getEvent().toString() + "_dd_" + getDdMessaging().getId() + "_",
                    oldMaidRetractDdMessagingCount));

            put("contractProspectTypes", getDdMessaging().getContractProspectTypes());
        }};

        Map<String, Object> r = service.createMessageTemplateIfNeeded(m);

        if (r.containsKey("clientTemplate")) {
            Template t = (Template) r.get("clientTemplate");
            if (getDdMessaging().getClientTemplate() != null) {
                accountingTemplateService.cloneConfigs(getDdMessaging().getClientTemplate(), t);
            }
            entity.setClientTemplate(t);
        }

        if (r.containsKey("maidTemplate")) {
            Template t = (Template) r.get("maidTemplate");
            if (getDdMessaging().getMaidTemplate() != null) {
                accountingTemplateService.cloneConfigs(getDdMessaging().getMaidTemplate(), t);
            }
            entity.setMaidTemplate(t);
        }

        if (r.containsKey("maidWhenRetractCancellationTemplate")) {
            Template t = (Template) r.get("maidWhenRetractCancellationTemplate");
            if (getDdMessaging().getMaidWhenRetractCancellationTemplate() != null) {
                accountingTemplateService.cloneConfigs(getDdMessaging().getMaidWhenRetractCancellationTemplate(), t);
            }
            entity.setMaidWhenRetractCancellationTemplate(t);
        }
    }

    @AfterDelete
    private void afterDelete() {
        TemplateRepository templateRepository = Setup.getRepository(TemplateRepository.class);
        PicklistItem p = PicklistHelper.getItem("template_status", "inactive");

        if (getClientTemplate() != null) {
            getClientTemplate().setStatus(p);
            templateRepository.save(getClientTemplate());
        }

        if (getMaidTemplate() != null) {
            getMaidTemplate().setStatus(p);
            templateRepository.save(getMaidTemplate());
        }

        if (getMaidWhenRetractCancellationTemplate() != null) {
            getMaidWhenRetractCancellationTemplate().setStatus(p);
            templateRepository.save(getMaidWhenRetractCancellationTemplate());
        }
    }
}
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 25, 2020
 *         Jirra ACC-1435
 */
@Entity
public class ClientRemark extends BaseEntity implements Serializable {

	@Column
	private String remark;

	@ManyToOne(fetch = FetchType.LAZY)
	@JsonSerialize(using = IdLabelSerializer.class)
	private Client client;

	public ClientRemark(String remark,
						Client client) {
		this.remark = remark;
		this.client = client;
	}

	public ClientRemark() {
	}

	public String getRemark() {
		return remark;
	}

	public Client getClient() {
		return client;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public void setClient(Client client) {
		this.client = client;
	}

	@JsonSerialize(using = ToStringSerializer.class)
	public User getClientCreator() {
		return super.getCreator();
	}

	@JsonSerialize(using = ToStringSerializer.class)
	public Date getClientCreatorDate() {
		return super.getCreationDate();
	}
}

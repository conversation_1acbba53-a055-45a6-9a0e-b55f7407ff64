package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.TypeIdLabelSerializer;
import com.magnamedia.repository.BasePLNodeRepository;
import com.magnamedia.repository.PLNodeRepository;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Oct 2, 2018
 */
@Entity
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes({@JsonSubTypes.Type(value = PLVariableNode.class, name = "PLVariableNode")})
@DiscriminatorColumn(name = "type")
public class PLNode extends BasePLNode<PLCompany> {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    @JoinColumn(name = "P_LCOMPANY_ID")
    protected PLCompany PLCompany;

    @ManyToOne
    @JsonSerialize(using = TypeIdLabelSerializer.class)
    protected PLNode parent;

    @JsonIgnore
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "parent")
    protected List<PLNode> children = new ArrayList();

    @Override
    public PLCompany getPLCompany() {
        return PLCompany != null ? PLCompany : parent != null ? parent.getPLCompany() : PLCompany;
    }

    @Override
    public void setPLCompany(PLCompany pLCompany) {
        this.PLCompany = pLCompany;
    }

    @Override
    public PLNode getParent() {
        return parent;
    }

    @Override
    public void setParent(BasePLNode parent) {
        this.parent = (PLNode) parent;
    }

    @Override
    public List<BasePLNode> getChildren() {
        return children.stream().map(plNode -> ((BasePLNode) plNode)).collect(Collectors.toList());
    }

    @Override
    public void setChildren(List<BasePLNode> children) {
        this.children = children.stream().map(basePLNode -> ((PLNode) basePLNode)).collect(Collectors.toList());
    }

    @Override
    public List<BasePLNode> getSortedChildren() {
        if (this.children != null && !this.children.isEmpty())
            return this.children.stream()
                    .sorted(Comparator.comparingInt(PLNode::getNodeOrder))
                    .collect(Collectors.toList());
        else
            return this.children.stream().map(plNode -> ((BasePLNode) plNode)).collect(Collectors.toList());
    }

    @Override
    public void validate() {
        if (!noValidate) {
            super.validate();
            PLNodeRepository pLNodeRepository = (PLNodeRepository) getRepository();

            if (this.getId() == null) {
                if (this.getNodeOrder() == null)
                    setNodeOrder((getParent() != null ?
                            pLNodeRepository.findByParent(getParent()).size() :
                            pLNodeRepository.findByPLCompanyAndParentIsNull(getPLCompany()).size())
                    );

            }

            List<BasePLNode> nodes;
            if (getParent() != null)
                nodes = getRepository().findByNameAndParent(getName(), getParent());
            else
                nodes = getRepository().findByNameAndPLCompanyAndParentIsNull(getName(), getPLCompany().getId());

            //Jirra ACC-387
            if (getId() != null)
                nodes = nodes.stream().filter(x -> !x.getId().equals(getId())).collect(Collectors.toList());
            if (!nodes.isEmpty()) {
                throw new RuntimeException("Name should be unique in the same company or parent node.");
            }

            if (getpLNodeType() == null) {
                if (getPLCompany() != null)
                    throw new RuntimeException("Type should not be empty.");
                else {
                    parent = pLNodeRepository.findOne(parent.getId());
                    setpLNodeType(parent.getpLNodeType());
                }
            }
        }
    }

    @Override
    @JsonIgnore
    protected BasePLNodeRepository getRepository() {
        return Setup.getRepository(PLNodeRepository.class);
    }
}

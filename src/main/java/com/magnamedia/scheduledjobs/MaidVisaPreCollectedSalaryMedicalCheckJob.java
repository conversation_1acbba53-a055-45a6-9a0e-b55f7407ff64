package com.magnamedia.scheduledjobs;


import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.service.MaidVisaFailedMedicalCheckService;

import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR> Mariam
 * @created 21/02/2025 - 08:05 PM
 * ACC-8796
 */
public class MaidVisaPreCollectedSalaryMedicalCheckJob implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(MaidVisaPreCollectedSalaryMedicalCheckJob.class.getName());

    @Override
    public void run(Map<String, ?> parameters) {
        logger.info("Start Job");

        MaidVisaFailedMedicalCheckService maidVisaFailedMedicalCheckService = Setup.getApplicationContext()
                .getBean(MaidVisaFailedMedicalCheckService.class);
        maidVisaFailedMedicalCheckService.processActivePreCollectedMaidVisaContracts();

        maidVisaFailedMedicalCheckService.processFailedMedicalCheckForRecurringContracts();
    }
}
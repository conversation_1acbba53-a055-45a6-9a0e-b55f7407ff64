package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.MaidInAccommodationStatistics;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.repository.MaidInAccommodationStatisticsRepository;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <PERSON> (Mar 14, 2021)
 */
public class MaidInAccommodationStatisticsJob implements MagnamediaJob {
    @Override
    public void run(Map<String, ?> map) {
        try {
            runJob(DateUtil.addDays(new Date(), -1));
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    public void runJob(Date now) {
        HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);

        Date start = DateUtil.getDayStart(now);
        Date end = DateUtil.getDayEnd(now);

        List<String> comeFromClientStatuses = Arrays.asList(HousemaidStatus.AVAILABLE.toString(),
                HousemaidStatus.PENDING_FOR_DISCIPLINE.toString(),
                HousemaidStatus.SICK_WITHOUT_CLIENT.toString());
        List<Long> comeFromClient = housemaidRepository.getIdsOfMaidsWhoComeFromClientToAccommodation(start, end, comeFromClientStatuses);


        List<String> wentWithClientStatuses = Arrays.asList(HousemaidStatus.AVAILABLE.toString(),
                HousemaidStatus.SICK_WITHOUT_CLIENT.toString(),
                HousemaidStatus.PENDING_FOR_DISCIPLINE.toString());

        List<Long> wentWithClient = housemaidRepository.getIdsOfMaidsWhoWentWithClientFromAccommodation(start, end, wentWithClientStatuses);

        MaidInAccommodationStatistics maidInAccommodationStatistics = new MaidInAccommodationStatistics();
        maidInAccommodationStatistics.setNumberOfMaidsWhoComeFromClient(comeFromClient.size());
        maidInAccommodationStatistics.setNumberOfMaidsWhoWentWithClient(wentWithClient.size());
        maidInAccommodationStatistics.setStatisticsDate(now);
        Setup.getRepository(MaidInAccommodationStatisticsRepository.class).save(maidInAccommodationStatistics);
    }
}

package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jun 04, 2020
 *         Jirra ACC-2024
 */

public class IncompleteDDInfoSJ implements MagnamediaJob {
    private static final Logger LOGGER =
            Logger.getLogger(IncompleteDDInfoSJ.class.getName());

    private DirectDebitRepository ddRepo;
    private DirectDebitRejectionFlowService directDebitRejectionFlowService;

    public IncompleteDDInfoSJ() {
        ddRepo = Setup.getRepository(DirectDebitRepository.class);
        directDebitRejectionFlowService = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.runJob();
    }

    private void runJob() {
        Page<DirectDebit> page;
        int pageIndex = 0;
        List<Long> processedIDs = new ArrayList();
        processedIDs.add(-1L);
        do {
            page = ddRepo.findByStatusOrMStatusAndCreationDateLessThanWithNoCashPaymentsAndIdNotIn(DirectDebitStatus.IN_COMPLETE, new DateTime().withTimeAtStartOfDay().toDate(),
                    processedIDs, PageRequest.of(pageIndex, 100));
            List<DirectDebit> incompleteDDs = page.getContent();

            for (DirectDebit dd : incompleteDDs) {
                try {
                    logger.log(Level.SEVERE, "MMM DD ID: " + dd.getId());
                    processedIDs.add(dd.getId());

                    Contract contract= dd.getContractPaymentTerm().getContract();
                    if (directDebitRejectionFlowService.doesClientHavePendingDesignerToDo(contract)) {
                        logger.log(Level.INFO, "Contract {0} has a Pending Graphic Designer Todo", contract.getId());
                        continue;
                    }

                    BackgroundTaskHelper.createBackGroundTask(IncompleteDDInfoSJ.class.getName(),
                            "directDebitController", "runIncompleteDDInfoSJ", dd, BackgroundTaskQueues.NormalOperationsQueue);
                } catch (Exception e) {
                    logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
                }
            }
        } while (page.hasNext());
    }
}

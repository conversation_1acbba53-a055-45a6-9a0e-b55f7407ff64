package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.ccapp.CcAppTracking.CcAppAction;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.CcAppTrackingRepository;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.joda.time.LocalDateTime;

/**
 *
 * <AUTHOR> Hachem
 */

public class CcAppTrackingJob implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> parameters) {
        
        LocalDateTime from = new LocalDateTime().minusMonths(1)
                .withDayOfMonth(1)
                .withHourOfDay(0)
                .withMinuteOfHour(0)
                .withSecondOfMinute(0);
        
        LocalDateTime to = new LocalDateTime().withDayOfMonth(1)
                .minusDays(1)
                .withHourOfDay(23)
                .withMinuteOfHour(59)
                .withSecondOfMinute(59);
        
        List<Object[]> ccAppStats = Setup.getRepository(CcAppTrackingRepository.class)
                .getCcAppTrackingStats(from.toDate(), to.toDate());
        
        Set<CcAppAction> processed = new HashSet<>();
        
        for(Object[] a : ccAppStats) {
            CcAppAction action = (CcAppAction)a[0];
            processed.add(action);
            
            processAction(action, (Long) a[1]);
        }
        
        Arrays.stream(CcAppAction.values())
                .filter(v -> !processed.contains(v))
                .forEach(v -> processAction(v, 0L));
    }
    
    private void processAction(CcAppAction action, Long count) {
        Integer limit = null;
        switch(action) {
            case CHANGE_BANK_DETAILS:
                limit = Integer.valueOf(Setup.getParameter(
                        Setup.getCurrentModule(), AccountingModule.CCAPP_CHANGE_BANK_DETAILS_THRESHOLD));

                if(count <= limit) {
                    EmailHelper.sendEmailByText(Setup.getParameter(
                            Setup.getCurrentModule(), AccountingModule.CCAPP_CHANGE_BANK_DETAILS_EMAIL_RECIPIENTS),
                            "Less than " + limit + " clients changed their bank account successfully through the app",
                            "Only " + count + " clients changed their bank account for this month through the app");
                }

                break;
            case PAY_BY_CARD:
                limit = Integer.valueOf(Setup.getParameter(
                        Setup.getCurrentModule(), AccountingModule.CCAPP_PAY_BY_CARD_THRESHOLD));

                if(count <= limit) {
                    EmailHelper.sendEmailByText(Setup.getParameter(
                                    Setup.getCurrentModule(), AccountingModule.CCAPP_PAY_BY_CARD_EMAIL_RECIPIENTS),
                            "Less than " + limit + " clients paid by credit card for this month through the app",
                            "Only " + count + " clients paid by credit card for this month through the app");
                }

                break;
            case VIEW_PAYMENT_HISTORY:
                limit = Integer.valueOf(Setup.getParameter(
                        Setup.getCurrentModule(), AccountingModule.CCAPP_VIEW_PAYMENT_HISTORY_THRESHOLD));

                if(count <= limit) {
                    EmailHelper.sendEmailByText(Setup.getParameter(
                                    Setup.getCurrentModule(), AccountingModule.CCAPP_VIEW_PAYMENT_HISTORY_EMAIL_RECIPIENTS),
                            "Less than " + limit + " clients accessed their payment history for this month through the app",
                            "Only " + count + " clients accessed their payment history for this month through the app");
                }

                break;
        }
    }
}

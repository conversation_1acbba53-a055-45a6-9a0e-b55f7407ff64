package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *         Created on 19, 4, 2020
 *         Jirra ACC-1611
 */

// this job checks for client didn't provide new info/sign after reminder period should run every hour
public class DirectDebitWaitingClientReSignJob implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> map) {
        doJob();
    }

    private static final Logger logger =
            Logger.getLogger(DirectDebitWaitingClientReSignJob.class.getName());

    public void doJob() {

        logger.log(Level.SEVERE, "DirectDebitWaitingClientReSignJob starts");


        DirectDebitRejectionToDoRepository repository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);
        DirectDebitRepository ddRepo = Setup.getRepository(DirectDebitRepository.class);
        DirectDebitRejectionFlowService directDebitRejectionFlowService = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);

        List<String> taskNames = new ArrayList<>();
        taskNames.add(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE.toString());
        taskNames.add(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D.toString());
        taskNames.add(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED.toString());

        List<DirectDebitRejectCategory> rejectCategories = new ArrayList<>();
        rejectCategories.add(DirectDebitRejectCategory.Signature);
        rejectCategories.add(DirectDebitRejectCategory.Account);
        rejectCategories.add(DirectDebitRejectCategory.EID);
        rejectCategories.add(DirectDebitRejectCategory.ComplianceORAccount);
        rejectCategories.add(DirectDebitRejectCategory.Authorization);
        rejectCategories.add(DirectDebitRejectCategory.Invalid_Account);

        Page<DirectDebitRejectionToDo> page;
        int pageIndex = 0;
        List<Long> processedIDs = new ArrayList();
        processedIDs.add(-1L);
        
        do {
            page = repository.findByTaskNameInAndLastRejectCategoryInAndIdNotIn(taskNames, rejectCategories, processedIDs, PageRequest.of(pageIndex, 100));
            List<DirectDebitRejectionToDo> allInWaitingReSign = page.getContent();

            logger.log(Level.SEVERE, "DirectDebitWaitingClientReSignJob allInWaitingReSign size: " + allInWaitingReSign.size());

            for (DirectDebitRejectionToDo directDebitRejectionToDo : allInWaitingReSign) {
                try {
                    processedIDs.add(directDebitRejectionToDo.getId());
                    if (directDebitRejectionToDo.isCompleted() || directDebitRejectionToDo.isStopped()) {
                        continue;
                    }

                    List<DirectDebit> directDebitsList = directDebitRejectionToDo.getDirectDebits();
                    if (directDebitsList == null) {
                        logger.info("No DDs Found");
                        continue;
                    }
                    if (directDebitsList.size() > 0) {
                        boolean thereIsNoIncompleteDD = directDebitsList.stream().noneMatch(dd -> (dd.getCategory() == DirectDebitCategory.A && dd.getMStatus()== DirectDebitStatus.IN_COMPLETE)
                                        || dd.getCategory() == DirectDebitCategory.B && (dd.getStatus() == DirectDebitStatus.IN_COMPLETE
                                        || dd.getMStatus() == DirectDebitStatus.IN_COMPLETE));

                        if (thereIsNoIncompleteDD) {
                            logger.log(Level.SEVERE, "DirectDebitWaitingClientReSignJob directDebitRejectionToDo has no incomplete dd");
                            continue;
                        } else {
                            if (directDebitRejectionToDo.getLastRejectCategory() == DirectDebitRejectCategory.Signature) {
                                DirectDebit directDebit = ddRepo.findOne(directDebitsList.get(0).getId());
                                Contract contract = directDebit.getContractPaymentTerm() != null ? directDebit.getContractPaymentTerm().getContract() : null;
                                if (contract != null && directDebitRejectionFlowService.doesClientHavePendingDesignerToDo(contract)) {
                                    logger.info("Contract#" + contract.getId() + " Has a Pending Graphic Designer ToDo -> do nothing");
                                    continue;
                                }
                            }
                        }
                    }
                    directDebitsList = directDebitRejectionToDo.getDirectDebitsForBouncingFlow();
                    boolean shouldContinue = false;
                    for (DirectDebit directDebit : directDebitsList) {
                        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
                        query.filterBy("directDebitId", "=", directDebit.getId());
                        query.filterBy("replaced", "=", false);
                        query.filterBy("bouncedFlowPausedForReplacement", "=", true);

                        List<Payment> execute = query.execute();

                        if (execute != null && execute.size() > 0) {
                            logger.log(Level.SEVERE, "DirectDebitWaitingClientReSignJob directDebitRejectionToDo is on direct debit that has paused payment for bouncing flow");
                            logger.log(Level.SEVERE, "DirectDebitWaitingClientReSignJob directDebit id:" + directDebit.getId());
                            logger.log(Level.SEVERE, "DirectDebitWaitingClientReSignJob paused payment id:" + execute.get(0).getId());
                            shouldContinue = true;
                            break;
                        }
                    }
                    if (shouldContinue)
                        continue;

                    DirectDebit lastDD = directDebitRejectionToDo.getLastDirectDebit();
                    if (lastDD != null && lastDD.getContractPaymentTerm() != null && lastDD.getContractPaymentTerm().getContract() != null) {
                        Contract contract = lastDD.getContractPaymentTerm().getContract();
                        if (contract.getStatus() == ContractStatus.CANCELLED || contract.getStatus() == ContractStatus.EXPIRED) {
                            logger.log(Level.SEVERE, "DirectDebitWaitingClientReSignJob contract is not active");
                            logger.log(Level.SEVERE, "DirectDebitWaitingClientReSignJob contract status is: " + contract.getStatus());
                            continue;
                        }

                    }
                    BackgroundTaskHelper.createBackGroundTask(DirectDebitWaitingClientReSignJob.class.getName(),
                            "directDebitRejectionToDoController", "proccessReminderAndLastReSignTrial",
                             directDebitRejectionToDo, BackgroundTaskQueues.NormalOperationsQueue);
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "DirectDebitWaitingClientReSignJob exception: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        } while (page.hasNext());
    }
}

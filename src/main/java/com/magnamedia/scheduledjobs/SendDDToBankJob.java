package com.magnamedia.scheduledjobs;

import com.magnamedia.controller.DirectDebitFileController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.dto.DDFSendDDToBankDto;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.service.QueryService;
import org.joda.time.DateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class SendDDToBankJob implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(SendDDToBankJob.class.getName());
    private QueryService queryService;
    private DirectDebitFileController directDebitFileController;
    private static boolean isRunning = false;

    public SendDDToBankJob() {
        queryService = Setup.getApplicationContext().getBean(QueryService.class);
        directDebitFileController = Setup.getApplicationContext().getBean(DirectDebitFileController.class);
    }

    @Override
    public void run(Map<String, ?> parameters) {
        logger.info("Started job");

        if (isRunning) return;
        isRunning = true;

        try {
            processInBatches();
        } catch (Exception e) {
            e.printStackTrace();
        }

        isRunning = false; // Ensure the flag is reset after execution
        logger.info( "Ended job");
    }

    private void processInBatches() {

        Date referenceDate = new DateTime(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ACC_6003_REFERENCE_DATE)).toDate();
        // handle page grouping file in batch
        handleAllPages(referenceDate, -1L, 20);
        // handle failure pages
        handleAllPages(referenceDate, -1L, 1);
    }

    private void handleAllPages(Date referenceDate, Long lastId, int size) {
        List<DDFSendDDToBankDto> result;
        do {
            result = handlePage(referenceDate, lastId, size);
            if (!result.isEmpty()) {
                lastId = result.get(result.size() - 1).getId();
            }
        } while (!result.isEmpty());
    }

    private List<DDFSendDDToBankDto> handlePage(Date referenceDate, Long lastId, int pageSize) {
        // Fetch paginated data
        Page<DDFSendDDToBankDto> result = null;
        try {
            result = queryService.advanceSearchForSendDDToBankAcc8544(
                    "", null, null, null,
                    referenceDate, PageRequest.of(0, pageSize), lastId);

            logger.info("size: " + result.getContent().size() + " ; last id: " + lastId);

            if (result.getContent().isEmpty()) {
                return new ArrayList<>();
            }

            logger.info("ddf ids: " + result.getContent().stream().map(DDFSendDDToBankDto::getId).collect(Collectors.toList()));

            directDebitFileController.generateBatchFileByRPA(
                    result.getContent().stream().map(DDFSendDDToBankDto::getId).collect(Collectors.toList()),
                    null, null, null, null);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return result == null ? new ArrayList<>() : result.getContent();
    }
}
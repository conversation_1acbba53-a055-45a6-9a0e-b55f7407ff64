package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.DDMessagingToDo;
import com.magnamedia.repository.DDMessagingToDoRepository;
import com.magnamedia.service.DdMessagingTodoService;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *         Created on 11, 4, 2020
 *         Jirra ACC-1597
 */

@Component
public class DirectDebitMessagesSendingJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(DirectDebitMessagesSendingJob.class.getName());

    @Override
    public void run(Map<String, ?> map) {
        sendScheduledDdMessaging();
    }

    public void sendScheduledDdMessaging() {
        LocalDateTime n = new LocalDateTime();

        logger.log(Level.SEVERE, "DirectDebitMessagesSendingJob starts at: {0}", n.toString("yyyy-MM-dd HH:mm:ss"));

        DDMessagingToDoRepository ddMessagingToDoRepository = Setup.getRepository(DDMessagingToDoRepository.class);
        DdMessagingTodoService ddMessagingTodoService = Setup.getApplicationContext().getBean(DdMessagingTodoService.class);

        Date start = new LocalDate().minusDays(1).toDate();
        Long lastId = -1L;
        Page<DDMessagingToDo> page;
        logger.info("start: " + start);

        do {
            page = ddMessagingToDoRepository.findByDdMessagingToDosToSend(lastId, start, PageRequest.of(0, 100));

            logger.info("last id: " + lastId);
            for (DDMessagingToDo t : page.getContent()) {
                try {
                    logger.info("todo id: " + t.getId());
                    LocalDateTime todoTime = new LocalDateTime(t.getSendTime().getTime());
                    LocalDateTime todoDate = t.getSendDate() != null ?
                            new LocalDateTime(t.getSendDate().getTime()) :
                            new LocalDateTime(t.getCreationDate());

                    LocalDateTime todoDateTime = todoDate.withTime(
                            todoTime.getHourOfDay(), todoTime.getMinuteOfHour(), 0, 0);

                    if(todoDateTime.isAfter(n)) {
                        logger.log(Level.SEVERE, "todoDateTime: " + todoDateTime.toString("yyyy-MM-dd HH:mm:ss") +
                                "; skipping todo: {0}", t.getId());
                        continue;
                    }

                    ddMessagingTodoService.processTodo(t.getId());
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "DirectDebitMessagesSendingJob exception", e);
                }
            }

            if (!page.getContent().isEmpty()) {
                lastId = page.getContent().get(page.getContent().size() - 1).getId();
            }

        } while (!page.getContent().isEmpty());
    }
}
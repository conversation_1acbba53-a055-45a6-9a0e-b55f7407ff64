package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DDSignatureTemp;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

//ACC-4493
@Repository
public interface DDSignatureTempRepository extends BaseRepository<DDSignatureTemp> {

    @Query("Select new Map(ddf.id as directDebitFileId, att as oldAttachment, ddf.directDebit.eid as eid," +
            "ddf.directDebit.contractPaymentTerm.contract.id as contractId) " +
            "from DirectDebitFile ddf " +
            "inner join Attachment att " +
            "on ddf.id = att.ownerId AND att.ownerType = 'DirectDebitFile' AND att.tag = 'direct_debit_signature' " +
            "where ddf.directDebit.contractPaymentTerm.contract.client.id = ?1")
    List<Map> getNotCorrectedCPTSignAttachments(Long clientId);

    @Query(nativeQuery = true,
            value = "SELECT cli.id FROM CLIENTS cli " +
                    "where cli.id > ?1 and cli.id not IN (SELECT DISTINCT con.CLIENT_ID from DDSIGNATURETEMPS d " +
                    "inner join CONTRACTPAYMENTTERMS cpt on d.CPT_ID = cpt.id " +
                    "inner join CONTRACTS con on cpt.CONTRACT_ID = con.id) " +
                    "and exists " +
                    "(select ddf.ID from CONTRACTS con " +
                    "inner join CONTRACTPAYMENTTERMS cpt on cpt.CONTRACT_ID = con.id " +
                    "inner join DIRECTDEBITS ddm on ddm.CONTRACT_PAYMENT_TERM_ID = cpt.id " +
                    "inner join  DIRECTDEBITFILES ddf on ddf.DIRECT_DEBIT_ID = ddm.ID " +
                    "inner join ATTACHMENTS att on ddf.id = att.OWNER_ID AND att.OWNER_TYPE = 'DirectDebitFile' " +
                    "and att.TAG = 'direct_debit_signature' " +
                    "where  con.CLIENT_ID = cli.id and ddm.IS_DELETED = false)  " +
                    "order by cli.id limit ?2")
    List<Long> getNotCorrectedCptFirstStep(Long clientId, Integer limit);

    @Query("Select count(cpt) > 0 from ContractPaymentTerm cpt inner join DDSignatureTemp dds on cpt.id = dds.cptId " +
        "where cpt.contract.client.id = ?1")
    Boolean existsByClient_Id(Long clientId);
    
    @Query("Select new Map(ddf.id as directDebitFileId, att as oldAttachment, ddf.directDebit.eid as eid," +
            "ddf.directDebit.contractPaymentTerm.contract.id as contractId) " +
            "from DirectDebitFile ddf " +
            "inner join Attachment att on ddf.id = att.ownerId AND att.ownerType = 'DirectDebitFile' " +
                "AND att.tag = 'direct_debit_signature' " +
            "where ddf.id = ?1")
    Map getNewDdfSignAttachments(Long ddfId);
    
    @Query(nativeQuery = true,
        value = "SELECT ddf.ID " +
                "FROM DIRECTDEBITFILES ddf " +
                "inner join ATTACHMENTS att ON ddf.id = att.OWNER_ID AND att.OWNER_TYPE = 'DirectDebitFile' " +
                    "and att.TAG = 'direct_debit_signature'" +
                "inner join DIRECTDEBITS ddm ON ddf.DIRECT_DEBIT_ID = ddm.ID " +
                "WHERE ddf.id > ?1 and ddm.IS_DELETED = false " +
                    "AND ddf.ID not in (select s.DIRECT_DEBIT_FILE_ID from DDSIGNATURETEMPS s) " +
                "Order by ddf.ID limit ?2")
    List<Long> getNewSignaturesNotHandled(Long ddfId, Integer limit);
    
    @Query("Select count(dds) > 0 from DDSignatureTemp dds Where dds.directDebitFileId = ?1")
    Boolean existsByDdf_Id(Long ddfId);

}

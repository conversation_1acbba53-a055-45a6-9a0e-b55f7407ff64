package com.magnamedia.repository;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.extra.PaymentWithOriginal;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.ExpensePaymentStatus;
import com.magnamedia.module.type.ExpenseRequestType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <PERSON> (Jan 19, 2021)
 */
@Repository
public interface ExpensePaymentRepository extends WorkflowRepository<ExpensePayment> {

    @Query("select ep from ExpensePayment ep " +
            "where ep.method='CASH' and ep.status='PENDING'")
    List<ExpensePayment> findCashierPendingPayments();

    Page<ExpensePayment> findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseContainingAndStatus(
            String taskName, ExpensePaymentStatus status, Pageable pageable);

    @Query("select ep from ExpensePayment ep " +
            "where ep.method='CASH' and ep.status='PAID_PENDING_INVOICE' and ep.confirmed=false")
    List<ExpensePayment> findCashierPendingInvoicePayments();

    @Query("select ep from ExpensePayment ep " +
            "where ep.method='CREDIT_CARD' and ep.status='PENDING'")
    List<ExpensePayment> findCreditCardPendingPayments();

    List<ExpensePayment> findByStatusAndConfirmedFalseAndMethodIn(ExpensePaymentStatus paid, List<ExpensePaymentMethod> methods);

    List<ExpensePayment> findByExpenseToPostAndIdNotInOrderByCreationDateDesc(Expense expense, List<Long> ids);

    @Query(value = "select ID,REVISION from EXPENSEPAYMENTS_REVISIONS " +
            "where CONFIRMED = 1 AND CONFIRMED_MODIFIED = 1 AND  " +
            "MODIFIED_BY_RECONCILIATOR = 1 AND " +
            "LAST_MODIFICATION_DATE > ?1 AND LAST_MODIFICATION_DATE < ?2 ", 
            nativeQuery = true)
    List<IdAndRevision> findConfirmedByReconciliatorBetweenDates(Date date1, Date date2);

    @Query(value = "select ep_rev.TRANSACTION_ID AS transactionId, ep_rev.EXPENSE_TO_POST_ID AS expenseToPostId, ep_rev.FROM_BUCKET_ID AS fromBucketId, " +
            "ep_rev.TAXABLE AS taxable, ep_rev.CURRENCY_ID AS currencyId, ep_rev.LOAN_AMOUNT AS loanAmount, " +
            "pi.NAME AS currencyName, b.NAME AS fromBucketName, exp.CAPTION AS expenseToPostName, " +
            "ep_rev.METHOD AS paymentMethod, ep_rev.AMOUNT AS amount, ep_rev.VAT_AMOUNT AS vatAmount, ep_rev.DESCRIPTION AS description " +
            "from EXPENSEPAYMENTS_REVISIONS as ep_rev  left join PICKLISTS_ITEMS as pi on ep_rev.CURRENCY_ID = pi.ID " +
            "left join BUCKETS as b on ep_rev.FROM_BUCKET_ID = b.ID " +
            "left join EXPENSES as exp on ep_rev.EXPENSE_TO_POST_ID = exp.ID " +
            "where ep_rev.ID = ?1 AND ep_rev.REVISION < ?2 " +
            " ORDER BY ep_rev.LAST_MODIFICATION_DATE DESC LIMIT 1 ", 
            nativeQuery = true)
    PaymentWithOriginal findLastEditedByIdAndRevision(Long Id, Long revision);

    @Query(value = "select ID,REVISION from EXPENSEPAYMENTS_REVISIONS " +
            "where CONFIRMED=1 AND " +
            "CONFIRMED_MODIFIED=1 AND  " +
            " LAST_MODIFIER in (?1) " +
            "AND LAST_MODIFICATION_DATE > ?2 " +
            "AND LAST_MODIFICATION_DATE < ?3 " +
            "AND STATUS = ?4", 
            nativeQuery = true)
    List<IdAndRevision> findmissingInvoiceDismissedByUsersBetweenDates(List<Long> userIds, Date date1, Date date2, String status);

    @Query(value = "select ID,REVISION from EXPENSEPAYMENTS_REVISIONS " +
            "where CONFIRMED=1 AND " +
            "CONFIRMED_MODIFIED=1 AND  " +
            " LAST_MODIFIER in (?1) " +
            "AND LAST_MODIFICATION_DATE > ?2 " +
            "AND LAST_MODIFICATION_DATE < ?3 " +
            "AND MISSING_VAT_INVOICE = 1", 
            nativeQuery = true)
    List<IdAndRevision> findmissingTaxInvoiceByUsersBetweenDates(List<Long> userIds, Date date1, Date date2);

    @Query(nativeQuery = true,
            value = "select DISTINCT(exp_rev.ID), exp_rev.REVISION from EXPENSEPAYMENTS_REVISIONS AS exp_rev " +
                    "INNER JOIN EXPENSEPAYMENTS exp ON exp_rev.ID = exp.ID " +
                    "where exp_rev.STATUS_MODIFIED = 1 AND exp_rev.STATUS IN ('PAID', 'PAID_PENDING_INVOICE') AND exp_rev.METHOD = :#{#method} AND " +
                        "exp_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} AND exp_rev.LAST_MODIFICATION_DATE <= :#{#toDate} AND " +
                        "(:#{#archive == true} = true or (:#{#archive == true} = false and exp.DONE_BY_COO = false))",
            countQuery = "select COUNT(DISTINCT(exp_rev.ID)) from EXPENSEPAYMENTS_REVISIONS AS exp_rev " +
                    "INNER JOIN EXPENSEPAYMENTS exp ON exp_rev.ID = exp.ID " +
                    "where exp_rev.STATUS_MODIFIED = 1 AND exp_rev.STATUS IN ('PAID', 'PAID_PENDING_INVOICE') AND exp_rev.METHOD = :#{#method} AND " +
                        "exp_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} AND exp_rev.LAST_MODIFICATION_DATE <= :#{#toDate} AND " +
                        "(:#{#archive == true} = true or (:#{#archive == true} = false and exp.DONE_BY_COO = false))")
    Page<IdAndRevision> getModifiedPaymentsByStatusAndMethodAndDate(@Param("method") String method, @Param("archive") boolean archive,
            @Param("fromDate") Date fromDate, @Param("toDate") Date toDate, Pageable pageable);

    @Query(nativeQuery = true,
            value = "select DISTINCT(exp_rev.ID), exp_rev.REVISION from EXPENSEPAYMENTS_REVISIONS AS exp_rev " +
                    "INNER JOIN EXPENSEPAYMENTS exp ON exp_rev.ID = exp.ID " +
                    "where exp_rev.STATUS_MODIFIED = 1 AND exp_rev.STATUS IN ('PAID', 'PAID_PENDING_INVOICE') AND exp_rev.METHOD = :#{#method} AND " +
                        "exp_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} AND exp_rev.LAST_MODIFICATION_DATE <= :#{#toDate} AND exp.DONE_BY_COO = false AND " +
                        "(EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = exp.ID and cooQ.RELATED_ENTITY_TYPE = exp.ENTITY_TYPE)) AND " +
                        "(NOT EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = exp.ID and " +
                        "cooQ.RELATED_ENTITY_TYPE = exp.ENTITY_TYPE AND (cooQ.ANSWER IS NULL OR cooQ.ANSWER = '')))",
            countQuery = "select COUNT(DISTINCT(exp_rev.ID)) from EXPENSEPAYMENTS_REVISIONS AS exp_rev " +
                    "INNER JOIN EXPENSEPAYMENTS exp ON exp_rev.ID = exp.ID " +
                    "where exp_rev.STATUS_MODIFIED = 1 AND exp_rev.STATUS IN ('PAID', 'PAID_PENDING_INVOICE') AND exp_rev.METHOD = :#{#method} AND " +
                        "exp_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} AND exp_rev.LAST_MODIFICATION_DATE <= :#{#toDate} AND exp.DONE_BY_COO = false AND " +
                        "(EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = exp.ID and cooQ.RELATED_ENTITY_TYPE = exp.ENTITY_TYPE)) AND " +
                        "(NOT EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = exp.ID and " +
                        "cooQ.RELATED_ENTITY_TYPE = exp.ENTITY_TYPE AND (cooQ.ANSWER IS NULL OR cooQ.ANSWER = '')))")
    Page<IdAndRevision> getModifiedQuestionedPaymentsByStatusAndMethodAndDate(@Param("method") String method,
            @Param("fromDate") Date fromDate, @Param("toDate") Date toDate, Pageable pageable);

    @Query(nativeQuery = true,
            value = "select DISTINCT(exp_rev.ID), exp_rev.REVISION from EXPENSEPAYMENTS_REVISIONS AS exp_rev " +
                    "INNER JOIN EXPENSEPAYMENTS exp ON exp_rev.ID = exp.ID " +
                    "where exp_rev.CONFIRMED_MODIFIED = 1 AND exp_rev.CONFIRMED = 1 AND " +
                        "exp_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} AND exp_rev.LAST_MODIFICATION_DATE <= :#{#toDate} AND " +
                        "((exp_rev.MODIFIED_BY_RECONCILIATOR = 1) OR (exp_rev.STATUS = 'PAID_PENDING_INVOICE')) and " +
                        "(:#{#archive == true} = true or (:#{#archive == true} = false and exp.DONE_BY_COO = false)) " +
                    "ORDER BY exp_rev.LAST_MODIFICATION_DATE",
            countQuery = "select COUNT(DISTINCT(exp_rev.ID)) from EXPENSEPAYMENTS_REVISIONS AS exp_rev " +
                        "INNER JOIN EXPENSEPAYMENTS exp ON exp_rev.ID = exp.ID " +
                        "where exp_rev.CONFIRMED_MODIFIED = 1 AND exp_rev.CONFIRMED = 1 AND " +
                            "exp_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} AND exp_rev.LAST_MODIFICATION_DATE <= :#{#toDate} AND " +
                            "((exp_rev.MODIFIED_BY_RECONCILIATOR = 1) OR (exp_rev.STATUS = 'PAID_PENDING_INVOICE')) and " +
                            "(:#{#archive == true} = true or (:#{#archive == true} = false and exp.DONE_BY_COO = false))")
    Page<IdAndRevision> getReconciliatorActions(@Param("archive") boolean archive, @Param("fromDate") Date fromDate, @Param("toDate") Date toDate, Pageable pageable);

    @Query(nativeQuery = true,
            value = "select DISTINCT(exp_rev.ID), exp_rev.REVISION from EXPENSEPAYMENTS_REVISIONS AS exp_rev " +
                    "INNER JOIN EXPENSEPAYMENTS exp ON exp_rev.ID = exp.ID " +
                    "where exp_rev.CONFIRMED_MODIFIED = 1 AND exp_rev.CONFIRMED = 1 AND " +
                        "exp_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} AND exp_rev.LAST_MODIFICATION_DATE <= :#{#toDate} AND " +
                        "((exp_rev.MODIFIED_BY_RECONCILIATOR = 1) OR (exp_rev.STATUS = 'PAID_PENDING_INVOICE')) AND exp.DONE_BY_COO = false AND " +
                        "(EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = exp.ID and cooQ.RELATED_ENTITY_TYPE = exp.ENTITY_TYPE)) AND " +
                        "(NOT EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = exp.ID and cooQ.RELATED_ENTITY_TYPE = exp.ENTITY_TYPE AND (cooQ.ANSWER IS NULL OR cooQ.ANSWER = ''))) " +
                    "ORDER BY exp_rev.LAST_MODIFICATION_DATE",
            countQuery = "select COUNT(DISTINCT(exp_rev.ID)) from EXPENSEPAYMENTS_REVISIONS AS exp_rev " +
                    "INNER JOIN EXPENSEPAYMENTS exp ON exp_rev.ID = exp.ID " +
                    "where exp_rev.CONFIRMED_MODIFIED = 1 AND exp_rev.CONFIRMED = 1 AND " +
                        "exp_rev.LAST_MODIFICATION_DATE >= :#{#fromDate} AND exp_rev.LAST_MODIFICATION_DATE <= :#{#toDate} AND " +
                        "((exp_rev.MODIFIED_BY_RECONCILIATOR = 1) OR (exp_rev.STATUS = 'PAID_PENDING_INVOICE')) AND exp.DONE_BY_COO = false AND " +
                        "(EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = exp.ID and cooQ.RELATED_ENTITY_TYPE = exp.ENTITY_TYPE)) AND " +
                        "(NOT EXISTS (SELECT * FROM COOQUESTIONS AS cooQ where cooQ.RELATED_ENTITY_ID = exp.ID and cooQ.RELATED_ENTITY_TYPE = exp.ENTITY_TYPE AND (cooQ.ANSWER IS NULL OR cooQ.ANSWER = '')))")
    Page<IdAndRevision> getQuestionedReconciliatorActions(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, Pageable pageable);

    @Query(nativeQuery = true,
            value = "SELECT P.TRANSACTION_ID " +
                    "FROM EXPENSEPAYMENTS_REVISIONS P " +
                    "WHERE P.ID = ?1 AND P.TRANSACTION_MODIFIED = 1 AND P.TRANSACTION_ID IS NOT NULL " +
                    "ORDER BY P.LAST_MODIFICATION_DATE ASC LIMIT 1")
    Long getExpensePaymentTransactionId(Long expensePaymentId);

    @Query(value = "Select todo.expensePayment from ExpenseRequestTodo todo " +
            "where todo.expenseRequestType <> 'TICKETING' " +
                "and todo.expensePayment.completed = false And todo.expensePayment.stopped = false " +
                "and todo.expensePayment.status = 'PENDING' and todo.status <> 'CANCELED' " +
                "and todo.expensePayment.taskName = 'TO_DO_IN_CREDIT_CARD_HOLDER_SCREEN'")
    Page<ExpensePayment> findByCreditCardHolderHomePage(Pageable pageable);

    @Query(value = "Select todo.expensePayment from ExpenseRequestTodo todo " +
            "where todo.expenseRequestType = ?1 and todo.expensePayment.completed  = false and todo.expensePayment.stopped  = false " +
                "and todo.expensePayment.status = 'PENDING' and todo.status <> 'CANCELED' " +
                "and todo.expensePayment.taskName = 'TO_DO_IN_CREDIT_CARD_HOLDER_SCREEN'")
    Page<ExpensePayment> findByCreditCardHolderAndTypePage(ExpenseRequestType type, Pageable pageable);

    interface IdAndRevision {
        Long getId();
        Long getRevision();
    }
}
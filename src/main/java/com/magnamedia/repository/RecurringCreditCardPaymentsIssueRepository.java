package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import com.magnamedia.entity.RecurringCreditCardPaymentsIssue;

import java.util.List;

@Repository
public interface RecurringCreditCardPaymentsIssueRepository extends BaseRepository<RecurringCreditCardPaymentsIssue> {

  @Query("select r.subEvent from RecurringCreditCardPaymentsIssue r where r.errorCode.code = ?1")
  List<FlowSubEventConfig> findSubEventByErrorCod(String code);

  @Query("select pl from Picklist p " +
          "inner join PicklistItem pl on p.id = pl.listId " +
          "where p.code = 'credit_card_error_codes' and " +
            "not exists (select 1 from RecurringCreditCardPaymentsIssue r where r.errorCode.id = pl.id )")
  List<PicklistItem> findByErrorCodeIsNotNullAndSubEventIsNull();

  @Query("select r from RecurringCreditCardPaymentsIssue r " +
          "where r.errorCode.id = :errorCodeId  and (:id is null or r.id <> :id)")
  List<RecurringCreditCardPaymentsIssue> findByErrorCodeAndSubEvent(
          @Param("errorCodeId") Long errorCodeId, @Param("id") Long id);
}
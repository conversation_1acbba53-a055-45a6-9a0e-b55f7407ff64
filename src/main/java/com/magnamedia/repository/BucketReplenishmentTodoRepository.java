package com.magnamedia.repository;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.workflow.BucketReplenishmentTodo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 1/22/2021
 */
@Repository
public interface BucketReplenishmentTodoRepository extends WorkflowRepository<BucketReplenishmentTodo> {

    @Query("select count(todo) from BucketReplenishmentTodo todo where todo.bucket = :bucket and " +
            "((todo.status = com.magnamedia.module.type.BucketReplenishmentTodoStatus.APPROVED and todo.transactionAdded = false ) or " +
            "(todo.status != com.magnamedia.module.type.BucketReplenishmentTodoStatus.APPROVED and todo.completed = false and todo.stopped = false))")
    Long countByBucketAndApprovedAndTransactionAddedFalseOrNotApprovedAndNotStoppedAndNotCompleted(@Param("bucket") Bucket bucket);
}

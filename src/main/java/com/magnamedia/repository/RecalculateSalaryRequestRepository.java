package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.RecalculateSalaryRequest;
import java.sql.Date;
import java.util.List;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on May 13, 2019
 * Jirra ACC-674
 */
public interface RecalculateSalaryRequestRepository extends BaseRepository<RecalculateSalaryRequest> {
    
    List<RecalculateSalaryRequest> findByDoneAndRefreshDateLessThanEqual(
            boolean isDone, Date date);
}

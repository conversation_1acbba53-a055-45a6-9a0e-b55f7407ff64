package com.magnamedia.repository;

import com.magnamedia.entity.BaseReportCompany;
import com.magnamedia.entity.Company;
import com.magnamedia.entity.PLCompany;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Oct 1, 2018
 */
@Repository
public interface PLCompanyRepository extends BaseCompanyReportRepository<PLCompany> {
    List<BaseReportCompany> findByCompany(Company company);
}

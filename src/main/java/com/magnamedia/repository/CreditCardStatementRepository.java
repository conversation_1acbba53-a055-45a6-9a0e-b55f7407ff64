/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.CreditCardStatement;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */

@Repository
public interface CreditCardStatementRepository extends BaseRepository<CreditCardStatement>{
    List<CreditCardStatement> findByTransactionDateAndDescriptionAndAmountAndBalance(Date transactionDate,String description,
            Double Amount,Double balance);
}

package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.VisaExpenseConfiguration;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.workflow.visa.ExpensePurpose;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface VisaExpenseConfigurationRepository extends BaseRepository<VisaExpenseConfiguration> {

    boolean existsByEmployeeTypeAndNewEmployeeAndExpensePurposeAndPaymentTypeAndIdNot(
            VisaExpenseConfiguration.EmployeeType employeeType, boolean isNewEmployee, ExpensePurpose expensePurpose,
            PaymentType paymentType, Long id);


    @Query("select e.code, e.id,  " +
            "CASE " +
                "when e.deleted = true THEN CONCAT(e.name, ' (Deleted)')  " +
                "when e.disabled = true then CONCAT(e.name, ' (Disabled)') " +
                "ELSE e.name END " +
            "from VisaExpenseConfiguration c " +
            "join c.expense e " +
            "where c.employeeType = ?1 and c.newEmployee = ?2 and c.expensePurpose = ?3 and c.paymentType = ?4")
    List<Object[]> findExpenseIdAndName(
            VisaExpenseConfiguration.EmployeeType employeeType, boolean isNewEmployee,
            ExpensePurpose expensePurpose, PaymentType paymentType);
}

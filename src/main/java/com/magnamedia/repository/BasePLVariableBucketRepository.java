package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.interfaces.BasePLVariableNode;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@NoRepositoryBean
public interface BasePLVariableBucketRepository<T extends BasePLVariableBucket> extends BaseRepository<T> {

    List<T> findByRevenue(Revenue revenue);
    List<T> findByRevenueAndIdNotIn(Revenue revenue, List<Long> ids);

    List<T> findByExpense(Expense expense);
    List<T> findByExpenseAndIdNotIn(Expense expense, List<Long> ids);

    List<T> findByPLVariable(BasePLVariableNode pLVariable);
}

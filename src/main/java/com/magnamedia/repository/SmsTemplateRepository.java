package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.sms.SmsTemplate;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> <<EMAIL>> 
 * Created at Jun 20, 2019
 * <AUTHOR> kanaan <<EMAIL>>
 * Jirra ACC-1092
 */
@Repository
public interface SmsTemplateRepository extends BaseRepository<SmsTemplate>{

    SmsTemplate findFirst1ByCodeOrderByCreationDateDesc(String code); 
    
    @Query("select s from SmsTemplate s where s.code = :code and s.enabled = true order by s.creationDate desc")
    SmsTemplate findLastOneByCodeAndEnabled(@Param("code") String code);
}
 
package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Complaint;
import com.magnamedia.entity.ComplaintType;
import com.magnamedia.entity.ComplaintCategory;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Housemaid;
import java.util.Date;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created at Nov 5, 2017
 */
@Repository
public interface ComplaintRepository extends BaseRepository<Complaint> {

    @Query(
            "SELECT COUNT(c) FROM Complaint c WHERE c.primaryType = ?1 OR ?1 MEMBER OF c.otherTypes")
    public long countByType(ComplaintType type);

    public long countByCategory(ComplaintCategory category);

    public List<Complaint> findByHousemaidAndCreationDateGreaterThanOrderByCreationDateDesc(Housemaid housemaid, Date creationDate);

    public List<Complaint> findByHousemaidAndCreationDateGreaterThan(Housemaid housemaid, Date creationDate);

    public List<Complaint> findByHousemaid(Housemaid housemaid);
    
    public List<Complaint> findByNeedReplaceDeduction(Boolean needReplaceDeduction);
    
    List<Complaint> findByContract(Contract contract);
    
    List<Complaint> findByClient(Client c);

    @Query("select count(c.id) > 0 from Complaint c " +
            "join c.primaryType as ct " +
            "where c.contract = ?1 and ct.code = 'Contract-amendments'")
    boolean existsByComplaintAddedBy5044(Contract contract);
}

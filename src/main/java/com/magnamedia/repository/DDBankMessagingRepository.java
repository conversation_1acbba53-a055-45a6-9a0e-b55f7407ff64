package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DDBankMessaging;
import com.magnamedia.entity.DDMessaging;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 *         Created on Oct 23, 2023
 *         ACC-6544
 */
@Repository
public interface DDBankMessagingRepository extends BaseRepository<DDBankMessaging> {

    @Query("select d from DDBankMessaging d " +
            "where d.ddMessaging = ?1 and ?2 member of d.banks")
    List<DDBankMessaging> findByDdMessagingAndBank(DDMessaging dd, PicklistItem bank);

    @Query("select d.id from DDBankMessaging d " +
            "where d.ddMessaging.id = :ddId and (:ddBankId is null or d.id <> :ddBankId) and :bank member of d.banks")
    List<Long> findByDDMessagingAndBank(@Param("ddId") Long ddId, @Param("ddBankId") Long ddBankId, @Param("bank") PicklistItem bank);
}
package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.TelecomPhone;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 1, 2018
 */
@Repository
public interface TelecomPhoneRepository extends BaseRepository<TelecomPhone> {

    @Query("SELECT t, b.amount, b.billDate "
            + "FROM TelecomPhone t LEFT JOIN t.telecomPhoneBills b "
            + "on t = b.phone "
            + "WHERE t.active = ?4 and t.deleted = ?5 and (b IS NULL OR b.billDate = "
            + "(select MAX(b2.billDate) from t.telecomPhoneBills b2 WHERE b2.phone.id = b.phone.id)) "
            + "AND ((LOWER(t.name) LIKE LOWER(CONCAT('%' , ?1 , '%')) OR "
            + "LOWER(t.number) LIKE LOWER(CONCAT('%' , ?2 , '%')) OR "
            //Jirra ACC-303
            + "LOWER(t.usageText) LIKE LOWER(CONCAT('%' , ?3 , '%')))) ")
    Page<?> getTelecomeByNameOrNumberOrUsageAndActiveAndDeleted(String query1,
                                                                String query2, String query3, boolean active, boolean deleted, Pageable pageable);

    @Query("SELECT t, b.amount, b.billDate "
            + "FROM TelecomPhone t LEFT JOIN t.telecomPhoneBills b "
            + "on t = b.phone "
            + "WHERE t.deleted = ?4 and (b IS NULL OR b.billDate = "
            + "(select MAX(b2.billDate) from t.telecomPhoneBills b2 WHERE b2.phone.id = b.phone.id)) "
            + "AND ((LOWER(t.name) LIKE LOWER(CONCAT('%' , ?1 , '%')) OR "
            + "LOWER(t.number) LIKE LOWER(CONCAT('%' , ?2 , '%')) OR "
            //Jirra ACC-303
            + "LOWER(t.usageText) LIKE LOWER(CONCAT('%' , ?3 , '%')))) ")
    Page<?> getTelecomeByNameOrNumberOrUsageAndDeleted(String query1,
                                                       String query2, String query3, boolean deleted, Pageable pageable);

    @Query("SELECT t, b.amount, b.billDate "
            + "FROM TelecomPhone t LEFT JOIN t.telecomPhoneBills b "
            + "on t = b.phone "
            + "WHERE t.active = ?4 and (b IS NULL OR b.billDate = "
            + "(select MAX(b2.billDate) from t.telecomPhoneBills b2 WHERE b2.phone.id = b.phone.id)) "
            + "AND ((LOWER(t.name) LIKE LOWER(CONCAT('%' , ?1 , '%')) OR "
            + "LOWER(t.number) LIKE LOWER(CONCAT('%' , ?2 , '%')) OR "
            //Jirra ACC-303
            + "LOWER(t.usageText) LIKE LOWER(CONCAT('%' , ?3 , '%')))) ")
    Page<?> getTelecomeByNameOrNumberOrUsageAndActive(String query1,
                                                      String query2, String query3, boolean active, Pageable pageable);

    @Query("SELECT t, b.amount, b.billDate "
            + "FROM TelecomPhone t LEFT JOIN t.telecomPhoneBills b "
            + "on t = b.phone "
            + "WHERE (b IS NULL OR b.billDate = "
            + "(select MAX(b2.billDate) from t.telecomPhoneBills b2 WHERE b2.phone.id = b.phone.id)) "
            + "AND ((LOWER(t.name) LIKE LOWER(CONCAT('%' , ?1 , '%')) OR "
            + "LOWER(t.number) LIKE LOWER(CONCAT('%' , ?2 , '%')) OR "
            //Jirra ACC-303
            + "LOWER(t.usageText) LIKE LOWER(CONCAT('%' , ?3 , '%')))) ")
    Page<?> getTelecomeByNameOrNumberOrUsage(String query1,
                                             String query2, String query3, Pageable pageable);

    List<TelecomPhone> findByServiceTypeAndActive(PicklistItem serviceType, Boolean isActive);

    List<TelecomPhone> findByServiceTypeNotAndActive(PicklistItem serviceType, Boolean isActive);

    List<TelecomPhone> findByPrimaryExpenseAndServiceTypeNotAndActiveAndDeleted(Expense expense, PicklistItem serviceType, Boolean isActive, Boolean isDeleted);
}

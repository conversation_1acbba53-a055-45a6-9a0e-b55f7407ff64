package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AccountBalance;
import com.magnamedia.entity.Bucket;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Oct 14, 2020
 *         Jirra ACC-2522
 */

@Repository
public interface AccountBalanceRepository extends BaseRepository<AccountBalance> {
    List<AccountBalance> findByBucketAndToDateGreaterThanEqual(Bucket bucket, Date date);

}

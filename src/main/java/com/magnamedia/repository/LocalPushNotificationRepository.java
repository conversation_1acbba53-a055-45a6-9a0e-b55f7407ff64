package com.magnamedia.repository;

import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.module.type.DDMessagingType;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface LocalPushNotificationRepository extends BaseRepository<PushNotification> {

    @Query("SELECT p FROM PushNotification p " +
            "inner join Template t on LOWER(p.type.code) = LOWER(t.name) " +
            "left join DDMessaging m on m.clientTemplate.id = t.id " +
            "left join DDBankMessaging db on t.id = db.clientTemplate.id " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "WHERE p.recepientType = 'Client' AND p.recepientId = ?1 and " +
                "((m is not null and m.event IN (?2)) OR (db_dd is not null and db_dd.event IN (?2)) OR t.name IN (?3)) and " +
            "(p.relatedEntityId1 = ?4 and p.relatedEntityType1 = 'Contract') " +
            "ORDER BY p.creationDate DESC")
    List<PushNotification> findLastNotificationByEvent(
            String clientId, List<DDMessagingType> event, List<String> templateNames, Long contractId, Pageable pageable);

    List<PushNotification> findByOwnerIdAndOwnerTypeAndCreatorModule(Long id, String t, Long m);

    List<PushNotification> findByOwnerIdAndOwnerTypeAndCreatorModuleAndCreationDateGreaterThan(Long id, String t, Long m, Date d);

    List<PushNotification> findByRecepientIdAndRecepientTypeAndCreatorModuleOrderByCreationDateDesc(String id, String t, Long m);

    @Query("SELECT p FROM PushNotification p " +
            "WHERE p.recepientId = ?1 AND p.recepientType = ?2 AND p.creatorModule = ?3 AND p.location = ?4 AND p.type.code LIKE ?5 " +
            "ORDER BY p.creationDate DESC")
    List<PushNotification> getNotificationsToMoveInbox(String id, String t, Long m, NotificationLocation l, String type);
}
package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Revenue;
import com.magnamedia.entity.Transaction;

import java.util.Date;
import java.util.List;

import com.magnamedia.entity.TransactionDetails;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Created at Nov 24, 2017
 */
@Repository
public interface RevenueRepository extends BaseRepository<Revenue> {

    Boolean existsByCode(String code);

    Boolean existsByName(String name);

    Revenue findByCode(String code);

    @Query("select count(b)>0 from Revenue b WHERE b.code =?2 AND b.id != ?1")
    Boolean existsBeforeByCode(Long id,
                               String code);

    @Query("select count(b)>0 from Revenue b WHERE b.name =?2 AND b.id != ?1")
    Boolean existsBeforeByName(Long id,
                               String name);

    @Query("SELECT SUM (amount) FROM Transaction t "
            + "WHERE t.revenue = ?1 AND t.date >= ?2 AND t.date <= ?3")
    Double calculateRevenuesBetweenTwoDates(
            Revenue revenue, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (amount - (CASE WHEN vatAmount IS NOT NULL THEN vatAmount ELSE 0 END)) FROM Transaction t "
            + "WHERE t.revenue = ?1 AND t.pnlValueDate >= ?2 AND t.pnlValueDate <= ?3 AND (t.accrual = null OR t.accrual = false)")
    Double calculateRevenuesBetweenTwoPnlDates(Revenue revenue, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (amount - (CASE WHEN vatAmount IS NOT NULL THEN vatAmount ELSE 0 END)) FROM Transaction t "
            + "WHERE t.revenue = ?1 AND t.date >= ?2 AND t.date <= ?3 AND (t.accrual = null OR t.accrual = false)")
    Double calculateRevenuesBetweenTwoTransactionDates(Revenue revenue, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.revenue = ?1 AND t.pnlValueDate >= ?2 AND t.pnlValueDate <= ?3 AND vatAmount IS NOT NULL")
    Double calculateRevenuesVatAmountBetweenTwoPnlDates(Revenue revenue, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.revenue = ?1 AND t.date >= ?2 AND t.date <= ?3 AND vatAmount IS NOT NULL")
    Double calculateRevenuesVatAmountBetweenTwoDates(Revenue revenue, Date transactionDate1, Date transactionDate2);

    //Jirra ACC-1389
    @Query("SELECT SUM (td.transactionAmount - (CASE WHEN t.vatAmount IS NOT NULL THEN t.vatAmount ELSE 0 END)) FROM Transaction t"
            + " INNER JOIN t.transactionDetails td"
            + " WHERE td.transactionAmount != 0 AND t.revenue = ?1 AND td.accrualDate >= ?2 AND td.accrualDate <= ?3")
    Double calculateAccrualRevenuesBetweenTwoDates(Revenue revenue, Date transactionDate1, Date transactionDate2);

    //Jirra ACC-1389
    @Query("SELECT SUM (td.averageAmount) FROM Transaction t"
            + " INNER JOIN t.transactionDetails td"
            + " WHERE t.revenue = ?1 AND td.accrualDate >= ?2 AND td.accrualDate <= ?3")
    Double calculateRevenuesAverageBetweenTwoDates(Revenue revenue, Date transactionDate1, Date transactionDate2);

    //Jirra ACC-1389
    @Query("SELECT SUM (td.profitAdjustment) FROM Transaction t"
            + " INNER JOIN t.transactionDetails td"
            + " WHERE t.revenue = ?1 AND td.accrualDate >= ?2 AND td.accrualDate <= ?3")
    Double calculateRevenuesProfitAdjustmentBetweenTwoDates(Revenue revenue, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.revenue = ?1 AND t.pnlValueDate >= ?2 AND t.pnlValueDate <= ?3 "
            + "AND vatType != null AND vatType = com.magnamedia.module.type.VatType.OUT")
    Double calculateOutputVatRevenuesBetweenTwoPnlDates(Revenue revenue, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.revenue = ?1 AND t.date >= ?2 AND t.date <= ?3 "
            + "AND vatType != null AND vatType = com.magnamedia.module.type.VatType.OUT")
    Double calculateOutputVatRevenuesBetweenTwoTransactionDates(Revenue revenue, Date transactionDate1, Date transactionDate2);

    //Jirra ACC-947
    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.revenue = ?1 AND t.pnlValueDate >= ?2 AND t.pnlValueDate <= ?3 "
            + "AND vatType != null AND vatType = com.magnamedia.module.type.VatType.IN")
    Double calculateInputVatRevenuesBetweenTwoPnlDates(Revenue revenue, Date transactionDate1, Date transactionDate2);

    @Query("SELECT SUM (vatAmount) FROM Transaction t "
            + "WHERE t.revenue = ?1 AND t.date >= ?2 AND t.date <= ?3 "
            + "AND vatType != null AND vatType = com.magnamedia.module.type.VatType.IN")
    Double calculateInputVatRevenuesBetweenTwoTransactionDates(Revenue revenue, Date transactionDate1, Date transactionDate2);

    // ACC-496 2) Get all Revenues-transactions behind a row in P&Ls page | Majd Bousaad
    @Query("SELECT t FROM Transaction t "
            + "WHERE (t.accrual = null OR t.accrual = false) AND t.revenue = ?1 AND t.pnlValueDate >= ?2 AND t.pnlValueDate <= ?3")
    List<Transaction> getRevenueTransactionsBetweenTwoPnlDates(Revenue revenue, Date fromDate, Date toDate);

    @Query("SELECT t FROM Transaction t "
            + "WHERE (t.accrual = null OR t.accrual = false) AND t.revenue = ?1 AND t.date >= ?2 AND t.date <= ?3")
    List<Transaction> getRevenueTransactionsBetweenTwoTransactionDates(Revenue revenue, Date fromDate, Date toDate);


    //Jirra ACC-1389
    @Query("SELECT td FROM Transaction t"
            + " INNER JOIN t.transactionDetails td"
            + " WHERE t.revenue = ?1 AND td.accrualDate >= ?2 AND td.accrualDate <= ?3")
    List<TransactionDetails> getRevenueTransactionDetailsBetweenTwoDates(Revenue revenue, Date fromDate, Date toDate);


}
package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BlockLog;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jan 13, 2019
 */
@Repository
public interface BlockLogsRepository extends BaseRepository<BlockLog> {
    public List<BlockLog> findByClientOrderByCreationDateDesc(Client client);
    
    public List<BlockLog> findByContractOrderByCreationDateDesc(Contract contract);
}


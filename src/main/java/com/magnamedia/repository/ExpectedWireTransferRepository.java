package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.ExpectedWireTransfer;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.Transaction;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jan 23, 2019
 * ACC-373
 */
@Repository
public interface ExpectedWireTransferRepository extends BaseRepository<ExpectedWireTransfer> {
    
    public List<ExpectedWireTransfer> findByTransaction(Transaction transaction);
    
    //Jirra ACC-430
    public List<ExpectedWireTransfer> findByPaymentId(Long paymentId);

}

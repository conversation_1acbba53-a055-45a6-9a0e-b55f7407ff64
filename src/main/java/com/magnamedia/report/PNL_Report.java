package com.magnamedia.report;

import com.magnamedia.controller.BaseCompanyReportController;
import com.magnamedia.entity.BaseReportCompany;
import com.magnamedia.entity.BasePLNode;
import com.magnamedia.entity.interfaces.BasePLVariableNode;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.type.PLNodeType;
import org.joda.time.DateTime;

import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Nov 12, 2018
 *         <p>
 *         Jirra ACC-281
 *         Edited  Jirra ACC-385
 */

public class PNL_Report extends BaseReport {

    enum Format {HTML, EXCEL, PDF}

    //Jirra ACC-824
    private DecimalFormat myFormatter = new DecimalFormat("###,###");

    private final BaseReportCompany pLCompany;
    private final Date fromDate;
    private final Date toDate;
    private final String[] headers;
    private final String[] summaryHeaders;
    //Jirra ACC-385
    private boolean colored = true;
    private Integer maxLevel = 2;
    private String baseUrl;
    private Format format;
    //Jirra ACC-804
    private boolean vatRowsAdded;
    private int rowIdx = 1;

    private DateTime before1MonthDate = null;
    private DateTime before2MonthDate = null;
    private DateTime before3MonthDate = null;

    private Map<String, Double> companyOutputVatCollect;
    private Map<String, Double> companyInputVatCollect;

    private boolean withRounding;
    private BaseCompanyReportController.SearchCriteria searchCriteria;

    private ExecutorService executor;

    public PNL_Report(BaseReportCompany pLCompany, Date fromDate, Date toDate, boolean colored, Integer maxLevel, String baseUrl, String format, boolean withRounding
            , BaseCompanyReportController.SearchCriteria searchCriteria) throws Exception {
        this.pLCompany = pLCompany;
        this.fromDate = fromDate;
        this.toDate = toDate;
        DateTime now = new DateTime(fromDate);
        before1MonthDate = now.minusMonths(1).withDayOfMonth(1);
        before2MonthDate = now.minusMonths(2).withDayOfMonth(1);
        before3MonthDate = now.minusMonths(3).withDayOfMonth(1);

        this.headers = new String[]{"Row", "Name", "Amount (AED)", DateUtil.formatMonth(before1MonthDate.toDate()), DateUtil.formatMonth(before2MonthDate.toDate()),
                DateUtil.formatMonth(before3MonthDate.toDate()), "Average", "Profit Adjustment", "Ratio", "Formula"};
        this.summaryHeaders = new String[]{"Row", "Type", "Amount (AED)", "Expense Ratio (Expense Total / Revenues)"};
        this.colored = colored;
        this.maxLevel = maxLevel;
        this.baseUrl = baseUrl;
        this.format = Format.valueOf(format);

        executor = Executors.newFixedThreadPool(4);

        calculateOutputVatCollected(pLCompany);
        calculateInputVatCollected(pLCompany);

        this.withRounding = withRounding;
        this.searchCriteria = searchCriteria;
    }

    private void calculateOutputVatCollected(BaseReportCompany pLCompany) throws Exception {
        this.companyOutputVatCollect = new HashMap();
        Future<Double> currentMonthQueryValue = executor.submit(() -> pLCompany.calculateOutputVATCollected(fromDate, toDate));
        Future<Double> before3MonthQueryValue = executor.submit(() -> pLCompany.calculateOutputVATCollected(before3MonthDate.toDate(), before3MonthDate.dayOfMonth().withMaximumValue().toDate()));
        Future<Double> before2MonthQueryValue = executor.submit(() -> pLCompany.calculateOutputVATCollected(before2MonthDate.toDate(), before2MonthDate.dayOfMonth().withMaximumValue().toDate()));
        Future<Double> before1MonthQueryValue = executor.submit(() -> pLCompany.calculateOutputVATCollected(before1MonthDate.toDate(), before1MonthDate.dayOfMonth().withMaximumValue().toDate()));

        this.companyOutputVatCollect.put("currentMonth", currentMonthQueryValue.get());
        this.companyOutputVatCollect.put("before3Month", before3MonthQueryValue.get());
        this.companyOutputVatCollect.put("before2Month", before2MonthQueryValue.get());
        this.companyOutputVatCollect.put("before1Month", before1MonthQueryValue.get());
    }

    private void calculateInputVatCollected(BaseReportCompany pLCompany) throws Exception {
        this.companyInputVatCollect = new HashMap();
        Future<Double> currentMonthQueryValue = executor.submit(() -> pLCompany.calculateInputVATCollected(fromDate, toDate));
        Future<Double> before3MonthQueryValue = executor.submit(() -> pLCompany.calculateInputVATCollected(before3MonthDate.toDate(), before3MonthDate.dayOfMonth().withMaximumValue().toDate()));
        Future<Double> before2MonthQueryValue = executor.submit(() -> pLCompany.calculateInputVATCollected(before2MonthDate.toDate(), before2MonthDate.dayOfMonth().withMaximumValue().toDate()));
        Future<Double> before1MonthQueryValue = executor.submit(() -> pLCompany.calculateInputVATCollected(before1MonthDate.toDate(), before1MonthDate.dayOfMonth().withMaximumValue().toDate()));

        this.companyInputVatCollect.put("currentMonth", currentMonthQueryValue.get());
        this.companyInputVatCollect.put("before3Month", before3MonthQueryValue.get());
        this.companyInputVatCollect.put("before2Month", before2MonthQueryValue.get());
        this.companyInputVatCollect.put("before1Month", before1MonthQueryValue.get());
    }

    private Cell[] getNodeNameRow(BasePLNode pLNode, boolean isSummary) {
        Cell[] res = new Cell[2];
        res[0] = new Cell("" + rowIdx++);
        res[1] = new Cell(pLNode.getName().replace("&", "&amp;"))
                .withBold(true)
                .withColumnSpan(isSummary ? summaryHeaders.length : headers.length)
                .withAlign(Align.Left);
        if (colored && (pLNode.getReportColor() != null) && (!pLNode.getReportColor().isEmpty()))
            for (Cell c : res)
                c.withStyle("background-color:" + pLNode.getReportColor() + " !important");
        return res;
    }

    private Cell[] getVariableRow(BasePLNode pLVariableNode, boolean isSummary) throws Exception {
        String name;
        Double value = pLVariableNode.getValue();
        Double before1MonthValue = 0.0;
        Double before2MonthValue = 0.0;
        Double before3MonthValue = 0.0;
        Double average = 0.0;
        Double profitAdjustment = 0.0;
        Double ratio = 0.0;
        String formula = "";

        if (format == Format.HTML) {
            //Jirra ACC-587
            name = "<a href='javascript:void(0);' data-toggle='modal' "
                    + "data-target='#showtransactionsbehindrow_modal' "
                    + "class='showdetails' data-id='" + pLVariableNode.getId() + "' total-amount='" + myFormatter.format(pLVariableNode.getValue()) + "'" +
                    " total-vat='" + myFormatter.format(pLVariableNode.getVatAmount()) + "'>"
                    + pLVariableNode.getName().replace("&", "&amp;")
                    + "</a>";
        } else
            name = pLVariableNode.getName().replace("&", "&amp;");

        if (!isSummary) {
            //Jirra ACC-2500
            Future<Double> before3MonthQueryValue = executor.submit(() -> pLVariableNode.calculateValue(before3MonthDate.toDate(), before3MonthDate.dayOfMonth().withMaximumValue().toDate()));
            Future<Double> before2MonthQueryValue = executor.submit(() -> pLVariableNode.calculateValue(before2MonthDate.toDate(), before2MonthDate.dayOfMonth().withMaximumValue().toDate()));
            Future<Double> before1MonthQueryValue = executor.submit(() -> pLVariableNode.calculateValue(before1MonthDate.toDate(), before1MonthDate.dayOfMonth().withMaximumValue().toDate()));

            before3MonthValue = before3MonthQueryValue.get();
            before2MonthValue = before2MonthQueryValue.get();
            before1MonthValue = before1MonthQueryValue.get();

            //Jirra ACC-1389
            average = pLVariableNode.getAverage();
            profitAdjustment = pLVariableNode.getProfitAdjustment();
        }

        //ACC-638
        if (pLVariableNode.getpLNodeType().equals(PLNodeType.REVENUES))
            ratio = pLVariableNode.getRatio() / 10.0;
        else
            ratio = pLVariableNode.getRelatedRatio() / 10.0;

        if (!isSummary) {
            formula = ((BasePLVariableNode) pLVariableNode).getFormula();
        }

        return getBasicRow(name, value, before1MonthValue, before2MonthValue, before3MonthValue, average, profitAdjustment, ratio, formula, isSummary,
                colored && (pLVariableNode.getReportColor() != null) && (!pLVariableNode.getReportColor().isEmpty()), pLVariableNode.getColor(), true);
    }

    private Cell[] getBasicRow(String name, Double value, Double before1MonthValue, Double before2MonthValue,
                               Double before3MonthValue, Double average, Double profitAdjustment, Double ratio, String formula, boolean isSummary, boolean reportColored, String color,
                               boolean isVariable) {
        if (this.withRounding) {
            value = Double.valueOf(floor(value / 1000) * 1000);
            before3MonthValue = Double.valueOf(floor(before3MonthValue / 1000) * 1000);
            before2MonthValue = Double.valueOf(floor(before2MonthValue / 1000) * 1000);
            before1MonthValue = Double.valueOf(floor(before1MonthValue / 1000) * 1000);
        }

        Cell[] res = new Cell[isSummary ? summaryHeaders.length : headers.length];
        res[0] = new Cell("" + rowIdx++);
        if (isVariable)
            res[1] = new Cell(name);
        else
            res[1] = new Cell(name).withBold(true);
        //Jirra ACC-755

        res[2] = new Cell(myFormatter.format(round(value)));

        if (!isSummary) {
            //Jirra ACC-2500
            res[3] = new Cell(myFormatter.format(round(before1MonthValue)));
            res[4] = new Cell(myFormatter.format(round(before2MonthValue)));
            res[5] = new Cell(myFormatter.format(round(before3MonthValue)));

            //Jirra ACC-1389
            res[6] = new Cell(myFormatter.format(round(average)));


            res[7] = new Cell(myFormatter.format(round(profitAdjustment)));
        }

        res[isSummary ? 3 : 8] = new Cell(("%" + String.format("%,.1f", (double) ratio)).replace("&", "&amp;"));

        if (!isSummary) {
            res[9] = new Cell(formula);
        }

        if (reportColored) {
            for (Cell c : res)
                c.withStyle("background-color:" + color + " !important");
        }
        return res;
    }

    private Cell[] getNodeTotalRow(BasePLNode pLNode, boolean isSummary) throws Exception {
        if (pLNode.getParent() == null) {
            return getMainLevelTotalRow(pLNode, isSummary);
        } else {
            return getSubLevelTotalRow(pLNode, isSummary);
        }
    }

    private Cell[] getMainLevelTotalRow(BasePLNode pLNode, boolean isSummary) throws Exception {
        String name = "Total " + pLNode.getName().replace("&", "&amp;");
//        Double value = getTotalValueWithVat(pLNode, fromDate, toDate, "currentMonth");
        Double value = pLNode.getValue();

        if (pLNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
            value += companyOutputVatCollect.get("currentMonth");
        } else {
            value += companyInputVatCollect.get("currentMonth");
        }

        Double before1MonthValue = 0.0;
        Double before2MonthValue = 0.0;
        Double before3MonthValue = 0.0;
        Double average = 0.0;
        Double profitAdjustment = pLNode.getProfitAdjustment();
        Double ratio = 0.0;

        if (!isSummary) {
            //Jirra ACC-2500
            Future<Double> before3MonthQueryValue = executor.submit(() -> getTotalValueWithVat(pLNode, before3MonthDate.toDate(), before3MonthDate.dayOfMonth().withMaximumValue().toDate(), "before3Month"));
            Future<Double> before2MonthQueryValue = executor.submit(() -> getTotalValueWithVat(pLNode, before2MonthDate.toDate(), before2MonthDate.dayOfMonth().withMaximumValue().toDate(), "before2Month"));
            Future<Double> before1MonthQueryValue = executor.submit(() -> getTotalValueWithVat(pLNode, before1MonthDate.toDate(), before1MonthDate.dayOfMonth().withMaximumValue().toDate(), "before1Month"));

            before3MonthValue = before3MonthQueryValue.get();
            before2MonthValue = before2MonthQueryValue.get();
            before1MonthValue = before1MonthQueryValue.get();

            //Jirra ACC-1389
            average = pLNode.getAverage();


            if (pLNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
                profitAdjustment -= Math.abs(companyOutputVatCollect.get("currentMonth"));
            } else {
                profitAdjustment += Math.abs(companyInputVatCollect.get("currentMonth"));
            }
        }

        if (pLNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
            ratio = pLNode.getRatio() / 10.0;
        } else {
            ratio = pLNode.getRelatedRatio() / 10.0;
        }

        return getBasicRow(name, value, before1MonthValue, before2MonthValue, before3MonthValue, average, profitAdjustment, ratio, "", isSummary,
                colored && (pLNode.getReportColor() != null) && (!pLNode.getReportColor().isEmpty()), pLNode.getColor(), false);
    }

    private Double getTotalValueWithVat(BasePLNode plNode, Date fromDate, Date toDate, String key) {
        if (plNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
            return plNode.calculateValue(fromDate, toDate) + companyOutputVatCollect.get(key);
        } else {
            return plNode.calculateValue(fromDate, toDate) + companyInputVatCollect.get(key);
        }
    }

    private Cell[] getSubLevelTotalRow(BasePLNode pLNode, boolean isSummary) throws Exception {
        String name = "Total " + pLNode.getName().replace("&", "&amp;");
//        Double value = pLNode.calculateAndSetValue(fromDate, toDate);
        Double value = pLNode.getValue();
        Double before1MonthValue = 0.0;
        Double before2MonthValue = 0.0;
        Double before3MonthValue = 0.0;
        Double average = 0.0;
        Double profitAdjustment = pLNode.getProfitAdjustment();
        Double ratio = 0.0;

        if (!isSummary) {
            //Jirra ACC-2500
            Future<Double> before3MonthQueryValue = executor.submit(() -> pLNode.calculateValue(before3MonthDate.toDate(), before3MonthDate.dayOfMonth().withMaximumValue().toDate()));
            Future<Double> before2MonthQueryValue = executor.submit(() -> pLNode.calculateValue(before2MonthDate.toDate(), before2MonthDate.dayOfMonth().withMaximumValue().toDate()));
            Future<Double> before1MonthQueryValue = executor.submit(() -> pLNode.calculateValue(before1MonthDate.toDate(), before1MonthDate.dayOfMonth().withMaximumValue().toDate()));

            before3MonthValue = before3MonthQueryValue.get();
            before2MonthValue = before2MonthQueryValue.get();
            before1MonthValue = before1MonthQueryValue.get();
            average = pLNode.getAverage();
        }

        if (pLNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
            ratio = pLNode.getRatio() / 10.0;
        } else {
            ratio = pLNode.getRelatedRatio() / 10.0;
        }

        return getBasicRow(name, value, before1MonthValue, before2MonthValue, before3MonthValue, average, profitAdjustment, ratio, "", isSummary,
                colored && (pLNode.getReportColor() != null) && (!pLNode.getReportColor().isEmpty()), pLNode.getColor(), false);
    }

    private Cell[] getTotalProfitRow() {
        Cell[] res = new Cell[summaryHeaders.length];
        res[0] = new Cell("" + rowIdx++);
        res[1] = new Cell("Net Cash Profit").withBold(true).withBackColor(Color.Yellow);
        //Jirra ACC-755
        res[2] = new Cell(myFormatter.format(round(
                (pLCompany.getRevenuesValue() + companyOutputVatCollect.get("currentMonth")) - (pLCompany.getExpensesValue() + companyInputVatCollect.get("currentMonth")))))
                .withBackColor(Color.Yellow);
        //res[1] = new Cell(String.format("%,.2f", pLCompany.getRevenuesValue()- pLCompany.getExpensesValue())).withBackColor(Color.Yellow);
        res[3] = new Cell("%" + ((pLCompany.getRevenuesValue() != 0D) ? ((double) (1000 - pLCompany.getRelatedExpensesRatio())) / 10 : "0.0ratio")).withBackColor(Color.Yellow);
        return res;
    }

    //Jirra ACC-1389
    private Cell[] getAccrualProfitRow() {
        Cell[] res = new Cell[summaryHeaders.length - 1];
        res[0] = new Cell("" + rowIdx++);
        res[1] = new Cell("Accrual Profit").withBold(true).withBackColor(Color.Yellow);

        Long netCashProfit = round(pLCompany.getRevenuesValue() - pLCompany.getExpensesValue());

        res[2] = new Cell(myFormatter.format(netCashProfit + pLCompany.getProfitAdjustmentsValue() - Math.abs(companyOutputVatCollect.get("currentMonth"))
                + Math.abs(companyInputVatCollect.get("currentMonth")))).withBackColor(Color.Yellow)
                .withColumnSpan(headers.length - 1);
        return res;
    }

    //Jirra ACC-804
    private Cell[] getTotalOutputVATCollectedRow() {
        Cell[] res = new Cell[headers.length];
        res[0] = new Cell("" + rowIdx++);
        res[1] = new Cell("Output VAT Collected").withBold(true).withBackColor(Color.Yellow);
        res[2] = new Cell(myFormatter.format(round(Math.abs(companyOutputVatCollect.get("currentMonth")))))
                .withBackColor(Color.Yellow);
        res[3] = new Cell(myFormatter.format(round(Math.abs(companyOutputVatCollect.get("before1Month")))))
                .withBackColor(Color.Yellow);
        res[4] = new Cell(myFormatter.format(round(Math.abs(companyOutputVatCollect.get("before2Month")))))
                .withBackColor(Color.Yellow);
        res[5] = new Cell(myFormatter.format(round(Math.abs(companyOutputVatCollect.get("before3Month")))))
                .withBackColor(Color.Yellow);
        res[6] = new Cell(myFormatter.format(round(0.0)))
                .withBackColor(Color.Yellow);
        res[7] = new Cell(myFormatter.format(round(-Math.abs(companyOutputVatCollect.get("currentMonth")))))
                .withBackColor(Color.Yellow);
        res[8] = new Cell("")
                .withBackColor(Color.Yellow);
        res[9] = new Cell("")
                .withBackColor(Color.Yellow);
        return res;
    }

    private Cell[] getTotalOutputVATCollectedRowForSummaryTable() {
        Cell[] res = new Cell[summaryHeaders.length];
        res[0] = new Cell("");
        res[1] = new Cell("Output VAT Collected");

        res[2] = new Cell(myFormatter.format(round(Math.abs(companyOutputVatCollect.get("currentMonth")))));
        res[3] = new Cell("");
        return res;
    }


    //Jirra ACC-804
    private Cell[] getTotalInputVATCollectedRow() {
        Cell[] res = new Cell[headers.length];
        res[0] = new Cell("" + rowIdx++);
        res[1] = new Cell("Input VAT paid").withBold(true).withBackColor(Color.Yellow);
        res[2] = new Cell(myFormatter.format(round(Math.abs(companyInputVatCollect.get("currentMonth")))))
                .withBackColor(Color.Yellow);
        res[3] = new Cell(myFormatter.format(round(Math.abs(companyInputVatCollect.get("before1Month")))))
                .withBackColor(Color.Yellow);
        res[4] = new Cell(myFormatter.format(round(Math.abs(companyInputVatCollect.get("before2Month")))))
                .withBackColor(Color.Yellow);
        res[5] = new Cell(myFormatter.format(round(Math.abs(companyInputVatCollect.get("before3Month")))))
                .withBackColor(Color.Yellow);
        res[6] = new Cell(myFormatter.format(round(0.0)))
                .withBackColor(Color.Yellow);
        res[7] = new Cell(myFormatter.format(round(Math.abs(companyInputVatCollect.get("currentMonth")))))
                .withBackColor(Color.Yellow);
        res[8] = new Cell("")
                .withBackColor(Color.Yellow);
        res[9] = new Cell("")
                .withBackColor(Color.Yellow);
        return res;
    }

    private Cell[] getTotalInputVATCollectedRowForSummaryTable() {
        Cell[] res = new Cell[summaryHeaders.length];
        res[0] = new Cell("");
        res[1] = new Cell("Input VAT paid");

        res[2] = new Cell(myFormatter.format(round(Math.abs(companyInputVatCollect.get("currentMonth")))));
        res[3] = new Cell("");
        return res;
    }

    @Override
    public void build() {
        addSection(
                new Text(new TextTitle(pLCompany.getReportTitle())
                        .withAlign(Align.Left)
                        .withBold(true)
                        .withFontSize(Size.Large),
                        ""));

        addSection(
                new Text("",
                        "From Date: " + DateUtil.formatFullDate(fromDate))
                        .withAlign(Align.Left).withBold(true));

        addSection(
                new Text("",
                        "  To Date: " + DateUtil.formatFullDate(toDate))
                        .withAlign(Align.Left).withBold(true));

        List<Column> sumColumnList = new ArrayList<>();

        for (String header : summaryHeaders) {
            sumColumnList.add(new Column(new ColumnTitle(header).withAlign(Align.Center).withBold(true)));
        }
        Column[] sumColumns = new Column[summaryHeaders.length];
        sumColumnList.toArray(sumColumns);
        try {
            buildSummaryTable(sumColumns);

            List<Column> columnList = new ArrayList<>();

            for (String header : headers) {
                columnList.add(new Column(new ColumnTitle(header).withAlign(Align.Center).withBold(true)));
            }
            Column[] columns = new Column[headers.length];
            columnList.toArray(columns);

            for (BasePLNode pLHeadNode : pLCompany.getSortedChildren()) {
                if (colored && (pLHeadNode.getReportColor() != null) && (!pLHeadNode.getReportColor().isEmpty()))
                    for (Column c : columns)
                        c.withStyle("background-color:" + pLHeadNode.getReportColor() + " !important");
                else
                    for (Column c : columns)
                        c.withStyle("");
                buildHeadlevelTable(columns, pLHeadNode, false);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("an error occurred while generating the report, please try again");
        }
    }

    public void buildSummaryTable(Column[] columns) throws Exception {
        TableTitle tt = new TableTitle("Summary Table").withAlign(Align.Center).withBackColor(Color.Grey);
        Table data = new Table(tt, columns);
        for (BasePLNode pLNode : pLCompany.getSortedChildren()) {
            buildNodeBlock(columns, pLNode, data, 1, true);
        }
        data.addRow(getTotalProfitRow());
        data.addRow(getAccrualProfitRow());
        addSection(data);
    }

    public void buildHeadlevelTable(Column[] columns, BasePLNode pLHeadNode, boolean isSummary) throws Exception {
        TableTitle tt = new TableTitle(pLHeadNode.getName()).withAlign(Align.Center);
        if (this.colored && (pLHeadNode.getReportColor() != null) && (!pLHeadNode.getReportColor().isEmpty()))
            tt.withStyle("background-color:" + pLHeadNode.getReportColor() + " !important");
        else
            tt.withBackColor(Color.Grey);
        Table data = new Table(tt, columns);
        for (BasePLNode pLNode : (List<BasePLNode>) pLHeadNode.getSortedChildren()) {
            buildNodeBlock(columns, pLNode, data, null, isSummary);
        }

        //Jirra ACC-2500
        if (pLHeadNode.getpLNodeType() != null && pLHeadNode.getpLNodeType().equals(PLNodeType.EXPENSES)) {
            data.addRow(getTotalInputVATCollectedRow());
        }

        if (pLHeadNode.getpLNodeType() != null && pLHeadNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
            data.addRow(getTotalOutputVATCollectedRow());
        }

        data.addRow(getNodeTotalRow(pLHeadNode, isSummary));

        addSection(data);
    }

    public void buildNodeBlock(Column[] columns, BasePLNode pLNode, Table data, Integer level, boolean isSummary) throws Exception {
        if (level != null && level == maxLevel)
            data.addRow(getVariableRow(pLNode, isSummary));
        else if (pLNode.getType().equals("PLVariableNode"))
            data.addRow(getVariableRow(pLNode, isSummary));
        else {
            data.addRow(getNodeNameRow(pLNode, isSummary));
            for (BasePLNode node : (List<BasePLNode>) pLNode.getSortedChildren()) {
                if (level != null) {
                    Integer level2 = level + 1;
                    buildNodeBlock(columns, node, data, level2, isSummary);
                } else
                    buildNodeBlock(columns, node, data, null, isSummary);
            }

            //Jirra ACC-2681
            if (isSummary && level != null && level.equals(1)) {
                if (pLNode.getpLNodeType() != null && pLNode.getpLNodeType().equals(PLNodeType.EXPENSES)) {
                    data.addRow(getTotalInputVATCollectedRowForSummaryTable());
                }

                if (pLNode.getpLNodeType() != null && pLNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
                    data.addRow(getTotalOutputVATCollectedRowForSummaryTable());
                }
            }

            data.addRow(getNodeTotalRow(pLNode, isSummary));
        }
    }

    private Long round(Double value) {
        if (value < 0)
            return -Math.round(Math.abs(value));
        else return Math.round(value);
    }

    private double floor(Double value) {
        if (value < 0)
            return -Math.floor(Math.abs(value));
        else return Math.floor(value);
    }

}

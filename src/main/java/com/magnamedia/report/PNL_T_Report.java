package com.magnamedia.report;

import com.magnamedia.controller.BaseCompanyReportController;
import com.magnamedia.entity.BasePLNode;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.TransactionDetails;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 */
public class PNL_T_Report extends BaseReport {

    private List<Transaction> transactions;
    private List<TransactionDetails> transactionDetails;
    private String[] headers;
    private Column[] columns;
    private Long seq;
    private BasePLNode pLNode;

    public PNL_T_Report(BasePLNode pLNode, Date fromDate, Date toDate, BaseCompanyReportController.SearchCriteria searchCriteria) {
        this.pLNode = pLNode;
        this.transactions = pLNode.getTransactionsBehindNode(fromDate, toDate, searchCriteria);
        this.transactionDetails = pLNode.getTransactionDetailsBehindNode(fromDate, toDate, searchCriteria);
        this.headers = new String[]{"Seq", "Transaction ID", "Bucket From", "Revenue Name", "Expense Name", "Bucket To",
                "Description", "Amount (AED)", "Average", "Profit Adjustment", "Date of Transaction", "Date of Creation"};
        this.columns = new Column[headers.length];
        for (int i = 0; i < headers.length; i++) {
            columns[i] = new Column(new ColumnTitle(headers[i]).withAlign(Align.Center).withBold(true));
        }
        this.seq = 1L;


    }

    private Cell[] getTransactionRow(Transaction transaction) {
        Cell[] cells = new Cell[this.headers.length];

        // Jirra ACC-1228
        cells[0] = new Cell((seq++).toString()).withAlign(Align.Center);

        cells[1] = new Cell((transaction.getId() != null) ?
                transaction.getId().toString() : " ").withAlign(Align.Center);

        cells[2] = new Cell((transaction.getFromBucket() != null) ?
                transaction.getFromBucket().getName().replace("&", "&amp;") : " ")
                .withAlign(Align.Center);

        cells[3] = new Cell((transaction.getRevenue() != null) ?
                transaction.getRevenue().getName().replace("&", "&amp;") : " ")
                .withAlign(Align.Center);

        cells[4] = new Cell((transaction.getExpense() != null) ?
                transaction.getExpense().getName().replace("&", "&amp;") : " ")
                .withAlign(Align.Center);

        cells[5] = new Cell((transaction.getToBucket() != null) ?
                transaction.getToBucket().getName().replace("&", "&amp;") : " ")
                .withAlign(Align.Center);

        cells[6] = new Cell(transaction.getDescription() == null ? "" : transaction.getDescription().replace("&", "&amp;")).withAlign(Align.Center);
        cells[7] = new Cell(transaction.getAmount().toString().replace("&", "&amp;")).withAlign(Align.Center);

        //Jirra ACC-1389
        cells[8] = new Cell("");
        cells[9] = new Cell("");

        cells[10] = new Cell(transaction.getDate().toString().replace("&", "&amp;")).withAlign(Align.Center);
        cells[11] = new Cell(transaction.getCreationDate().toString().replace("&", "&amp;")).withAlign(Align.Center);
        return cells;
    }

    private Cell[] getTransactionDetailsRow(TransactionDetails transactionDetails) {
        Transaction transaction = transactionDetails.getTransaction();
        Cell[] cells = new Cell[this.headers.length];

        // Jirra ACC-1228
        cells[0] = new Cell((seq++).toString()).withAlign(Align.Center);

        cells[1] = new Cell((transaction.getId() != null) ?
                transaction.getId().toString() : " ").withAlign(Align.Center);

        cells[2] = new Cell((transaction.getFromBucket() != null) ?
                transaction.getFromBucket().getName().replace("&", "&amp;") : " ")
                .withAlign(Align.Center);

        cells[3] = new Cell((transaction.getRevenue() != null) ?
                transaction.getRevenue().getName().replace("&", "&amp;") : " ")
                .withAlign(Align.Center);

        cells[4] = new Cell((transaction.getExpense() != null) ?
                transaction.getExpense().getName().replace("&", "&amp;") : " ")
                .withAlign(Align.Center);

        cells[5] = new Cell((transaction.getToBucket() != null) ?
                transaction.getToBucket().getName().replace("&", "&amp;") : " ")
                .withAlign(Align.Center);

        cells[6] = new Cell(transaction.getDescription().replace("&", "&amp;")).withAlign(Align.Center);
        cells[7] = new Cell(transactionDetails.getTransactionAmount().toString().replace("&", "&amp;")).withAlign(Align.Center);

        //Jirra ACC-1389
        cells[8] = new Cell(transactionDetails.getAverageAmount() != null ? String.format("%.2f", transactionDetails.getAverageAmount()).replace("&", "&amp;") : "").withAlign(Align.Center);
        cells[9] = new Cell(transactionDetails.getProfitAdjustment() != null ? String.format("%.2f", transactionDetails.getProfitAdjustment()).replace("&", "&amp;") : "").withAlign(Align.Center);

        cells[10] = new Cell(transactionDetails.getAccrualDate().toString().replace("&", "&amp;")).withAlign(Align.Center);
        cells[11] = new Cell(transaction.getCreationDate().toString().replace("&", "&amp;")).withAlign(Align.Center);
        return cells;
    }

    @Override
    public void build() {

        Table table = new Table(new TableTitle(this.pLNode.getName().replace("&", "&amp;"))
                .withAlign(Align.Center)
                .withBackColor(Color.Grey), columns);

        for (Transaction transaction : this.transactions) {
            table.addRow(getTransactionRow(transaction));
        }

        //Jirra ACC-1389
        for (TransactionDetails transactionDetails : this.transactionDetails) {
            table.addRow(getTransactionDetailsRow(transactionDetails));
        }

        addSection(table);

    }
}
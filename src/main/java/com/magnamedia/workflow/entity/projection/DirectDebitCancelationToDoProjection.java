package com.magnamedia.workflow.entity.projection;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.workflow.Task;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.rest.core.config.Projection;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 19, 2019
 * Jirra ACC-1134
 */
@Projection(name = "directDebitCancelationToDoProjection",
        types = BaseEntity.class)
public interface  DirectDebitCancelationToDoProjection 
        extends Task{

    Long getId();
    @Value("#{target.getDirectDebitFile() != null ? "
                + "{id: target.getDirectDebitFile().getId(), "
                + "attachments: target.getDirectDebitFile().getAttachments(), "
                + "type: target.getDirectDebitFile().getDdFrequency().getValue(), "
                + "ddaRefNo: target.getDirectDebitFile().getDdaRefNo(), "
                + "contractPaymentTerm: (target.getDirectDebitFile().getDirectDebit() != null && "
                + "target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm() != null ? "
                    + "{id: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getId(), "
                    + "attachments: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getAttachments(), "
                    + "contract: (target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract() != null ? "
                        + "{id: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getId(), "
                        + "client: (target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient() != null ? "
                            + " {id: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient().getId(), "
                            + "name: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient().getName()}"
                            + ": null)} "
                        + ": null), "
                    + "bankName : target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getBankName()} "
                    + ": null)}"
                + ": null}")
    Map<?, ?> getDirectDebit();
}

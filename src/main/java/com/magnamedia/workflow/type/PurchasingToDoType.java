package com.magnamedia.workflow.type;

/**
 * <PERSON> (Jan 31, 2021)
 */
public enum PurchasingToDoType {
    PA_CONFIRM_PURCHASING_REQUEST("confirm purchasing request"),//Purchasing Auditor to confirm purchase request
    PM_GET_BEST_SUPPLIER("get best supplier"),//Purchasing Manager to get best price/supplier
    PA_CONFIRM_BEST_SUPPLIER("confirm best supplier"),//Purchasing Auditor to confirm best price/suppliers
    PM_GET_BETTER_SUPPLIER("get better supplier"),//Purchase Manager to Get better prices
    PM_PURCHASE("purchase");// Purchasing Manager to PURCHASE
//    SK_RECEIVE_ORDER("recive the order"); //Stockkeeper to Receive the order


    PurchasingToDoType(String name) {
        this.name = name;
    }

    private final String name;

    @Override
    public String
    toString() {
        return name;
    }
}

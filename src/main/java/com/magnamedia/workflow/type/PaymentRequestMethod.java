package com.magnamedia.workflow.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Aug 22, 2019
 * ACC-837
 */
public enum PaymentRequestMethod implements LabelValueEnum {
    CASH("Cash"),
    SALARY("Salary"),
    WIRE_TRANSFER("Wire Transfer"),
    ANSARI_TRANSFER("Ansari Transfer");

    private final String label;

    private PaymentRequestMethod(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}

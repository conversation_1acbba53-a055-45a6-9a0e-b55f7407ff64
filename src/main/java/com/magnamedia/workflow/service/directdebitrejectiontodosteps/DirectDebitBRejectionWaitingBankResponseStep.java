package com.magnamedia.workflow.service.directdebitrejectiontodosteps;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.module.type.DirectDebitRejectionToDoType;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 4-4-2020
 * Jirra ACC-1595
 */
@Service
public class DirectDebitBRejectionWaitingBankResponseStep
        extends DirectDebitRejectionToDoManualStep<DirectDebitRejectionToDo> {


    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;

    public DirectDebitBRejectionWaitingBankResponseStep() {
        this.setId(DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B.toString());
    }

    @Override
    public void onSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void postSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void onDone(DirectDebitRejectionToDo entity) {
        super.onDone(entity);
        DirectDebit directDebit = entity.getDirectDebits().get(entity.getDirectDebits().size() - 1);

        DirectDebitRejectionFlowService directDebitRejectionFlowService = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);

        boolean allManualRejected = directDebitRejectionFlowService.allManualFilesRejected(directDebit.getDirectDebitFiles());
        boolean allAutoRejected = directDebitRejectionFlowService.allAutoFilesRejected(directDebit.getDirectDebitFiles());

        if (directDebit.getStatus() == DirectDebitStatus.CONFIRMED && allManualRejected) {
            Integer maxManualTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_DD_MAX_MANUAL_TRIALS));

            if (entity.getManualDDBTrials() < maxManualTrials) {
                List<DirectDebitFile> manualDDs = directDebit.getDirectDebitFiles().stream()
                        .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL &&
                                ddf.getTrialNumber() == entity.getManualDDBTrialsPatch())
                        .limit(Math.min(
                                directDebit.getDdConfiguration().getNumberOfGeneratedDDs(),
                                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                                        AccountingModule.PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES))))
                        .collect(Collectors.toList());

                for (DirectDebitFile manualDD : manualDDs) {
                    directDebitFileRepository.save(manualDD.clone(entity.getManualDDBTrialsPatch() + 1));
                }

                entity.setManualDDBTrials(entity.getManualDDBTrials() + 1);
                entity.setManualDDBTrialsPatch(entity.getManualDDBTrialsPatch() + 1);
                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B.toString());

            } else {
                entity.setManualDDBTrials(0);
                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_RE_SCHEDULE_B.toString());
            }
        } else if (allAutoRejected && directDebit.getMStatus() == DirectDebitStatus.CONFIRMED) {
            Integer maxAutoTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_DD_MAX_AUTO_TRIALS));

            List<DirectDebitFile> autoDDs = directDebit.getDirectDebitFiles().stream()
                    .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.AUTOMATIC &&
                            ddf.getTrialNumber() == entity.getAutoDDBTrialsPatch())
                    .limit(Math.min(
                            directDebit.getDdConfiguration().getNumberOfGeneratedDDs(),
                            Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                                    AccountingModule.PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES))))
                    .collect(Collectors.toList());

            if (entity.getAutoDDBTrials() < maxAutoTrials) {
                for (DirectDebitFile autoDD : autoDDs) {
                    directDebitFileRepository.save(autoDD.clone(entity.getAutoDDBTrialsPatch() + 1));
                }

                entity.setAutoDDBTrials(entity.getAutoDDBTrials() + 1);
                entity.setAutoDDBTrialsPatch(entity.getAutoDDBTrialsPatch() + 1);
                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B.toString());
            } else {
                if (!autoDDs.isEmpty()) {
                    Date startDate = autoDDs.get(0).getStartDate();
                    Date newStartDate = new LocalDate(startDate).plusMonths(2).toDate();
                    if (newStartDate.before(autoDDs.get(0).getExpiryDate())) {
                        entity.setAutoDDBTrials(0);
                        addNewTask(entity, DirectDebitRejectionToDoType.WAITING_RE_SCHEDULE_B.toString());
                    } else {
                        entity.setStopped(true);
                        entity.setCompleted(true);
                    }
                }
            }

        }
        directDebitRejectionToDoRepository.save(entity);
    }

    @Override
    public void postDone(DirectDebitRejectionToDo entity) {
    }
}

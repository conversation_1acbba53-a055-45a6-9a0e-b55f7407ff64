package com.magnamedia.workflow.service.directdebitrejectiontodosteps;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.*;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 4-4-2020
 *         Jirra ACC-1595
 */
@Service
public class DirectDebitARejectionWaitingAccountantActionStep extends DirectDebitRejectionToDoManualStep<DirectDebitRejectionToDo> {

    @Autowired
    private DirectDebitRepository directDebitRepository;

    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private DirectDebitRejectionFlowService directDebitRejectionFlowService;

    private static final Logger logger =
            Logger.getLogger(DirectDebitARejectionWaitingAccountantActionStep.class.getName());

    public DirectDebitARejectionWaitingAccountantActionStep() {
        this.setId(DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION.toString());
    }

    @Override
    public void onSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void postSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void onDone(DirectDebitRejectionToDo entity) {

        DirectDebit rejectedDirectDebit = entity.getLastDirectDebit();
        boolean leadingRejectionFlow = false;

        if (rejectedDirectDebit != null) {
            rejectedDirectDebit = directDebitRepository.findOne(rejectedDirectDebit.getId());

            if (entity.getSendToClient()) {
                entity.setTrials(entity.getTrials() + 1);
                entity.setReminder(0);
                entity.setDontSendDdMessage(false);
                entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());

                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE.toString());

                leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                        rejectedDirectDebit.getContractPaymentTerm().getContract(), Arrays.asList(rejectedDirectDebit.getId()));

                if (rejectedDirectDebit.getCategory() == DirectDebitCategory.A) {
                    rejectedDirectDebit.setMStatus(DirectDebitStatus.REJECTED);
                } else {
                    rejectedDirectDebit.setStatus(DirectDebitStatus.REJECTED);
                    rejectedDirectDebit.setMStatus(DirectDebitStatus.REJECTED);
                }

                for (DirectDebitFile directDebitFile : rejectedDirectDebit.getDirectDebitFiles()) {
                    directDebitFile.setNeedAccountantReConfirmation(false);
                    directDebitFile.setStatus(DirectDebitFileStatus.REJECTED);
                    directDebitFile.setDdStatus(DirectDebitStatus.REJECTED);
                    directDebitFile.setIsFromAccountantAction(true);

                    if (directDebitFile.getRejectCategory().equals(DirectDebitRejectCategory.Signature))
                        directDebitSignatureService.updateSignatureStatus(directDebitFile, DirectDebitSignatureStatus.REJECTED);

                    directDebitFile = Setup.getApplicationContext().getBean(DirectDebitStatusChangeService.class)
                            .ddFileRejected(directDebitFile);

                    directDebitFileRepository.save(directDebitFile);
                }

                directDebitRepository.save(rejectedDirectDebit);

                DirectDebit newDD = rejectedDirectDebit.clone(DirectDebitStatus.IN_COMPLETE);
                newDD.setNonCompletedInfo(true);
                newDD.setConfirmedBankInfo(false);
                newDD.setDirectDebitRejectionToDo(entity);
                newDD.setAttachments(new ArrayList<>());
                directDebitRepository.save(newDD);

                Integer maxTrials =
                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_TRIALS));

                if (entity.getTrials() > maxTrials) {
                    Contract contract = rejectedDirectDebit.getContractPaymentTerm().getContract();
                    
                    if (contract.isTerminateContractDueRejection() || !entity.isDdAddedByOecFlow()) {
                        logger.log(Level.INFO, "terminateContractDueRejection: " + contract.isTerminateContractDueRejection());
                        logger.log(Level.INFO, "isDdAddedByOecFlow: " + entity.isDdAddedByOecFlow());

                        entity.setContractScheduleDateOfTermination(
                                directDebitRejectionFlowService
                                        .setContractForTermination(
                                                rejectedDirectDebit.getContractPaymentTerm(),
                                                "direct_debit_rejection_type_a_maxbankinfotrials_reached",
                                                entity));
                        entity.setLeadingRejectionFlow(true);
                    }
                    entity.setCompleted(true);
                    entity.setStopped(true);
                }

            } else {
                if (entity.getEid() != null) {
                    rejectedDirectDebit.setEid(entity.getEid());
                } else if (entity.getAccountName() != null) {
                    rejectedDirectDebit.setAccountName(entity.getAccountName());
                } else {
                    logger.log(Level.INFO, "eid and account name is null");
                    return;
                }

                if (rejectedDirectDebit.getCategory() == DirectDebitCategory.A) {
                    rejectedDirectDebit.setMStatus(DirectDebitStatus.PENDING);
                } else {
                    rejectedDirectDebit.setStatus(DirectDebitStatus.PENDING);
                    rejectedDirectDebit.setMStatus(DirectDebitStatus.PENDING);
                }

                rejectedDirectDebit.setConfirmedBankInfo(true);

                for (DirectDebitFile directDebitFile : rejectedDirectDebit.getDirectDebitFiles()) {
                    directDebitFile.setNeedAccountantReConfirmation(false);
                    directDebitFile.setStatus(DirectDebitFileStatus.NOT_SENT);
                    directDebitFile.setDdStatus(DirectDebitStatus.PENDING);
                    directDebitFile.setConfirmedBankInfo(true);
                    if (entity.getEid() != null) {
                        directDebitFile.setEid(entity.getEid());
                    } else if (entity.getAccountName() != null) {
                        directDebitFile.setAccountName(entity.getAccountName());
                    }

                    if (!Setup.getApplicationContext().getBean(DirectDebitService.class)
                            .createDirectDebitActivationAttachmentIfNotExist(directDebitFile)) {

                        directDebitFileRepository.save(directDebitFile);
                        directDebitSignatureService.updateSignatureStatus(directDebitFile, DirectDebitSignatureStatus.UNUSED);
                    }

                }

                if (entity.getTrials() == 0 && entity.getDirectDebits().size() == 1) {
                    entity.setStopped(true);
                    entity.setCompleted(true);
                    entity.setDontSendDdMessage(true);
                    rejectedDirectDebit.setDirectDebitRejectionToDo(null);

                } else {
                    entity.setDontSendDdMessage(true);
                    addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE.toString());
                }
                directDebitRepository.save(rejectedDirectDebit);
            }
        } else {
            throw new RuntimeException("lastDirectDebit can't be null");
        }

        entity.setLeadingRejectionFlow(leadingRejectionFlow);
        super.onDone(entity);
    }

    @Override
    public void postDone(DirectDebitRejectionToDo entity) {

    }
}

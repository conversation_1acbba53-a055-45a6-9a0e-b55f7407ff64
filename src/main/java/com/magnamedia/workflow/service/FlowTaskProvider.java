package com.magnamedia.workflow.service;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.workflow.WorkflowComponent;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.SearchField;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.workflow.Task;
import com.magnamedia.core.workflow.TasksHolder;
import com.magnamedia.core.workflow.TasksManager;
import com.magnamedia.core.workflow.Workflow;
import com.magnamedia.entity.ClientRefundSetup;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.extra.UserLibrary;
import com.magnamedia.helper.UserHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.ClientRefundSetupRepository;
import com.magnamedia.service.ClientRefundService;
import com.magnamedia.workflow.entity.projection.*;
import com.magnamedia.workflow.type.ClientRefundPaymentMethod;
import com.magnamedia.workflow.type.ClientRefundStatus;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.projection.ProjectionFactory;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Aug 23, 2019
 *         ACC-837  ACC-1134
 */
@WorkflowComponent
public class FlowTaskProvider implements TasksManager {

    @Autowired
    private DirectDebitCancelationToDoFlow directDebitCancelationToDoFlow;
    @Autowired
    private ExpensePaymentFlow expensePaymentFlow;
    @Autowired
    private PurchasingFlow purchasingFlow;
    @Autowired
    private DirectDebitRejectionToDoFlow directDebitRejectionToDoFlow;
    @Autowired
    private ClientRefundFlow clientRefundFlow;
    @Autowired
    private ExpenseRequestFlow expenseRequestFlow;
    @Autowired
    private BucketReplenishmentFlow bucketReplenishmentFlow;
    @Autowired
    private MaintenanceRequestFlow maintenanceRequestFlow;

    @Override
    public Map<String, TasksHolder> getTasks(User user, SelectFilter filter, Boolean adminMode, ObjectNode objectNode, Boolean withContentFlag) {

        Map<String, TasksHolder> requests = new HashMap<>();
        try {
            requests.put("directDebitCancelationToDoFlow", DirectDebitCancelationTasks(user, filter, adminMode, null, objectNode, withContentFlag));
            requests.put("directDebitRejectionDoFlow", DirectDebitRejectionTasks(user, filter, adminMode, null, objectNode, withContentFlag)); // acc-1595
            requests.put("clientRefundFlow", ClientRefundTasks(user, filter, adminMode, null, objectNode, withContentFlag));// ACC-2845
            requests.put("expenseRequestFlow", ExpenseRequestTasks(user, filter, adminMode, null, objectNode, withContentFlag)); // Acc-2913
            requests.put("bucketReplenishmentFlow", BucketReplenishmentFlow(user, filter, adminMode, null, objectNode, withContentFlag));
            requests.put(ExpensePaymentFlow.flowName, expensePaymentTasks(user, filter, adminMode, null, objectNode, withContentFlag));
            requests.put(PurchasingFlow.flowName, purchasingTasks(user, filter, adminMode, null, objectNode, withContentFlag));
            requests.put(MaintenanceRequestFlow.flowName, maintenanceRequestTasks(user, filter, adminMode, null, objectNode, withContentFlag));
        } catch (IOException ex) {
            Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
        }
        return requests;
    }


    @Override
    public Map<String, TasksHolder> getTasks(User user, SelectFilter filter, String processName, Boolean adminMode, ObjectNode objectNode, Boolean withContentFlag) {

        Map<String, TasksHolder> requests = new HashMap<>();
        switch (processName) {
//            case "paymentrequestworkflow": {
//                try {
//                    requests.put("paymentrequestworkflow", getPaymentRequesTasks(user, filter, adminMode, null, objectNode, withContentFlag));
//                } catch (IOException ex) {
//                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
//                }
//                break;
//            }
            case "directDebitCancelationToDoFlow": {
                try {
                    requests.put("directDebitCancelationToDoFlow", DirectDebitCancelationTasks(user, filter, adminMode, null, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
            // jira acc-1595
            case "directDebitRejectionToDoFlow": {
                try {
                    requests.put("directDebitRejectionToDoFlow", DirectDebitRejectionTasks(user, filter, adminMode, null, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
            //Jirra ACC-2845
            case "clientRefundFlow": {
                try {
                    requests.put("clientRefundFlow", ClientRefundTasks(user, filter, adminMode, null, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }

            //Jirra ACC-2913
            case "expenseRequestFlow": {
                try {
                    requests.put("expenseRequestFlow", ExpenseRequestTasks(user, filter, adminMode, null, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }

            case ExpensePaymentFlow.flowName: {
                try {
                    requests.put(ExpensePaymentFlow.flowName, expensePaymentTasks(user, filter, adminMode, null, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
            case PurchasingFlow.flowName: {
                try {
                    requests.put(PurchasingFlow.flowName, purchasingTasks(user, filter, adminMode, null, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
            case MaintenanceRequestFlow.flowName: {
                try {
                    requests.put(MaintenanceRequestFlow.flowName, maintenanceRequestTasks(user, filter, adminMode, null, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
        }
        return requests;
    }

    @Override
    public Map<String, TasksHolder> getTasks(User user, SelectFilter filter, String processName, String taskName, Boolean adminMode, ObjectNode objectNode, Boolean withContentFlag) {

        Map<String, TasksHolder> requests = new HashMap<>();
        switch (processName) {
//            case "paymentrequestworkflow": {
//                try {
//                    requests.put("paymentrequestworkflow", getPaymentRequesTasks(user, filter, adminMode, taskName, objectNode, withContentFlag));
//                } catch (IOException ex) {
//                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
//                }
//                break;
//            }
            case "directDebitCancelationToDoFlow": {
                try {
                    requests.put("directDebitCancelationToDoFlow", DirectDebitCancelationTasks(user, filter, adminMode, taskName, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
            // jira acc-1595
            case "directDebitRejectionToDoFlow": {
                try {
                    requests.put("directDebitRejectionToDoFlow", DirectDebitRejectionTasks(user, filter, adminMode, taskName, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
            //Jirra ACC-2845
            case "clientRefundFlow": {
                try {
                    requests.put("clientRefundFlow", ClientRefundTasks(user, filter, adminMode, taskName, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
            //Jirra ACC-2913
            case "expenseRequestFlow": {
                try {
                    requests.put("expenseRequestFlow", ExpenseRequestTasks(user, filter, adminMode, taskName, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
            case ExpensePaymentFlow.flowName: {
                try {
                    requests.put(ExpensePaymentFlow.flowName, expensePaymentTasks(user, filter, adminMode, taskName, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
            case PurchasingFlow.flowName: {
                try {
                    requests.put(PurchasingFlow.flowName, purchasingTasks(user, filter, adminMode, taskName, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
            case MaintenanceRequestFlow.flowName: {
                try {
                    requests.put(MaintenanceRequestFlow.flowName, maintenanceRequestTasks(user, filter, adminMode, taskName, objectNode, withContentFlag));
                } catch (IOException ex) {
                    Logger.getLogger(FlowTaskProvider.class.getName()).log(Level.SEVERE, null, ex);
                }
                break;
            }
        }
        return requests;
    }

    @Override
    public List<SearchField> getSearchbleFields(String processName, String taskName) {

        if (taskName == null) {
            if (processName != null) {
                switch (processName) {
//                    case "paymentrequestworkflow": {
//                        return paymentRequestWorkFlow.getProcessFilters();
//                    }
                    case "directDebitCancelationToDoFlow": {
                        return directDebitCancelationToDoFlow.getProcessFilters();
                    }
                    // jira acc-1595
                    case "directDebitRejectionToDoFlow": {
                        return directDebitRejectionToDoFlow.getProcessFilters();
                    }
                    //Jirra ACC-2845
                    case "clientRefundFlow": {
                        return clientRefundFlow.getProcessFilters();
                    }
                    case ExpensePaymentFlow.flowName: {
                        return expensePaymentFlow.getProcessFilters();
                    }
                    case PurchasingFlow.flowName: {
                        return purchasingFlow.getProcessFilters();
                    }
                    case MaintenanceRequestFlow.flowName: {
                        return maintenanceRequestFlow.getProcessFilters();
                    }
                    //Jirra ACC-2913
                    case "expenseRequestFlow": {
                        return expenseRequestFlow.getProcessFilters();
                    }
                    default: {
                        return null;
                    }
                }
            }
        } else {
            if (processName != null) {
                switch (processName) {
//                    case "paymentrequestworkflow": {
//                        return paymentRequestWorkFlow.getIdStepMap().containsKey(taskName)
//                                ? paymentRequestWorkFlow.getIdStepMap().get(taskName).getTaskFilters()
//                                : null;
//                    }
                    case "directDebitCancelationToDoFlow": {
                        return directDebitCancelationToDoFlow.getIdStepMap().containsKey(taskName)
                                ? directDebitCancelationToDoFlow.getIdStepMap().get(taskName).getTaskFilters()
                                : null;
                    }
                    // jira acc-1595
                    case "directDebitRejectionToDoFlow": {
                        return directDebitRejectionToDoFlow.getIdStepMap().containsKey(taskName)
                                ? directDebitRejectionToDoFlow.getIdStepMap().get(taskName).getTaskFilters()
                                : null;
                    }
                    //Jirra ACC-2845
                    case "clientRefundFlow": {
                        return clientRefundFlow.getIdStepMap().containsKey(taskName)
                                ? clientRefundFlow.getIdStepMap().get(taskName).getTaskFilters()
                                : null;
                    }
                    //Jirra ACC-2913
                    case "expenseRequestFlow": {
                        return expenseRequestFlow.getIdStepMap().containsKey(taskName)
                                ? expenseRequestFlow.getIdStepMap().get(taskName).getTaskFilters()
                                : null;
                    }
                    case ExpensePaymentFlow.flowName: {
                        return expensePaymentFlow.getIdStepMap().containsKey(taskName) ?
                                expensePaymentFlow.getIdStepMap().get(taskName).getTaskFilters() : null;
                    }
                    case PurchasingFlow.flowName: {
                        return purchasingFlow.getIdStepMap().containsKey(taskName) ?
                                purchasingFlow.getIdStepMap().get(taskName).getTaskFilters() : null;
                    }
                    case MaintenanceRequestFlow.flowName: {
                        return maintenanceRequestFlow.getIdStepMap().containsKey(taskName) ?
                                maintenanceRequestFlow.getIdStepMap().get(taskName).getTaskFilters() : null;
                    }
                    default: {
                        return null;
                    }
                }
            }
        }
        return null;
    }

    // ACC-7699
    @Override
    public List<SearchField> getDefaultSearchableFields(String processName, String taskName) {
        if (processName == null) return TasksManager.super.getDefaultSearchableFields(processName, taskName);

        switch (processName) {
            case "directDebitCancelationToDoFlow": {
                return directDebitCancelationToDoFlow.getDefaultSearchableFields();
            }
            case "directDebitRejectionToDoFlow": {
                return directDebitRejectionToDoFlow.getDefaultSearchableFields();
            }
            case "clientRefundFlow": {
                return clientRefundFlow.getDefaultSearchableFields();
            }
            case ExpensePaymentFlow.flowName: {
                return expensePaymentFlow.getDefaultSearchableFields();
            }
            case PurchasingFlow.flowName: {
                return purchasingFlow.getDefaultSearchableFields();
            }
            case MaintenanceRequestFlow.flowName: {
                return maintenanceRequestFlow.getDefaultSearchableFields();
            }
            case "expenseRequestFlow": {
                return expenseRequestFlow.getDefaultSearchableFields();
            } default: {
                return TasksManager.super.getDefaultSearchableFields(processName, taskName);
            }
        }
    }

    @Override
    public String getSummry(List requests, Workflow workflow) {
        return "";
    }

    private TasksHolder DirectDebitCancelationTasks(
            User user, SelectFilter filter, Boolean adminMode, String taskName,
            ObjectNode objectNode, Boolean withContentFlag) throws IOException {

        ProjectionFactory pf = Setup.getProjectionFactory();
        if (filter == null) {
            filter = new SelectFilter();
        }
        filter.and("directDebitFile.ddStatus", "in", Arrays.asList(DirectDebitStatus.CONFIRMED, DirectDebitStatus.EXPIRED));
        filter.and("hidden", "=", false);
        List<String> joins = new ArrayList<>();
        Map<String, String> joinType = new HashMap<>();

        TasksHolder tasksHolder = new TasksHolder();
        Map<String, List<Task>> map = new HashMap<>();

        orderSteps(directDebitCancelationToDoFlow, user, map, objectNode);

        ((adminMode != null && adminMode)
                ? directDebitCancelationToDoFlow.getAllTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)
                : directDebitCancelationToDoFlow.getTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)).stream()
                .forEach(e
                        -> {
                    map.get(e.getTempTaskName())
                            .add(pf.createProjection(DirectDebitCancelationToDoProjection.class, e));
                });
        processTasksHolder(tasksHolder, map, directDebitCancelationToDoFlow, "Direct Debit Cancelation ToDo Flow");
        return tasksHolder;
    }

    // jira acc-1595
    private TasksHolder DirectDebitRejectionTasks(
            User user, SelectFilter filter, Boolean adminMode, String taskName,
            ObjectNode objectNode, Boolean withContentFlag) throws IOException {

        ProjectionFactory pf = Setup.getProjectionFactory();
        if (filter == null) {
            filter = new SelectFilter();
        }
        Map<String, String> joinType = new HashMap<>();
        List<String> joins = new ArrayList<>();

        Map<String, List<Task>> map = new HashMap<>();
        TasksHolder tasksHolder = new TasksHolder();

        orderSteps(directDebitRejectionToDoFlow, user, map, objectNode);

        ((adminMode != null && adminMode)
                ? directDebitRejectionToDoFlow.getAllTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)
                : directDebitRejectionToDoFlow.getTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)).stream()
                .forEach(e
                        -> {
                    map.get(e.getTempTaskName())
                            .add(pf.createProjection(DirectDebitRejectionToDoProjection.class, e));
                });
        processTasksHolder(tasksHolder, map, directDebitRejectionToDoFlow, "Direct Debit Rejection ToDo Flow");
        return tasksHolder;
    }

    // Jirra ACC-2845
    public TasksHolder ClientRefundTasks(
            User user, SelectFilter filter, Boolean adminMode, String taskName,
            ObjectNode objectNode, Boolean withContentFlag) throws IOException {

        ProjectionFactory pf = Setup.getProjectionFactory();
        if (filter == null) {
            filter = new SelectFilter();
        }

        Map<String, String> joinType = new HashMap<>();
        List<String> joins = new ArrayList<>();
        processClientRefundFilters(user, filter, taskName);

        Map<String, List<Task>> map = new HashMap<>();
        TasksHolder tasksHolder = new TasksHolder();

        orderSteps(clientRefundFlow, user, map, objectNode);

        ((adminMode != null && adminMode)
                ? clientRefundFlow.getAllTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)
                : clientRefundFlow.getTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)).stream()
                .forEach(e -> map.get(e.getTempTaskName()).add(pf.createProjection(ClientRefundFlowProjection.class, e)));

        processTasksHolder(tasksHolder, map, clientRefundFlow, "Client Refund Flow");
        return tasksHolder;
    }


    private TasksHolder expensePaymentTasks(User user, SelectFilter filter, Boolean adminMode, String taskName, ObjectNode objectNode, Boolean withContentFlag) throws IOException {
        ProjectionFactory pf = Setup.getProjectionFactory();
        if (filter == null) {
            filter = new SelectFilter();
        }
        Map<String, String> joinType = new HashMap<>();
        List<String> joins = new ArrayList<>();

        processExpensePaymentFilters(user, filter, joins, joinType, taskName);


        Map<String, List<Task>> map = new HashMap<>();
        TasksHolder tasksHolder = new TasksHolder();

        orderSteps(expensePaymentFlow, user, map, objectNode);

        ((adminMode != null && adminMode)
                ? expensePaymentFlow.getAllTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)
                : expensePaymentFlow.getTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)).stream()
                .forEach(e -> map.get(e.getTempTaskName()).add(pf.createProjection(ExpensePaymentFlowProjection.class, e)));
        processTasksHolder(tasksHolder, map, expensePaymentFlow, "Expense Payement Flow");
        return tasksHolder;

    }

    private TasksHolder purchasingTasks(User user, SelectFilter filter, Boolean adminMode, String taskName, ObjectNode objectNode, Boolean withContentFlag) throws IOException {
        ProjectionFactory pf = Setup.getProjectionFactory();
        if (filter == null) {
            filter = new SelectFilter();
        }
        Map<String, String> joinType = new HashMap<>();
        List<String> joins = new ArrayList<>();

        processPurchasingFilters(user, filter, joins, joinType, taskName);


        Map<String, List<Task>> map = new HashMap<>();
        TasksHolder tasksHolder = new TasksHolder();

        orderSteps(purchasingFlow, user, map, objectNode);

        ((adminMode != null && adminMode)
                ? purchasingFlow.getAllTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)
                : purchasingFlow.getTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)).stream()
                .forEach(e -> map.get(e.getTempTaskName()).add(pf.createProjection(PurchasingFlowProjection.class, e)));
        processTasksHolder(tasksHolder, map, purchasingFlow, "purchase Flow");
        return tasksHolder;
    }

    private TasksHolder maintenanceRequestTasks(User user, SelectFilter filter, Boolean adminMode, String taskName, ObjectNode objectNode, Boolean withContentFlag) throws IOException {
        ProjectionFactory pf = Setup.getProjectionFactory();
        if (filter == null) {
            filter = new SelectFilter();
        }
        Map<String, String> joinType = new HashMap<>();
        List<String> joins = new ArrayList<>();

        processMaintinanceFilters(user, filter, joins, joinType, taskName);


        Map<String, List<Task>> map = new HashMap<>();
        TasksHolder tasksHolder = new TasksHolder();

        orderSteps(maintenanceRequestFlow, user, map, objectNode);

        ((adminMode != null && adminMode)
                ? maintenanceRequestFlow.getAllTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)
                : maintenanceRequestFlow.getTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)).stream()
                .forEach(e -> map.get(e.getTempTaskName()).add(pf.createProjection(MaintenanceRequestFlowProjection.class, e)));
        processTasksHolder(tasksHolder, map, maintenanceRequestFlow, "maintenance request flow");
        return tasksHolder;
    }

    private void processMaintinanceFilters(User user, SelectFilter filter, List<String> joins, Map<String, String> joinType, String taskName) {
    }

    private void processPurchasingFilters(User user, SelectFilter filter, List<String> joins, Map<String, String> joinType, String taskName) {
    }

    private void processExpensePaymentFilters(User user, SelectFilter filter, List<String> joins, Map<String, String> joinType, String taskName) {
    }


    private SelectFilter processClientRefundFilters(
            User user, SelectFilter filter, String taskName) {

        if (taskName == null || taskName.isEmpty()) {
            return null;
        }

        if (taskName.equals(ClientRefundTodoType.WAITING_COO_APPROVAL.toString())) {
            filter.and("status", "=", ClientRefundStatus.PENDING);
            filter.and("ceoAction", "IS NULL", null);
        } else if (taskName.equals(ClientRefundTodoType.WAITING_MANAGER_APPROVAL.toString()) ||
                taskName.equals(ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_MANAGER_APPROVAL.toString())) {

            if (taskName.equals(ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_MANAGER_APPROVAL.toString())) {
                filter.and("conditionalRefund", "=", true);
            }

            SelectFilter setupFilters = getPurposeFilter(Setup.getRepository(ClientRefundSetupRepository.class)
                    .findByApprovedBy(user));

            User creditCardDefaultManager = Setup.getApplicationContext().
                    getBean(ClientRefundService.class)
                    .getDefaultCreditCardRefundManager();
            if (creditCardDefaultManager != null && creditCardDefaultManager.getId().equals(user.getId())) {
                setupFilters.or(new SelectFilter("methodOfPayment", "=", ClientRefundPaymentMethod.CREDIT_CARD)
                        .and(getPurposeFilter(Setup.getRepository(ClientRefundSetupRepository.class)
                            .findByAutoApprovedTrueAndApprovedByIsNullOrApprovedByIn(UserHelper.getCooUser()))));
            }

            filter.and("status", "=", ClientRefundStatus.PENDING);
            filter.and("managerAction", "IS NULL", null);
            filter.and(setupFilters);
        } else if (taskName.equals(ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_COO_APPROVAL.toString())) {

            // If current User Has not the position -> return empty result
            if (!UserLibrary.currentUserHasPositionCode(AccountingModule.POSITION_COLLECTION_MANAGER_CONDITIONAL_REFUND)) {
                return filter.and("id", "=", -1);
            }

            filter.and("status", "=", ClientRefundStatus.PENDING);
            filter.and("ceoAction", "IS NULL", null);
            filter.and("conditionalRefund", "=", true);
        }

        return filter;
    }

    private SelectFilter getPurposeFilter(List<ClientRefundSetup> setups){
        SelectFilter partialRefundFilter = new SelectFilter("partialRefundForCancellationPaymentMethod", "is not null", null)
                .and("purpose", "in", setups.stream()
                        .filter(s -> s.getPartialRefundForCancellationPaymentMethod() != null &&
                                !s.getPartialRefundForCancellationPaymentMethod().hasTag("deprecated"))
                        .map(ClientRefundSetup::getPaymentRequestPurpose)
                        .collect(Collectors.toList()))
                .and("partialRefundForCancellationPaymentMethod", "in", setups.stream()
                        .filter(s -> s.getPartialRefundForCancellationPaymentMethod() != null)
                        .map(ClientRefundSetup::getPartialRefundForCancellationPaymentMethod)
                        .collect(Collectors.toList()));
        SelectFilter nonPartialRefundFilter = new SelectFilter("partialRefundForCancellationPaymentMethod", "is null", null)
                .and("purpose", "in", setups.stream()
                        .filter(s -> s.getPartialRefundForCancellationPaymentMethod() == null ||
                                s.getPartialRefundForCancellationPaymentMethod().hasTag("deprecated"))
                        .map(ClientRefundSetup::getPaymentRequestPurpose)
                        .collect(Collectors.toList()));
        return partialRefundFilter.or(nonPartialRefundFilter);
    }

    // ACC-2913
    private TasksHolder ExpenseRequestTasks(
            User user, SelectFilter filter, Boolean adminMode, String taskName,
            ObjectNode objectNode, Boolean withContentFlag) throws IOException {

        ProjectionFactory pf = Setup.getProjectionFactory();
        if (filter == null) {
            filter = new SelectFilter();
        }
        Map<String, String> joinType = new HashMap<>();
        List<String> joins = new ArrayList<>();

        processExpenseRequestFilters(user, filter, joins, joinType, taskName);

        Map<String, List<Task>> map = new HashMap<>();
        TasksHolder tasksHolder = new TasksHolder();

        orderSteps(expenseRequestFlow, user, map, objectNode);

        ((adminMode != null && adminMode)
                ? expenseRequestFlow.getAllTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)
                : expenseRequestFlow.getTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)).stream()
                .forEach(e -> map.get(e.getTempTaskName()).add(pf.createProjection(ExpenseRequestFlowProjection.class, e)));
        processTasksHolder(tasksHolder, map, expenseRequestFlow, "Expense Request Flow");
        return tasksHolder;
    }


    private SelectFilter processExpenseRequestFilters(User user, SelectFilter filter, List<String> joins,
                                                      Map<String, String> joinType, String taskName) {

        if (taskName == null || taskName.isEmpty()) {
            return null;
        }

        return filter;
    }


    //Bucket Replenishment
    private TasksHolder BucketReplenishmentFlow(
            User user, SelectFilter filter, Boolean adminMode, String taskName,
            ObjectNode objectNode, Boolean withContentFlag) throws IOException {

        ProjectionFactory pf = Setup.getProjectionFactory();
        if (filter == null) {
            filter = new SelectFilter();
        }
        Map<String, String> joinType = new HashMap<>();
        List<String> joins = new ArrayList<>();

        processBucketReplenishmentFilters(user, filter, joins, joinType, taskName);

        Map<String, List<Task>> map = new HashMap<>();
        TasksHolder tasksHolder = new TasksHolder();

        orderSteps(bucketReplenishmentFlow, user, map, objectNode);

        ((adminMode != null && adminMode)
                ? bucketReplenishmentFlow.getAllTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)
                : bucketReplenishmentFlow.getTasks(user, filter, joins, joinType, taskName, withContentFlag, objectNode)).stream()
                .forEach(e -> map.get(e.getTempTaskName()).add(pf.createProjection(ExpenseRequestFlowProjection.class, e)));
        processTasksHolder(tasksHolder, map, bucketReplenishmentFlow, "Bucket Replenishment Flow");
        return tasksHolder;
    }


    private SelectFilter processBucketReplenishmentFilters(
            User user, SelectFilter filter, List<String> joins,
            Map<String, String> joinType, String taskName) {

        if (taskName == null || taskName.isEmpty()) return null;
        return filter;
    }

    public List<DirectDebitCancelationToDo> DirectDebitCancelationTasksList(
            User user, SelectFilter filter, String taskName) throws IOException {

        if (filter == null) {
            filter = new SelectFilter();
        }
        List<String> joins = new ArrayList<>();
        Map<String, String> joinType = new HashMap<>();
        filter.and("directDebitFile.ddStatus", "in", Arrays.asList(DirectDebitStatus.CONFIRMED, DirectDebitStatus.EXPIRED));
        filter.and("hidden", "=", false);
        return directDebitCancelationToDoFlow.getTasks(user, filter, joins, joinType, taskName, null, null);
    }
}

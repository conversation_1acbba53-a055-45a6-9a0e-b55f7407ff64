package com.magnamedia.workflow.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SearchField;
import com.magnamedia.core.workflow.RoleBasedWorkflow;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.workflow.service.purchasingsteps.*;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <PERSON> (Jan 31, 2021)
 */
@Service
public class PurchasingFlow extends RoleBasedWorkflow<PurchasingToDo> {

    private final ConfirmPurchaseRequestStep confirmPurchaseRequestStep;
    private final ConfirmBestSupplierStep confirmBestSupplierStep;
    private final GetBestSupplierStep getBestSupplierStep;
    private final GetBetterSupplierStep getBetterSupplierStep;
    private final PurchasingStep purchasingStep;
    public static final String flowName = "purchasingFlow";

    public PurchasingFlow(ConfirmPurchaseRequestStep confirmPurchaseRequestStep,
                          ConfirmBestSupplierStep confirmBestSupplierStep,
                          GetBestSupplierStep getBestSupplierStep,
                          GetBetterSupplierStep getBetterSupplierStep,
                          PurchasingStep purchasingStep) {
        this.confirmPurchaseRequestStep = confirmPurchaseRequestStep;
        this.confirmBestSupplierStep = confirmBestSupplierStep;
        this.getBestSupplierStep = getBestSupplierStep;
        this.getBetterSupplierStep = getBetterSupplierStep;
        this.purchasingStep = purchasingStep;

        setId(flowName);

        this.getWorkflowSteps().addAll(
                Arrays.asList(
                        this.confirmPurchaseRequestStep,
                        this.confirmBestSupplierStep,
                        this.getBestSupplierStep,
                        this.getBetterSupplierStep,
                        this.purchasingStep
                ));

        this.getWorkflowSteps().forEach((workflowStep) -> {
            this.idStepMap.put(workflowStep.getId(), workflowStep);
        });

    }

    public List<SearchField> getDefaultSearchableFields() {
        return Arrays.asList(
                new SearchField("creationDate", "Creation Date", "timestamp",
                        Setup.DATE_OPERATIONS),
                new SearchField("lastModificationDate", "Last Update Date", "timestamp",
                        Setup.DATE_OPERATIONS)
        );
    }
}

package com.magnamedia.workflow.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SearchField;
import com.magnamedia.core.workflow.RoleBasedWorkflow;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.workflow.service.clientrefundtodosteps.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on Dec 06, 2020
 * Jirra ACC-2845
 */

@Service(value = "clientRefundFlow")
public class ClientRefundFlow extends RoleBasedWorkflow<ClientRefundToDo> {

    @Autowired
    ClientRefundWaitingManagerApprovalStep clientRefundWaitingManagerApprovalStep;
    @Autowired
    ClientRefundWaitingCooApprovalStep clientRefundWaitingCooApprovalStep;
    @Autowired
    ClientRefundBankTransferCreatedStep clientRefundBankTransferCreatedStep;
    @Autowired
    ClientRefundCreditCardTransferCreatedStep clientRefundCreditCardTransferCreatedStep;
    @Autowired
    ConditionalRefundWaitingPaymentsBeforeManagerApprovalStep conditionalRefundWaitingPaymentsBeforeManagerApprovalStep;
    @Autowired
    ConditionalRefundWaitingPaymentsBeforeCooApprovalStep conditionalRefundWaitingPaymentsBeforeCooApprovalStep;

    public ClientRefundFlow(ClientRefundWaitingManagerApprovalStep clientRefundWaitingManagerApprovalStep,
                            ClientRefundWaitingCooApprovalStep clientRefundWaitingCooApprovalStep,
                            ClientRefundBankTransferCreatedStep clientRefundBankTransferCreatedStep,
                            ClientRefundCreditCardTransferCreatedStep clientRefundCreditCardTransferCreatedStep,
                            ConditionalRefundWaitingPaymentsBeforeManagerApprovalStep conditionalRefundWaitingPaymentsBeforeManagerApprovalStep,
                            ConditionalRefundWaitingPaymentsBeforeCooApprovalStep conditionalRefundWaitingPaymentsBeforeCooApprovalStep) {
        setId("clientRefundFlow");

        this.clientRefundWaitingManagerApprovalStep = clientRefundWaitingManagerApprovalStep;
        this.clientRefundWaitingCooApprovalStep = clientRefundWaitingCooApprovalStep;
        this.clientRefundBankTransferCreatedStep = clientRefundBankTransferCreatedStep;
        this.clientRefundCreditCardTransferCreatedStep = clientRefundCreditCardTransferCreatedStep;
        this.conditionalRefundWaitingPaymentsBeforeManagerApprovalStep = conditionalRefundWaitingPaymentsBeforeManagerApprovalStep;
        this.conditionalRefundWaitingPaymentsBeforeCooApprovalStep = conditionalRefundWaitingPaymentsBeforeCooApprovalStep;

        this.getWorkflowSteps().addAll(Arrays.asList(
                this.clientRefundWaitingManagerApprovalStep,
                this.clientRefundWaitingCooApprovalStep,
                this.clientRefundBankTransferCreatedStep,
                this.clientRefundCreditCardTransferCreatedStep,
                this.conditionalRefundWaitingPaymentsBeforeManagerApprovalStep,
                this.conditionalRefundWaitingPaymentsBeforeCooApprovalStep
        ));

        this.getWorkflowSteps().forEach((workflowStep) -> {
            this.idStepMap.put(workflowStep.getId(), workflowStep);
        });
    }

    @Override
    public List<SearchField> fillSearchFields() {
        List<SearchField> fields = new ArrayList();

        SearchField field1 = new SearchField("client.name",
                "Client Name",
                "string",
                Arrays.asList("=", "Contains", "<>", "IS EMPTY", "IS NOT EMPTY"));
        fields.add(field1);

        SearchField field2 = new SearchField("purpose",
                "Refund Purpose",
                "com.magnamedia.entity.workflow.PaymentRequestPurpose",
                Setup.DEFAULT_OPERATIONS,
                "/accounting/paymentrequestpurposes/getallclientspurposewithsetup");
        fields.add(field2);

        SearchField field3 = new SearchField("amount",
                "Amount",
                "double",
                Arrays.asList("<", ">", "<=", ">=", "="));
        fields.add(field3);

        SearchField field4 = new SearchField("conditionalRefund",
                "Conditional",
                "boolean",
                Setup.DEFAULT_OPERATIONS);
        fields.add(field4);

        SearchField field5 =  new SearchField("methodOfPayment",
                "Refund Method",
                "com.magnamedia.workflow.type.ClientRefundPaymentMethod",
                Setup.DEFAULT_OPERATIONS,
                "/accounting/clientRefundTodo/getRefundMethods");
        fields.add(field5);

        SearchField field6 = new SearchField();
        field6.setLabel("From Date");
        field6.setName("creationDate");
        field6.setDefaultOperation(">=");
        field6.setOperations(Arrays.asList("=", "<>", ">", ">="));
        fields.add(field6);

        SearchField field7 = new SearchField();
        field7.setLabel("To Date");
        field7.setName("toCreationDate");
        field7.setDefaultOperation("<=");
        field7.setOperations(Arrays.asList("=", "<>", "<", "<="));
        fields.add(field7);

        return fields;
    }

    @Override
    public Map<String, String> getTableHeader() {
        Map<String, String> tableHeader = new HashMap();
        tableHeader.put("id", "ID");
        tableHeader.put("detail", "Details");
        tableHeader.put("contract.label", "Contract");
        tableHeader.put("client.label", "Client");
        tableHeader.put("purpose.label", "Refund Purpose");
        tableHeader.put("amount", "Amount (AED)");
        tableHeader.put("numberOfMonthlyPayments", "Number Of Monthly Payments");
        tableHeader.put("complaint.label", "Complaint");
        tableHeader.put("methodOfPayment.label", "Refund Method");
        tableHeader.put("status.label", "Status");
        tableHeader.put("accountName", "Account Name");
        tableHeader.put("iban", "Iban");
        tableHeader.put("eid", "EID");
        tableHeader.put("description", "Description");
        tableHeader.put("notes", "Notes");
        tableHeader.put("requesterUserName", "Requester");
        tableHeader.put("statusChangeDate", "Status Change Date");
        tableHeader.put("creationDate", "Request Date");
        tableHeader.put("proofUploaded", "Proof Uploaded");
        tableHeader.put("attachments", "Attachment");
        tableHeader.put("transferReference", "Transfer Reference Number");
        return tableHeader;
    }

    @Override
    public Map<String, String> getTableColumnTypes() {
        Map<String, String> tableColumnTypes = new HashMap();
        tableColumnTypes.put("id", "Long");
        tableColumnTypes.put("detail", "Long");
        tableColumnTypes.put("contract.label", "String");
        tableColumnTypes.put("client.label", "String");
        tableColumnTypes.put("purpose.label", "String");
        tableColumnTypes.put("amount", "Double");
        tableColumnTypes.put("numberOfMonthlyPayments", "Integer");
        tableColumnTypes.put("complaint.label", "String");
        tableColumnTypes.put("methodOfPayment.label", "String");
        tableColumnTypes.put("status.label", "String");
        tableColumnTypes.put("accountName", "String");
        tableColumnTypes.put("iban", "String");
        tableColumnTypes.put("eid", "String");
        tableColumnTypes.put("description", "String");
        tableColumnTypes.put("notes", "String");
        tableColumnTypes.put("requesterUserName", "String");
        tableColumnTypes.put("statusChangeDate", "Date");
        tableColumnTypes.put("proofUploaded", "Boolean");
        tableColumnTypes.put("attachments", "List");
        tableColumnTypes.put("creationDate", "Date");
        tableColumnTypes.put("transferReference", "String");
        return tableColumnTypes;
    }

    public List<SearchField> getDefaultSearchableFields() {
        return Arrays.asList(
                new SearchField("creationDate", "Creation Date", "timestamp",
                        Setup.DATE_OPERATIONS),
                new SearchField("lastModificationDate", "Last Update Date", "timestamp",
                        Setup.DATE_OPERATIONS)
        );
    }

    @Override
    public String getWorkflowHeader() {
        return "Client Refund Flow";
    }
}

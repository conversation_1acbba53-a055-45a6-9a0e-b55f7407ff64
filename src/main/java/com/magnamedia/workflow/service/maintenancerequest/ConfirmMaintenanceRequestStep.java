package com.magnamedia.workflow.service.maintenancerequest;

import com.magnamedia.controller.ExpenseRequestTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.MaintenanceRequest;
import com.magnamedia.entity.StockKeeperToDo;
import com.magnamedia.entity.Supplier;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.module.type.ExpenseRequestType;
import com.magnamedia.module.type.MaintenanceRequestStatus;
import com.magnamedia.module.type.MaintenanceRequestType;
import com.magnamedia.repository.*;
import com.magnamedia.service.CurrencyExchangeSevice;
import com.magnamedia.service.ExpenseNotificationService;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import com.magnamedia.workflow.type.ExpenseRequestTodoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <PERSON> (Feb 07, 2021)
 */
@Service
public class ConfirmMaintenanceRequestStep extends MaintenanceFlowAbstractStep<MaintenanceRequest> {
    public ConfirmMaintenanceRequestStep() {
        this.setId(MaintenanceRequestType.PURCHASE_AUDITOR_APPROVE_REQUEST.toString());
    }

    @Autowired
    SupplierRepository supplierRepository;
    @Autowired
    ExpenseRequestTodoRepository expenseRequestTodoRepository;
    @Autowired
    ExpenseRequestTodoController expenseRequestTodoController;
    @Autowired
    MaintenanceRequestRepository maintenanceRequestRepository;
    @Autowired
    CurrencyExchangeSevice currencyExchangeSevice;

    @Autowired
    private ExpenseNotificationService expenseNotificationService;

    @Override
    public void onDone(MaintenanceRequest entity) {
        if (entity.getAuditorDecision() == null)
            return;

        switch (entity.getAuditorDecision()) {
            case APPROVED:
                approveRequest(entity);
                break;
            case REJECTED:
                rejectMaintenanceRequest(entity);
                break;
            case SENT_GET_BETTER_PRICE:
                entity.setStatus(MaintenanceRequestStatus.PENDING);
                addNewTask(entity, MaintenanceRequestType.PURCHASE_MANAGER_GET_BETTER_PRICE.toString());
                expenseNotificationService.expenseToDoCreatedEmail(entity.getEntityType(), entity.getTaskName());
                if(entity.getSendToGetBetterPriceNote()!= null && !entity.getSendToGetBetterPriceNote().isEmpty())
                    entity.addMaintenanceRequestNote(entity.getSendToGetBetterPriceNote());
                break;
        }
        super.onDone(entity);
    }

    private void rejectMaintenanceRequest(MaintenanceRequest entity) {
        if (entity.getExpenseRequestTodo() != null) {
            ExpenseRequestTodo request = entity.getExpenseRequestTodo();
            request.setStopped(true);
            request.setCompleted(true);
            request.setStatus(ExpenseRequestStatus.REJECTED);
            expenseRequestTodoRepository.save(request);
        }
        entity.setStatus(MaintenanceRequestStatus.REJECTED);
    }


    public boolean limitExceeded(Expense expense, BigDecimal amount) {
        if (expense.getLimitForApproval() == null) return false;
        return amount.doubleValue() > expense.getLimitForApproval();
    }

    private void approveRequest(MaintenanceRequest entity) {
        updateExpenseRequest(entity);
        ExpenseRequestTodo expenseRequest = entity.getExpenseRequestTodo();

        if (!expenseRequest.getTaskName().equals(ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString())) {
            entity.setStatus(MaintenanceRequestStatus.APPROVED);
            entity.setCompleted(Boolean.TRUE);
            createStockKeeperToDo(entity);
        }
    }

    private void updateExpenseRequest(MaintenanceRequest entity) {
        ExpenseRequestTodo request = entity.getExpenseRequestTodo();
        if (request == null) {
            request = new ExpenseRequestTodo();
            request.setRequestedBy(entity.getCreator());
            request.setCooRequestType("Approve Maintenance Request for " + entity.getDescription());
            request.setExpenseRequestType(ExpenseRequestType.MAINTENANCE);
            request.setExpense(Setup.getRepository(ExpenseRepository.class).findOne(entity.getExpense().getId()));
            request.setStatus(ExpenseRequestStatus.PENDING);
            entity.setExpenseRequestTodo(request);
            if (entity.getCost() != null)
                request.setAmount(entity.getCost().doubleValue());
            request.setCurrency(currencyExchangeSevice.getLocalCurrency());
            if (entity.getVatAmount() != null)
                request.setVatAmount(entity.getVatAmount().doubleValue());
            Supplier supplier = supplierRepository.findOne(entity.getSupplier().getId());
            request.setBeneficiaryDetailsFromSupplier(supplier);

            expenseRequestTodoController.createEntity(request);
        } else {
            request.setAmount(entity.getCost().doubleValue());
            request.setVatAmount(entity.getVatAmount() != null ? entity.getVatAmount().doubleValue() : null);
            Supplier supplier = supplierRepository.findOne(entity.getSupplier().getId());
            request.setBeneficiaryDetailsFromSupplier(supplier);

            request.moveToCooApprovalStep(false);
            expenseRequestTodoRepository.save(request);
        }

    }

    public void cooRejectedRequest(ExpenseRequestTodo entity) {

        MaintenanceRequest maintenanceRequest = maintenanceRequestRepository.findTop1ByExpenseRequestTodoOrderByCreationDateDesc(entity);
        maintenanceRequest.setStatus(MaintenanceRequestStatus.REJECTED);
        maintenanceRequestRepository.save(maintenanceRequest);
    }

    public void cooApprovedRequest(ExpenseRequestTodo entity) {
        MaintenanceRequest maintenanceRequest = maintenanceRequestRepository.findTop1ByExpenseRequestTodoOrderByCreationDateDesc(entity);
        maintenanceRequest.setStatus(MaintenanceRequestStatus.APPROVED);
        entity.setCompleted(Boolean.TRUE);
        maintenanceRequestRepository.save(maintenanceRequest);

        createStockKeeperToDo(maintenanceRequest);
    }

    private void createStockKeeperToDo(MaintenanceRequest maintenanceRequest) {
        StockKeeperToDo stockKeeperToDo = new StockKeeperToDo();
        stockKeeperToDo.setToDoType(StockKeeperToDo.StockKeeperToDoType.MAINTENANCE);
        stockKeeperToDo.setMaintenanceRequest(maintenanceRequest);
        stockKeeperToDo.setExpenseRequestTodo(maintenanceRequest.getExpenseRequestTodo());
        stockKeeperToDo.setName("Maintenance Request " + maintenanceRequest.getDescription());
        Setup.getRepository(StockKeeperToDoRepository.class).save(stockKeeperToDo);
    }

    public void cooGetBetterPriceRequest(ExpenseRequestTodo entity) {
        MaintenanceRequest maintenanceRequest = maintenanceRequestRepository.findTop1ByExpenseRequestTodoOrderByCreationDateDesc(entity);
        maintenanceRequest.setSendToGetBetterPriceNote(entity.getSendToGetBetterPriceNote());
        maintenanceRequest.setStatus(MaintenanceRequestStatus.PENDING);
        
        addNewTask(maintenanceRequest, MaintenanceRequestType.PURCHASE_MANAGER_GET_BETTER_PRICE.toString());
        
        expenseNotificationService.expenseToDoCreatedEmail(
                maintenanceRequest.getEntityType(), maintenanceRequest.getTaskName());
        
        maintenanceRequestRepository.save(maintenanceRequest);
    }
}

package com.magnamedia.workflow.service.expenserequesttodosteps;


import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.workflow.service.ExpenseRequestManualStep;
import com.magnamedia.workflow.type.ExpenseRequestTodoType;
import org.springframework.stereotype.Service;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * Created on Jan 18, 2021
 * Jirra ACC-2913
 */

@Service
public class ExpenseRequestPaymentObjectCreatedStep extends ExpenseRequestManualStep<ExpenseRequestTodo> {


    private static final Logger logger =
            Logger.getLogger(ExpenseRequestPaymentObjectCreatedStep.class.getName());


    public ExpenseRequestPaymentObjectCreatedStep() {
        this.setId(ExpenseRequestTodoType.PAYMENT_OBJECT_CREATED.toString());
    }

    @Override
    public void onSave(ExpenseRequestTodo entity) {
    }

    @Override
    public void postSave(ExpenseRequestTodo entity) {
    }

    @Override
    public void onDone(ExpenseRequestTodo entity) {

        logger.log(Level.INFO, "Expense Request Payment Created Step Started");

    }

    @Override
    public void postDone(ExpenseRequestTodo entity) {
    }
}

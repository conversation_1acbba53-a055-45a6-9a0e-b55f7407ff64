package com.magnamedia.workflow.service.expenserequesttodosteps;


import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.workflow.service.ExpenseRequestManualStep;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import com.magnamedia.workflow.type.ExpenseRequestTodoFlowActions;
import com.magnamedia.workflow.type.ExpenseRequestTodoType;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *         Created on Jan 18, 2021
 *         Jirra ACC-2913
 */

@Service
public class ExpenseRequestWaitingManagerApprovalStep extends ExpenseRequestManualStep<ExpenseRequestTodo> {


    private static final Logger logger =
            Logger.getLogger(ExpenseRequestWaitingManagerApprovalStep.class.getName());


    public ExpenseRequestWaitingManagerApprovalStep() {
        this.setId(ExpenseRequestTodoType.WAITING_MANAGER_APPROVAL.toString());
    }

    @Override
    public void onSave(ExpenseRequestTodo entity) {
    }

    @Override
    public void postSave(ExpenseRequestTodo entity) {
    }

    @Override
    public void onDone(ExpenseRequestTodo entity) {

        logger.log(Level.INFO, "Expense Request Manager Approval Step Started");

        if (entity.getManagerAction() != null) {

            if (entity.getManagerAction().equals(ExpenseRequestTodoFlowActions.APPROVE)) {
                
                if (entity.getApprovedFromEmail() != null && entity.getApprovedFromEmail())
                    entity.addApproval(entity.getApproveHolderEmail());
                else
                    entity.addApproval(CurrentRequest.getUser().getUsername());

                Expense relatedExpense = entity.getExpense();

                if (relatedExpense.cooLimitExceeded(entity.getAmountInLocalCurrency())) {
                    addNewTask(entity, ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString());
                } else {

                    entity.setStatus(ExpenseRequestStatus.PENDING_PAYMENT);
                    if (entity.getPaymentMethod() == null || !entity.getPaymentMethod().equals(ExpensePaymentMethod.CHEQUE) || !entity.getPaymentMethod().equals(ExpensePaymentMethod.INVOICED)) {
                        addNewTask(entity, ExpenseRequestTodoType.PAYMENT_OBJECT_CREATED.toString());
                    }
                }

            } else if (entity.getManagerAction().equals(ExpenseRequestTodoFlowActions.REJECT)) {
                entity.setStopped(true);
                entity.setCompleted(true);
                entity.setStatus(ExpenseRequestStatus.REJECTED);

            } else {
                throw new RuntimeException("Manager Action has unexpected value");
            }
        } else {
            throw new RuntimeException("Manager Action can't be null");
        }

        super.onDone(entity);
        // no need for save since complete task method in workflow controller do the save


    }

    @Override
    public void postDone(ExpenseRequestTodo entity) {
    }

    @Override
    public List<String> getTaskHeader() {
        return new ArrayList<>();
    }


}
